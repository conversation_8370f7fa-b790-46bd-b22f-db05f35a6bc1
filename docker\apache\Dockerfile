FROM almalinux:8.10

# Install EPEL and Remi repositories for PHP 7.4
RUN dnf install -y epel-release && \
    dnf install -y https://rpms.remirepo.net/enterprise/remi-release-8.rpm && \
    dnf module reset php -y && \
    dnf module enable php:remi-7.4 -y

# Install Apache 2.4.63, PHP 7.4 and required extensions
RUN dnf install -y \
    httpd \
    php \
    php-cli \
    php-common \
    php-mysqlnd \
    php-pdo \
    php-mbstring \
    php-xml \
    php-json \
    php-curl \
    && dnf clean all

# Configure Apache to use prefork MPM (required for PHP module)
RUN echo "LoadModule mpm_prefork_module modules/mod_mpm_prefork.so" > /etc/httpd/conf.modules.d/00-mpm.conf

# Configure PHP with Apache
RUN echo "LoadModule php_module modules/libphp.so" > /etc/httpd/conf.modules.d/10-php.conf
RUN echo "AddType application/x-httpd-php .php" >> /etc/httpd/conf.modules.d/10-php.conf
RUN echo "DirectoryIndex index.php index.html" >> /etc/httpd/conf.modules.d/10-php.conf

# Enable Apache modules
RUN sed -i 's/#LoadModule rewrite_module/LoadModule rewrite_module/' /etc/httpd/conf.modules.d/00-base.conf

# Copy Apache config
COPY docker/apache/000-default.conf /etc/httpd/conf.d/000-default.conf

# Create Apache run directory
RUN mkdir -p /run/httpd

# Set proper permissions for Apache on AlmaLinux
RUN chown -R apache:apache /var/www/html && \
    chmod -R 755 /var/www/html

# Set document root
WORKDIR /var/www/html

EXPOSE 80

# Start Apache in foreground
CMD ["httpd", "-D", "FOREGROUND"]
<?php  if ( ! defined('BASEPATH')) exit('No direct script access allowed');



$config['protocol'] = 'smtp';

//$config['smtp_host'] = 'ssl://smtp.googlemail.com';

//$config['smtp_port'] = '465';

//$config['smtp_user'] = '<EMAIL>';

//$config['smtp_pass'] = 'qwe1iop2';



// $config['smtp_host'] = '**************';    //**************  **************

// $config['smtp_port'] = '25';

// $config['smtp_user'] = '<EMAIL>';

// $config['smtp_pass'] = 'jDe_8888';



// $config['smtp_host'] = '**************';

// $config['smtp_port'] = '25';

// $config['smtp_user'] = '';

// $config['smtp_pass'] = '';



// $config['smtp_host'] = 'smtp-relay.sendinblue.com';

// $config['smtp_port'] = '587';

// $config['smtp_user'] = '<EMAIL>';

// $config['smtp_pass'] = 'tmva8WN5HVbFU6L1';



// $config['smtp_host'] = 'smtp.ems-email.com';

// $config['smtp_port'] = '2525';

// $config['smtp_user'] = 'citem.email';

// $config['smtp_pass'] = '0s0Em@1Lsa0uf';



// $config['smtp_host'] = 'smtp.pepipost.com';

// $config['smtp_port'] = '587';

// $config['smtp_user'] = 'citempepi';

// $config['smtp_pass'] = 'M1s#87878';



// $config['smtp_host'] = 'smtp.sendgrid.net';

// $config['smtp_port'] = '587';

// $config['smtp_user'] = 'smdd';

// $config['smtp_pass'] = 'M1s#87878';



//$config['smtp_host'] = 'smtp.mandrillapp.com';

//$config['smtp_port'] = '587';

//$config['smtp_user'] = '<EMAIL>';

//$config['smtp_pass'] = 'StwFvjkqJEHZzhv9NdJBrg';



//$config['smtp_host'] = 'smtp.mandrillapp.com';

//$config['smtp_port'] = '587';

//$config['smtp_user'] = '<EMAIL>';

//$config['smtp_pass'] = 'RnFr2MXWFOmIDaOfPJW1FA';  // V6WiI8h0JD8a2q3ZPDrVtQ


$config['smtp_host'] = '**************';
$config['smtp_port'] = '587';
$config['smtp_user'] = '<EMAIL>';
$config['smtp_pass'] = '$Ix@$Ca2kHp&h!ilt@8L7fiPXVYA40$af%5zf^wYAW7QhbqN!%w$2kDZOG4H';


$config['mailtype'] = 'html';

$config['charset'] = 'utf-8';

$config['wordwrap'] = FALSE;




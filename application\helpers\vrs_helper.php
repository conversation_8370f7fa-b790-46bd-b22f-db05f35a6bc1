<?php
function sessionRights($set_data){
	
		if (isset($set_data['sessionData']) && isset($set_data['sessionData']['realname'])){

		    $data['read_set_value'] = $set_data['sessionData']['realname'];
		    
		    $data['useremail'] = $set_data['sessionData']['uemail'];
			$data['sessionRights'] = $set_data['sessionData']['urights'];
			$data['username'] = $set_data['sessionData']['user'];
			$data['loginuser'] = $set_data['sessionData']['loginuser'];	
			$data['fairdesc'] = $set_data['sessionData']['fdesc'];		
			$data['myVenue'] = $set_data['sessionData']['venue'];
			$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			$data['fcode'] = $set_data['sessionData']['fcode'];
			$data['diyTerminal'] = $set_data['sessionData']['terminalNo'];
			
			$data['sysName'] = $set_data['sessionData']['sysName'];			
			$data['systitle'] = $set_data['sessionData']['systitle'];
			$data['eventDB'] = $set_data['sessionData']['eventDB'];
			$data['sector'] = $set_data['sessionData']['sector'];
			$data['sectorName'] = $set_data['sessionData']['sectorName'];	

			$data['eventProj'] = $set_data['sessionData']['eventProj'];
			$data['genericFcode'] = $set_data['sessionData']['genericFcode'];
			$data['sectorProjects'] = $set_data['sessionData']['sectorProjects'];
			$data['selectedSector'] = $set_data['sessionData']['selectedSector'];

			$data['getDay'] = $set_data['sessionData']['getDay'];

			$data['default_form_type'] = $set_data['sessionData']['default_form_type'];

			$data['controller_CI'] = $set_data['sessionData']['controller_CI'];					

			$CI = get_instance();

			$CI->load->Model('master_model');

			$data['userAccessRef']= $CI->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");	
			$data['userAccessMod']= $CI->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");	
			//$data['colDataTable'] = $CI->master_model->loadRec("v_reference","where switch ='VCOL' and sector like '%".$data['sector']."%' and exclude=0 order by sortfield");
			$data['colDataTable'] = $CI->master_model->loadRec("v_reference","where switch ='VCOL' and exclude=0 order by sortfield");
			//===========================================
			//======== get RIGHTS procedure =============
			//===========================================

			foreach ($data['userAccessRef'] as $r1)
            {
              
              $founds=0;                
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {
               
               $xxx = $set_data['sessionData'][$x."r"];               
               																			// Using ${} is a way to create dynamic variables, ex.====> ${'vrs_'.$r1['c_code']} = "";
               if ($r1['c_code']==$xxx) { $data['vrs_'.$r1['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r1['c_code']] = ""; }
              //echo $data['vrs_'.$r1['c_code']]."<br>";

            }
            foreach ($data['userAccessMod'] as $r2)
            {
              
              $founds=0;                
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {
               
               $xxx = $set_data['sessionData'][$x."r"];               
               // Using ${} is a way to create dynamic variables,
               if ($r2['c_code']==$xxx) { $data['vrs_'.$r2['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r2['c_code']] = ""; }
              //echo $data['vrs_'.$r2['c_code']]."<br>";

            }

            if($data['colDataTable']<>"") {
	            foreach ($data['colDataTable'] as $r3)
	            {
	              
	              $founds=0;                
	              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {
	               
	               $xxx = $set_data['sessionData'][$x."r"];               
	               // Using ${} is a way to create dynamic variables,
	               if ($r3['c_code']==$xxx) { $data['vrs_'.$r3['c_code']] = $xxx; $founds=1; }
	               // ================================================================
	              }
	              if($founds==0) { $data['vrs_'.$r3['c_code']] = ""; }
	              //echo $data['vrs_'.$r2['c_code']]."<br>";

	            }
        	}

			return $data;

		}else{
			redirect('vrs/index');
		}


						
	}


function getCheckbox($inputType,$showLabel,$vdata1,$vdata2,$frmName,$vtitle,$otherRem,$fcodeFrmData2) {

   if($fcodeFrmData2=="ra10173") {$vresult="<div class='row'> <div class='col-md-12'>";}
   elseif($fcodeFrmData2=="nopanel") {$vresult ="<p><div class='row'> <div class='col-md-12'><label>".$vtitle."<span class='text-danger'><small> </small></span></label><br>";}
   else {
    $vresult= '<div class="panel panel-default">
			   <div class="panel-heading"><span class="text-danger">'.(($fcodeFrmData2=="" || $fcodeFrmData2=="chkShowHide") ? " " : "").'</span><strong> '.$vtitle.'</strong>
			   <div class="pull-right"><span class="text-danger"><small>'.(($fcodeFrmData2=="" || $fcodeFrmData2=="chkShowHide") ? "* required" : "").'</small></span></div></div>
               <div class="panel-body">
			   <div class="row"> <div class="col-lg-12 col-sm-12">';
	}		  

	$remarks="";
    $arrlength=0;
	$ctr=0;
	$ctrTxt=0; //counter for multiple textbox in P_SWITCH;
	
    if($otherRem=='other_Product') {$refCODE='prod_cat'; $refREMARKS='remarks_major';}   //determine reference between v_genproducts & other tables
    else {$refCODE='item_code'; $refREMARKS='remarks';}	          						 //determine reference between v_genproducts & other tables

	if($fcodeFrmData2<>"" && $fcodeFrmData2<>"org" && $fcodeFrmData2<>"ra10173" && $fcodeFrmData2<>"nopanel" && $fcodeFrmData2<>"notrequired" && $fcodeFrmData2<>"radio-inline" && $fcodeFrmData2<>"chkShowHide") 
	 {
	    foreach($fcodeFrmData2 as $temp0)
		 { 
		 	$getFcode=$temp0['fair_code'];
	 	 }
     }
    // die("aaa=".$getFcode);
	//settype($otherRem, "array");
	
   if (isset($vdata2) && $vdata2<>"")
	 {
	    	  
		foreach($vdata2 as $temp1)  
		 { 
		   $chkFcode = $temp1['fair_code'];

		   	if(isset($getFcode))	
		   	 {
		   	  if($chkFcode==$getFcode)
		   	  	{
		         $temp[]    = $temp1[$refCODE];     //  item_code or prod_cat        field
			     $vremarks[] = $temp1[$refREMARKS]; //  remarks   or remarks_major   field
			    } 
		   	 }
		    else
			 {
		      $temp[]    = $temp1[$refCODE]; 
			  $vremarks[] = $temp1[$refREMARKS];
			 } 
		 }   //print_r($temp);
		$arrlength=count($temp); // get array lenght
	 }
	
   if(isset($vdata1))
	 {
	  foreach($vdata1 as $r3)  
	  {
	  	$p_switch = (isset($r3['p_switch']) ? $r3['p_switch'] : "" );
	    $remarkx='';
	    $vtxtbox='';
		$chk='';					   
		for ($x=0; $x<$arrlength; $x++)
		 {					    
			  if (isset($temp[$x]) && $temp[$x]==$r3['c_code']) 
			   {
			    $chk=TRUE;
				if($p_switch=='textbox' || $p_switch=='textarea') 
				  {
				     $remarkx=$vremarks[$x];
				  }
			   } //echo "<br>aaa=".$temp[$x];
			   //$remarks[$x]=$vremarks[$x];
		 } //die("aaa=".print_r($vremarks));

		$vtxtbox = array (
			   'class' => '',
			   'id'      => $otherRem.$ctr,
 			   'name'    => $otherRem.$ctr,
			   'value'   => $remarkx,
			   'maxlength' => '40',
			   'size' => '15'		   
			   );
			   
		$vtxtarea = array (
			   'id'      => $otherRem.$ctr,
 			   'name'    => $otherRem.$ctr,
			   'value'   => $remarkx,
			   'rows'    => '1',
			   'class' => 'form-control'
			   );	   

				       //=== the # symbol used to concat itemcode and c_profile ==============		

		if ($inputType=='checkbox')
		 {		   

		   if (isset($vdata2)) {$vcheck= $chk;} else {$vcheck= set_checkbox($frmName, $r3['c_code']."#".$r3['c_profile']."#".$p_switch."#".$ctr,$chk);}	
	       $cbox_repre = array (
			'id'      => $frmName,
			'name'    => $frmName,
			'value'   => $r3['c_code']."#".$r3['c_profile']."#".$p_switch."#".$ctr,  
			'checked' => $vcheck
		    ); // set_checkbox($frmName, $r3['c_code']."#".$r3['c_profile']."#".$r3['p_switch']."#".$ctr,$chk) 
		 } 
		if ($inputType=='radio')
		 {
		   $chkShowHide="";
		   $valueArr=$r3['c_code']."#".$r3['c_profile']."#".$r3['p_switch']."#".$ctr;

		   if($fcodeFrmData2=="chkShowHide") 
		   	{ 
		   		if($r3['c_code']=="Yes") {$chkShowHide="showMe('show-hide')";} else {$chkShowHide="hideMe('show-hide')";}
		   		//$chkShowHide="chkShowHide()"; 
		   		//$valueArr=$r3['c_code'];
			}
		   	 	
		   if (isset($vdata2)) {$vcheck= $chk;} else {$vcheck= set_radio($frmName, $r3['c_code']."#".$r3['c_profile']."#".$r3['p_switch']."#".$ctr,$chk);}		 
	       $cbox_repre = array (
			'id'      => $frmName,
			'name'    => $frmName,
			'value'   => $valueArr,  
			'checked' => $vcheck,
			'onclick' => $chkShowHide
		    ); //set_radio($frmName, $r3['c_code']."#".$r3['c_profile']."#".$r3['p_switch']."#".$ctr,$chk)
		 }
		   //}
 		 		   		   
      	//if($ctr==9 || $ctr==18 || $ctr==27) {$vresult = $vresult. "</td><td>";}	
      	//if($ctr==7 || $ctr==14 || $ctr==21) {$vresult = $vresult. "</td><td>";}	
	  	//if($ctr==5 || $ctr==10 || $ctr==15 || $ctr==20 || $ctr==25) {$vresult = $vresult. "</td><td>";}

	    if($ctr==8 || $ctr==16 || $ctr==24) {$vresult = $vresult. '</div><div class="col-lg-12 col-sm-12">';}	      		 //($ctr==6 || $ctr==12 || $ctr==18 || $ctr==24) 
 		 
		if ($inputType=='checkbox')
		 {

		  if($fcodeFrmData2=="ra10173") 
		  	{ 
		  		//$textoption = "<strong>".$vtitle."</strong>";
		  		$textoption = "".$vtitle."";
		  		$vlabel1 = "<div class='checkbox'><label class='checkbox-inline'>";
		  		$vlabel2 = "</label></div>";
		  	}	  
		  else 
		  	{
		  		$textoption = $r3['c_profile'];
		  		$vlabel1 = "<label class='checkbox-inline'>";
		  		$vlabel2 = "</label>";
		  	}

		  $vresult =$vresult.$vlabel1. form_checkbox($cbox_repre)." ".$textoption.$vlabel2; //." ".$vtxtbox."<br>"; 

		  //die("aaa= ".$result);

		  if($p_switch<>"") {

			  if($r3['p_switch']=='textbox')
			  	{
			  		$vresult =$vresult."&nbsp&nbsp".form_input($vtxtbox)."<br>";

			  	}

			  elseif($r3['p_switch']=='textarea'){$vresult =$vresult. form_textarea($vtxtarea)."<br>";}			  
			  else {$vresult =$vresult."<br>";}
		   } 
		  else {
		  	  $vresult =$vresult."<br>";	
		  }


		 }
		if ($inputType=='radio')  
		 {

		  if($fcodeFrmData2=="radio-inline") {$remove_br="";} else {$remove_br="<br>";}

		  $vlabel1 = "<label class='radio-inline'>";
		  $vlabel2 = "</label>";	  

		  $vshow = ($showLabel=='showlabel') ? " ".$r3['c_profile'] : "";
		  $vresult =$vresult.$vlabel1. form_radio($cbox_repre). $vshow. " ".$vlabel2; //.$vtxtbox."<br>"; 
		  if($r3['p_switch']=='textbox'){$vresult = $vresult."&nbsp&nbsp".form_input($vtxtbox)."<br>";}
		  elseif($r3['p_switch']=='textarea'){$vresult =$vresult. form_textarea($vtxtarea)."<br>";}		  		  
		  else {$vresult =$vresult.$remove_br;}
		 }
		$ctr++;
	  } //end foreach $vdata1
	 } // end isset($vdata1) 
	 
	 if($fcodeFrmData2=="org" || $fcodeFrmData2=="ra10173" || $fcodeFrmData2=="nopanel") { $vresult = $vresult.'</div></div></p>'; }
	 else {$vresult = $vresult.'</div></div></div></div>'; }

  return $vresult;
}

// ======== populate checkbox in users ===================================================================
function getCheckboxOLD($inputType,$showLabel,$vdata1,$vdata2,$frmName,$vtitle,$otherRem,$showSpecific) {
  $vresult=$vtitle; 
  //$vresult = $vresult.='<table width="100%" border="0" class="table"><tr valign="top" style="vertical-align:top;"><td valign="top" style="vertical-align:top;">';
  $vresult = $vresult.='<table width="100%" border="0" class="table table-bordered table-hover"><tr valign="top" style="vertical-align:top;"><td valign="top" style="vertical-align:top;">';


  $remarks="";
  $arrlength=0;
  $ctr=0;
  $ctrTxt=0; //counter for multiple textbox in P_SWITCH;
  
  //settype($otherRem, "array");
  
    if (isset($vdata2))
    {
    foreach($vdata2 as $temp1)  
     { 
       if ($showSpecific=='1')  // genproducts table used
       {
        $temp[]    = $temp1['prod_cat'];
        $pcod[]    = $temp1['prod_code'];
        $vremarks[] = $temp1['remarks_major'];
        $vremMinor[] = $temp1['remarks_minor'];     
       }
       else                     // other sub table  
       {
        $temp[]    = $temp1['item_code']; 
      $vremarks[] = $temp1['remarks'];
       }
     }   //print_r($temp); die();
    $arrlength=count($temp); // get array lenght
    }
  
  if(isset($vdata1))
   {
	   foreach($vdata1 as $r3)  
	    {
	      $remarkx='';
	      $vtxtbox='';
	      $chk='';             
		    for ($x=0; $x<$arrlength; $x++)
		     {              
		        if (isset($temp[$x]) && $temp[$x]==$r3['c_code']) 
		         {
		          $chk=TRUE; //echo "sss"; die();
		        if($r3['p_switch']=='textbox' || $r3['p_switch']=='textarea') 
		          {
		             $remarkx=$vremarks[$x];
		          }
		         } //echo "<br>aaa=".$temp[$x]."---".$chk; die();
		         //$remarks[$x]=$vremarks[$x];
		     } //die("aaa=".print_r($vremarks));

		    $vtxtbox = array (
		         'id'      => $otherRem.$ctr,
		         'name'    => $otherRem.$ctr,
		         'value'   => $remarkx,
		         'maxlength' => '40',
		         'size' => '15'
		         );
		         
		    $vtxtarea = array (
		         'id'      => $otherRem.$ctr,
		         'name'    => $otherRem.$ctr,
		         'value'   => $remarkx,
		         'rows'    => '1',
		         'class' => 'form-control'
		         );    

	               //=== the # symbol used to concat itemcode and c_profile ==============
		    if ($inputType=='checkbox')
		     { //$chk=TRUE;  //echo "xxxxxxx";
		         $cbox_repre = array (
		      'id'      => $frmName,
		      'name'    => $frmName,
		      'value'   => $r3['c_code']."#".$r3['c_profile']."#".$r3['p_switch']."#".$ctr,  
		      'checked' => $chk
		        );
		     }
		    if ($inputType=='radio')
		     {
		         $cbox_repre = array (
		      'id'      => $frmName,
		      'name'    => $frmName,
		      'value'   => $r3['c_code']."#".$r3['c_profile']."#".$r3['p_switch']."#".$ctr,  
		      'checked' => $chk
		        );
		     }
	                 
	      //if($ctr==9 || $ctr==18 || $ctr==27) {$vresult = $vresult. "</td><td>";} 
		    if($ctr==7 || $ctr==14 || $ctr==21) {$vresult = $vresult. "</td><td>";}        
		     
		    if ($inputType=='checkbox')
		     {  
		      $vresult =$vresult. form_checkbox($cbox_repre)." ".$r3['c_profile']; //." ".$vtxtbox."<br>"; 
		      if($r3['p_switch']=='textbox'){$vresult =$vresult." ".form_input($vtxtbox)."<br>";}
		      elseif($r3['p_switch']=='textarea'){$vresult =$vresult. form_textarea($vtxtarea)."<br>";}       
		      else {$vresult =$vresult."<br>";}
		     }
		    if ($inputType=='radio')  
		     {  
		      $vshow = ($showLabel=='showlabel') ? " ".$r3['c_profile'] : "";
		      $vresult =$vresult. form_radio($cbox_repre). $vshow. " "; //.$vtxtbox."<br>"; 
		      if($r3['p_switch']=='textbox'){$vresult =$vresult. form_input($vtxtbox)."<br>";}
		      elseif($r3['p_switch']=='textarea'){$vresult =$vresult. form_textarea($vtxtarea)."<br>";}           
		      else {$vresult =$vresult."<br>";}
		     }
		        //print_r($cbox_repre); die();
		    $ctr++; //die();
	    } //end foreach $vdata1
   } // end isset($vdata1) 
    
        
  	$vresult = $vresult."</td></tr></table>"; 

  return $vresult;
  }


//============================================================================
//ERIC START
    function shortenNumber($n){
        if ($n == 0.00) {
            return '0';
        }else{
            $formatted = sprintf('%.3f', $n / 1000000);
            // return rtrim($formatted, '0.').'M';
            return rtrim(rtrim($formatted, '0'), '.').'M';
        }
    }
//ERIC STOP   
//============================================================================
	function remove_unallowed_fields($raw_data,$allowed_field){
		$new_data = array_filter($raw_data,function($key)use($allowed_field){
			return in_array($key,array_values($allowed_field));
		},ARRAY_FILTER_USE_KEY);
		return $new_data;
	}
//============================================================================
	function getItemFromUsers($row,$dataArr,$field){
	            $key = array_search($row, array_column($dataArr, 'id'));
	            return ($key===false)? '':$dataArr[$key][$field];
	        }
//============================================================================
//E5
if(!function_exists('checkUserPermission')){
	function checkUserPermission($view){
		$permission = fn($view)=>strpos(json_encode($_SESSION['sessionData']),$view)?TRUE:FALSE;
		return $permission($view); 
	}
}
//============================================================================

/**
 * Dump helper. Functions to dump variables to the screen, in a nicley formatted manner.
 * <AUTHOR> van Veen
 * @version 1.0
 */
if (!function_exists('dump')) {
    function dump ($var, $label = 'Dump', $echo = TRUE)
    {
        // Store dump in variable 
        ob_start();
        var_dump($var);
        $output = ob_get_clean();
        
        // Add formatting
        $output = preg_replace("/\]\=\>\n(\s+)/m", "] => ", $output);
        $output = '<pre style="background: #FFFEEF; color: #000; border: 1px dotted #000; padding: 10px; margin: 10px 0; text-align: left;">' . $label . ' => ' . $output . '</pre>';
        
        // Output
        if ($echo == TRUE) {
            echo $output;
        }
        else {
            return $output;
        }
    }
}
if (!function_exists('dump_exit')) {
    function dump_exit($var, $label = 'Dump', $echo = TRUE) {
        dump ($var, $label, $echo);
        exit;
    }
}

/**
 * Dump helper. Functions to dump variables to the screen, in a nicley formatted manner.
 * <AUTHOR> van Veen
 * @version 1.0
 */

//============================================================================
function diagnostics($var){
	if(ENVIRONMENT=='development'){
		dump($var);
	}
}
//============================================================================




















//============================================================================
?>
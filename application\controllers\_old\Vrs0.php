<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

// IMPORTANT -- if field type is TEXT not VARCHAR
// ------------ in insertFormTable() field name must be initialzed to " " first ('brand_associated' => " ")
// ------------ so updating of x_researcher_csv table in updateFormTable() would work -----

// --------- session disables in --> sess_expiration -> config.php

//http://*************/ci/vrs?fcode=mfio2016

// Using ${} is a way to create dynamic variables

// field : validation_status    ==  validated
// value : APPROVED TRADE       ==  3
//         DISAPPROVED TRADE    ==  2
//         INCOMPLETE TRADE     ==  0
//         PENDING TRADE        ==  0

class vrs extends CI_Controller
{

     public function __construct()
     {
          parent::__construct();
		  ini_set('memory_limit','256M');
          $this->load->library('session');
          $this->load->helper('form');
          $this->load->helper('url');
          $this->load->helper('html');
          $this->load->helper('array');  // used by element() in function updateSurvey
          //$this->load->database();
          $this->load->library('form_validation');
		  $this->load->library('pagination');
		  //$this->load->library('encrypt2');
		  $this->load->library('encryption');
          //load the login model
          $this->load->model('vrs_model');
          $this->load->model('vrs_read_model');
          $this->load->model('master_model');

     }

     public function index()
     {
	    //if(!isset($_GET['fcode'])) {die("session expired...! Select Home button of Browser to Login");}    //redirect('http://www.manilafame.com/event?fcode=mfio2016');

        redirect('vrs/login?fcode='); 		// .FAIR_CODE

     }


     public function fameplus()
     {
     	$rsFcode = $this->master_model->loadRec("busmatch_date","where sector='02' and preselected_event='1' limit 1");
     	if($rsFcode=="") { die("No active Project.. pls contact SMDD. tnx.");}
   		redirect('vrs/login?fcode='.$rsFcode[0]["fair_code"]);
     }

     public function ifex()
     {
     	$rsFcode = $this->master_model->loadRec("busmatch_date","where sector='01' and preselected_event='1' limit 1");
     	if($rsFcode=="") { die("No active Project.. pls contact SMDD. tnx.");}
   		redirect('vrs/login?fcode='.$rsFcode[0]["fair_code"]);
     }

     public function createph()
     {
     	$rsFcode = $this->master_model->loadRec("busmatch_date","where sector='20' and preselected_event='1' limit 1");
     	if($rsFcode=="") { die("No active Project.. pls contact SMDD. tnx.");}
   		redirect('vrs/login?fcode='.$rsFcode[0]["fair_code"]);
     }

     public function ssx()
     {
     	$rsFcode = $this->master_model->loadRec("busmatch_date","where sector='25' and preselected_event='1' limit 1");
     	if($rsFcode=="") { die("No active Project.. pls contact SMDD. tnx.");}
   		redirect('vrs/login?fcode='.$rsFcode[0]["fair_code"]);
     }

     public function tanyag()
     {
     	$rsFcode = $this->master_model->loadRec("busmatch_date","where sector='24' and preselected_event='1' limit 1");
     	if($rsFcode=="") { die("No active Project.. pls contact SMDD. tnx.");}
   		redirect('vrs/login?fcode='.$rsFcode[0]["fair_code"]);
     }

     public function issueor()
     {

   		redirect('vrs/login?fcode=payment');
     }

     public function login()
     {
     	  $vfcode = strtoupper((isset($_GET['fcode'])) ? $_GET['fcode'] : (isset($_POST['fcode']) ? $_POST['fcode'] : ""));
          //get the posted values
          $username = $this->input->post("txt_username");
          $password = $this->input->post("txt_password");

          //set validations
          $this->form_validation->set_rules("txt_username", "Username", "trim|required");
          $this->form_validation->set_rules("txt_password", "Password", "trim|required");

          if ($this->form_validation->run() == FALSE)
          {
               //validation fails
               $this->load->view('vrs_login',array('fcode' => $this->input->post("fcode"),'systitle' => 'VISITOR REGISTRATION SYSTEM','sysName'=>'vrs'));
          }
          else
          {
               //validation succeeds
               if ($this->input->post('btn_login') == "Login")
               {

			        //$encrypted_string = $this->encrypt->encode($password);
			        $tmp = $this->encryption->encrypt($password);
        			$encrypted_string = str_replace(array('+', '/', '='), array('-', '_', '^'), $tmp);


        		  	//$plaintext_string = $this->encryption->decrypt($encrypted_string);

					//$teskey='msEKl+QfT4JOM+5zUFrZ832X6Qgaw8AQSVM3areCPHp5vg75xj/PO8bmOoXAIvn3zANUN4CAGli2EJRGHmn/Tw';

					//$plaintext_string2 = $this->encryption->decrypt($encrypted_string);
					//die("aaa= ".$encrypted_string." bbb= ".$plaintext_string." ccc= ".$plaintext_string2);

                    //check if username and password is correct


                    $usr_result = $this->master_model->get_user($username, $encrypted_string);
                    if ($usr_result) //active user record is present
                    {
                         //set the session variables
						 foreach($usr_result as $r1)
						 {
						 	//$data['v_useraccess']= $this->master_model->getRec("v_useraccess","where rep_code = ?",$r1['id'],"");
						 	$refID = $r1['id'];

                            $sessionArray = array(
                              'realname' => $r1['name'],
							  'urights' => $r1['rights'],
							  'user'    => $r1['username'],
							  'userid'    => $r1['id'],
							  'venue'    => $r1['sector'],
							  'venueNum'    => $r1['status'],
							  'default_form_type'    => $r1['default_type'],
							  'eventDB' => EVENT_DB,
							  'sysName' => 'vrs',
							  'systitle' => 'Visitor Registration System',
                              'loginuser' => TRUE,
                              'controller_CI' => "registration"
                            );

						 }

						 //$chkuri =  $this->uri->segment(3);
						 //die("aaa= ".$vfcode);

						 if($vfcode == "PAYMENT") {

						 	$this->session->set_flashdata('msg', '<div class="alert alert-danger text-center">Invalid FairCode!..<fayment></div>');
	                        redirect('vrs/login?fcode='.$this->input->post("fcode"));

						 } else {
							 $vsector = "";
							 $getValue = $this->master_model->loadRec("busmatch_date","where fair_code ='".$vfcode."' limit 1");

							 if($getValue<>"") {
								 foreach ($getValue as $r1) {
								 	$vsector = $r1['sector'];
								 }

							 } else {
							 	$this->session->set_flashdata('msg', '<div class="alert alert-danger text-center">Invalid FairCode!</div>');
	                         	redirect('vrs/login?fcode='.$this->input->post("fcode"));
							 }
						 }

						 $data['v_useraccess']= $this->master_model->getRec("v_useraccess","where sector='".$vsector."' and rep_code = ?",$refID,"");
						 //$data['v_useraccess']= $this->vrs_model->getRec("v_useraccess","where fair_code='".$vfcode."' AND rep_code = ?",$r1['id'],"");

						 //$sessionArray =[];
						 $ctr = 0;
						 if($data['v_useraccess']<>"")
						 {
						 	foreach ($data['v_useraccess'] as $r2) {
						 		$ctr ++;
						 		$sessionArrayT1[$ctr] = array(
                              	$ctr."r" => $r2['item_code']
						 		);
						 		$sessionArray = array_merge($sessionArray,$sessionArrayT1[$ctr]);
						 	}
						 }
						 //insert number of recs in $data['v_useraccess']
						 $sessionArrayT2 = array('numrights' => $ctr);
                         $sessionArray = array_merge($sessionArray,$sessionArrayT2);


                         $this->session->set_userdata('sessionData',$sessionArray);
                         //redirect("index");
						 //redirect("login/index");
						 //$data['uname'] = $username;
						 $set_data = $this->session->all_userdata();
						 $data['read_set_value'] = $set_data['sessionData']['realname'];
						 $data['sessionRights'] = $set_data['sessionData']['urights'];
						 $data['user'] = $set_data['sessionData']['user'];
                         //=== load main menu ========================
						   $data['homeBar'] = "class='active'";
						 //===========================================

						 //$this->load->view('index',$data);
						 //redirect('vrs/index?fcode='.$this->input->post("fcode"));
						 redirect('vrs/loadEvent?fcode='.$this->input->post("fcode"));
                    }
                    else
                    {
                         $this->session->set_flashdata('msg', '<div class="alert alert-danger text-center">Invalid username and password!</div>');
                         redirect('vrs/login?fcode='.$this->input->post("fcode"));
                    }
               }
               else
               {
                    redirect('vrs/loadEvent');
               }
          }
     }

	 public function logout() {
	   $set_data = $this->session->all_userdata();

	   if(isset($set_data['sessionData']['fcode'])) {

		   $fcode = $set_data['sessionData']['fcode'];
	       $sessionArray = array(
	                         'username' => '',
	                         'loginuser' => FALSE
	                       );
	       // Removing values from session
	       $this->session->unset_userdata('sessionData', $sessionArray);
	       //$data['read_set_value'] = 'Successfully Unset Session Set Value';
	       $this->session->set_flashdata('msg', '<div class="alert alert-success text-center">Logout Successfully</div>');

	   } else {
	   		redirect('vrs/login?fcode=');
	   }
       //die("aaa= ". $fcode);
       redirect('vrs/login?fcode='.$fcode);
     }

     public function loadEvent()
     {
       $vfcode = strtoupper((isset($_GET['fcode'])) ? $_GET['fcode'] : "");
       $set_data = $this->session->all_userdata();

	   if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {

		  	$userAccessMod = $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");
			//echo "sss=".$this->input->post("fcode");

			if($vfcode == "PAYMENT") {

				   $vsector = "00";
				   //$vsectorName = "ALL";
				   $vdesc = "ISSUE O.R.";
				   $getWebCode = "";
				   $disable_prereg = "";
				   $sectorName = "ALL";

			} else {

				$getEvent = $this->master_model->loadRec2("select fair_code,sector,description,disable_prereg,event,url_busmatch from busmatch_date where fair_code='".$vfcode."'");
				if($getEvent=="") {
					$this->session->set_flashdata('msg', '<div class="alert alert-danger text-center">Invalid FairCodez!</div>');
					redirect('vrs/login?fcode='.$vfcode);		// die("invalid FAIR CODE"));
				}
				if($getEvent[0]['event']=="" || !isset($getEvent[0]['event'])) {
					$this->session->set_flashdata('msg', '<div class="alert alert-danger text-center">No Event Name detected...report to SMDD</div>');
					redirect('vrs/login?fcode='.$vfcode);
				}


				foreach ($getEvent as $eventCode)
				 {
				   $vsector = $eventCode['sector']; //die($sector);
				   $vsectorName = $eventCode['event'];		//	get sector name
				   $vdesc = $eventCode['description'];
				   $getWebCode = trim($eventCode['url_busmatch']);
				   $disable_prereg = $eventCode['disable_prereg'];
				   $sectorName = $eventCode['event'];
				 }

				$vdisabled = explode('_', $disable_prereg); //die("aaa= ".$vdisabled[0]);
			 	//if($vdisabled[0]=="1") {die("FAIR CODE closed");}

			}



			 $getIP = explode('.',$_SERVER['REMOTE_ADDR']);
			 $xIP = (isset($getIP[3]) ? $getIP[3] : "1");

			 $sessionArrayT3 = array(
							  'fcode' => $vfcode,
							  'fdesc' => $vdesc,
							  'sector' => $vsector,
							  'sectorName' => $sectorName,
							  'getWebCode' => $getWebCode,
							  'terminalNo' => $xIP
							);
             $sessionArray = array_merge($set_data['sessionData'],$sessionArrayT3);

             $this->session->set_userdata('sessionData',$sessionArray);
	         //=======================================================================

            foreach ($userAccessMod as $r2)
            {

              $founds=0;
			  for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

			   $xxx = $set_data['sessionData'][$x."r"];
			   // Using ${} is a way to create dynamic variables,
			   if ($r2['c_code']==$xxx) { ${'vrs_'.$r2['c_code']} = $xxx; $founds=1; }
			   // ================================================================
			  }
			  if($founds==0) { ${'vrs_'.$r2['c_code']} = ""; }
			  //echo $data['vrs_'.$r2['c_code']]."<br>";

		    }

 			//print_r($sessionArray); die();

 			if($vrs_disable_home == "") { $this->main(); }
 			else { $this->dashboard(); }

	      }
	      else
	      {
	      redirect('vrs/index?fcode='.$vfcode);
	      }

     }

     public function main()
     {
      //=== load main menu ========================
	  $data['homeBar'] = "class='active'";
	  //===========================================

       $set_data = $this->session->all_userdata();

       //print_r($set_data['sessionData']); die();

	   if (isset($set_data['sessionData']) && isset($set_data['sessionData']['realname'])  && $set_data['sessionData']['realname'] != NULL)
		  {
		    $data['read_set_value'] = $set_data['sessionData']['realname'];
			$data['sessionRights'] = $set_data['sessionData']['urights'];
			$data['loginuser'] = $set_data['sessionData']['loginuser'];

			$data['sysName'] = $set_data['sessionData']['sysName'];
			$data['systitle'] = $set_data['sessionData']['systitle'];
			$data['eventDB'] = $set_data['sessionData']['eventDB'];

			$data['fcode'] = $set_data['sessionData']['fcode'];
			$data['sector'] = $set_data['sessionData']['sector'];
			$data['fairdesc'] = $set_data['sessionData']['fdesc'];
			$data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

			$data['myVenue'] = $set_data['sessionData']['venue'];
			$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			$data['controller_CI'] = $set_data['sessionData']['controller_CI'];


			$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");



			//===========================================
            //======== get RIGHTS procedure =============
            //===========================================
            foreach ($data['userAccessRef'] as $r1)
            {

              $founds=0;
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

               $xxx = $set_data['sessionData'][$x."r"];
               																					// Using ${} is a way to create dynamic variables, ex.====> ${'vrs_'.$r1['c_code']} = "";
               if ($r1['c_code']==$xxx) { $data['vrs_'.$r1['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r1['c_code']] = ""; }
              //echo $data['vrs_'.$r1['c_code']]."<br>";

            }
            foreach ($data['userAccessMod'] as $r2)
            {

              $founds=0;
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

               $xxx = $set_data['sessionData'][$x."r"];
               // Using ${} is a way to create dynamic variables,
               if ($r2['c_code']==$xxx) { $data['vrs_'.$r2['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r2['c_code']] = ""; }
              //echo $data['vrs_'.$r2['c_code']]."<br>";

            }
            //===========================================
            //===========================================


			// $data['vrs_diy_general_public_print'] = $set_data['sessionData']['vrs_diy_general_public_print'];

			$data['mmenu']='';
			$vcountry= (isset($_GET['ctry']) ? $_GET['ctry'] : "");

			if($vcountry=="")
			 {$data['results']=null;}
			else
			 {	//$data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where country='".$vcountry."' AND reg_status='T' AND deleted='0' and trade_code LIKE '".$data['fcode']."%' order by co_name");

				$data['varResults']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","","A.rep_code = B.rep_code","A.country='".$vcountry."' AND A.deleted= '0' and B.reg_status='T' and B.fair_code LIKE '".$data['fcode']."%' ORDER BY co_name","");

			  /*
				$data['varResults']= $this->vrs_read_model->joinTable3("v_contact_profile as A","v_attendance as B","v_voucher_code as C","A.rep_code,A.barcode,A.co_name,A.email,A.email2,A.work_email,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,A.region,B.buyerclass,B.visitor_type,B.visitor_status,B.date_apply,A.pid,B.url_form,B.validation_status,B.validated,B.date_validated,B.emailed,A.continent,C.item_code","A.rep_code = B.rep_code","B.rep_code = C.rep_code",$qryAdd." A.deleted= '0' and B.reg_status='T' and B.fair_code LIKE '".$data['fcode']."%' ORDER BY A.co_name","LEFT");
			  */

			 	$data['results'] = array();
			 	if($data['varResults']<>"") {
			 		foreach($data['varResults'] as $rs1) {

			 		//$temp = $this->encryption->encrypt($rs1['email']);
					//$vpid= str_replace(array('+', '/', '='), array('-', '_', '^'), $temp);

				  	$data['results'][] = array("pid"=>$vpid,"rep_code"=>$rs1['rep_code'],"barcode"=>$rs1['barcode'],"co_name"=>$rs1['co_name'],"email"=>$rs1['email'],"email2"=>$rs1['email2'],"work_email"=>$rs1['work_email'],"mobile"=>$rs1['mobile'],"cont_per_fn"=>$rs1['cont_per_fn'],"cont_per_ln"=>$rs1['cont_per_ln'],"country"=>$rs1['country'],"buyerclass"=>$rs1['buyerclass'],"visitor_type"=>$rs1['visitor_type'],"visitor_status"=>$rs1['visitor_status'],"date_apply"=>$rs1['date_apply'],"url_form"=>$rs1['url_form'],"validated"=>$rs1['validated'],"date_validated"=>$rs1['date_validated'],"emailed"=>$rs1['emailed'],"continent"=>$rs1['continent'],"region"=>$rs1['region'],"validation_status"=>$rs1['validation_status'],"promoCode"=>$rs1['item_code'],"isPaid"=>$rs1['v_attendance.remarks']);
					}
				}



			 }
		  }
	   else
	      {
			redirect('vrs/index');
		  }
	   $this->load->view('vrs_main',$data);
	 }

     public function cutoff()
     {
      //=== load main menu ========================
	  $data['userBar'] = "class='active'";
	  //===========================================

       $set_data = $this->session->all_userdata();

	   if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {
		    $data['read_set_value'] = $set_data['sessionData']['realname'];
			$data['sessionRights'] = $set_data['sessionData']['urights'];
			$data['loginuser'] = $set_data['sessionData']['loginuser'];

			$data['sysName'] = $set_data['sessionData']['sysName'];
			$data['systitle'] = $set_data['sessionData']['systitle'];
			$data['eventDB'] = $set_data['sessionData']['eventDB'];

			$data['fcode'] = $set_data['sessionData']['fcode'];
			$data['sector'] = $set_data['sessionData']['sector'];
			$data['fairdesc'] = $set_data['sessionData']['fdesc'];
			$data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

			$data['myVenue'] = $set_data['sessionData']['venue'];
			$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			$data['cutoff']= (isset($_POST['cutoff']) ? $_POST['cutoff'] : "");
			$data['vpass']= (isset($_POST['vpass']) ? $_POST['vpass'] : "");

			$data['controller_CI'] = $set_data['sessionData']['controller_CI'];

			$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

			$data['getCO'] = $this->master_model->loadRec("v_reference","where switch='cutof' and sector LIKE '%".$data['sector']."%'");

			if($data['cutoff']=="" && $data['vpass']=="")
			 {
			  $data['cutofMessage'] = "";
			 }
			else
			 {
			   if($data['vpass']<>"mis")
			    {
			     $data['cutofMessage'] = "Invalid Passcode....bummer";
			    }
			   else
			    {
			      if ($data['cutoff']<>"")
			       { //die($_POST['cutoffButt']);
				     $vdate=explode("_",$_POST['cutoff']);
				   	 if($vdate[1]=='DAY 1'){ $vexp='<=';} else {$vexp='=';}

					 switch($_POST['cutoffButt'])
					 {
					  case "proceedCO":
			            $this->vrs_model->updateAllRecord("v_contact_profile","cutoff","1","date(date_apply)".$vexp."'".$vdate[0]."' and reg_status='T' and deleted='0' and trade_code='".$data['fcode']."'");
			            $this->vrs_model->updateAllRecord("v_contact_profile","marked",$data['myVenueNum'],"date(date_apply)".$vexp."'".$vdate[0]."' and reg_status='T' and deleted='0' and trade_code='".$data['fcode']."'");
			            $this->vrs_model->updateAllRecord("v_contact_profile","date_input",date('Y-m-d H:i:s'),"date(date_apply)".$vexp."'".$vdate[0]."' and reg_status='T' and deleted='0' and trade_code='".$data['fcode']."'");
			            //======= update CUTOFF DATE ================
			            $this->master_model->updateReftable("v_reference","p_switch",date('Y-m-d H:i:s'),"switch","cutof");
					  break;
					  case "resetCO":
			            $this->vrs_model->updateAllRecord("v_contact_profile","cutoff","0","date(date_apply)".$vexp."'".$vdate[0]."' and reg_status='T' and deleted='0' and trade_code='".$data['fcode']."'");
			            $this->vrs_model->updateAllRecord("v_contact_profile","marked",$data['myVenueNum'],"date(date_apply)".$vexp."'".$vdate[0]."' and reg_status='T' and deleted='0' and trade_code='".$data['fcode']."'");
			            $this->vrs_model->updateAllRecord("v_contact_profile","date_input",date('Y-m-d H:i:s'),"date(date_apply)".$vexp."'".$vdate[0]."' and reg_status='T' and deleted='0' and trade_code='".$data['fcode']."'");
					  break;
					 }
					 $data['cutofMessage'] ="Done";
			       }
			      else
			       { $data['cutofMessage'] ="Select Cut-off Date"; }
			    }
             }
		  }
	   else
	      {
			redirect('vrs/index');
		  }
	   $this->load->view('vrs_cuttof',$data);
	 }

     public function reports()
     {
      //=== load main menu ========================
	  $data['reportBar'] = "class='active'";
	  //===========================================

       $set_data = $this->session->all_userdata();

	   if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {
		    $data['read_set_value'] = $set_data['sessionData']['realname'];
			$data['sessionRights'] = $set_data['sessionData']['urights'];
			$data['loginuser'] = $set_data['sessionData']['loginuser'];
			$data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

			$data['myVenue'] = $set_data['sessionData']['venue'];
			$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			$data['sysName'] = $set_data['sessionData']['sysName'];
			$data['systitle'] = $set_data['sessionData']['systitle'];
			$data['eventDB'] = $set_data['sessionData']['eventDB'];

			$data['fcode'] = $set_data['sessionData']['fcode'];
			$data['sector'] = $set_data['sessionData']['sector'];
			$data['fairdesc'] = $set_data['sessionData']['fdesc'];

			$data['controller_CI'] = $set_data['sessionData']['controller_CI'];

			$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");


            //===========================================
            //======== get RIGHTS procedure =============
            //===========================================
            foreach ($data['userAccessRef'] as $r1)
            {

              $founds=0;
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

               $xxx = $set_data['sessionData'][$x."r"];
               																					// Using ${} is a way to create dynamic variables, ex.====> ${'vrs_'.$r1['c_code']} = "";
               if ($r1['c_code']==$xxx) { $data['vrs_'.$r1['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r1['c_code']] = ""; }
              //echo $data['vrs_'.$r1['c_code']]."<br>";

            }
            foreach ($data['userAccessMod'] as $r2)
            {

              $founds=0;
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

               $xxx = $set_data['sessionData'][$x."r"];
               // Using ${} is a way to create dynamic variables,
               if ($r2['c_code']==$xxx) { $data['vrs_'.$r2['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r2['c_code']] = ""; }
              //echo $data['vrs_'.$r2['c_code']]."<br>";

            }
            //===========================================

            // ================================ DASHBOARD FOR PRE-REG ==========================================================

            $rstat = ($data['vrs_view_preg_dashboard']<>"" ? "B.pre_reg='P'" : "B.reg_status='T'");    // ===== if vrs_view_preg_dashboard has value then SHOW PREREG IN DASHBOARD ONLY

			// if($data['vrs_view_preg_dashboard']<>"")  // ===== if has value SHOW PREREG IN DASHBOARD ONLY
			// {
			// 	$rstat = "pre_reg='P'";
			// 	$vexp="";
			// }

			// ================================ DASHBOARD FOR PRE-REG ==========================================================


			$data['reportType'] = (isset($_GET['type']) ? $_GET['type'] : (isset($_POST['type']) ? $_POST['type'] : ""));  // report perCtry, perDaily, perType
			$data['vselectType']= (isset($_POST['selectType']) ? $_POST['selectType'] : "ALL");
			// ====== load visitor type (TRADE BUYER VISITOR MEDIA) =============
			$data['vType'] = $this->master_model->loadRec2("SELECT c_profile,c_code from v_reference where switch='BSTAT' and sector LIKE '%".$data['sector']."%' order by sortfield");
			//===================================================================
			//if($data['vselectType']=='ALL') {$qryAdd = " cutoff='1' AND ";} else {$qryAdd = "visitor_type LIKE '%".$data['vselectType']."%' AND cutoff='1' AND ";}

			if($data['vselectType']=='ALL')   {$qryAdd = "";}                 else {$qryAdd = "B.visitor_type LIKE '%".$data['vselectType']."%' AND ";}

			$data['disable_query'] = (isset($_GET['d']) && $_GET['d']<>"" ? "1" : "");	// disable dropdown query in report.php id DASHBOARD used

			switch($data['reportType'])
			 {
			  //default :
			  //  $qryAdd = "";
			  case "perCtry" :
			    if(isset($_GET['d']) && $_GET['d']<>"")
			     {
			      //$this->load->library('encrypt2');
				  //$myDay = $this->encrypt2->decode2($_GET['d']);

				  $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['d']);
        		  $myDay = $this->encryption->decrypt($decryptX);

				  //===== chk if valid by searching '#' =========
				  $xValid = strpos($myDay,'#');    					//die("aaa = ". $myDay); //[0]."   bbb= ".$tmp[1]);
				  if($xValid=="") {redirect('vrs/index');}
				  //=============================================

				  $tmp = explode('#',$myDay);           			//die("aaa = ". $tmp[1]); // TRADE BUYER_and date(date_apply) = value

				  $data['vType'] = $tmp[0];

				  $qryDate = ($tmp[1]<>"" ? $tmp[1]." and " : "and");

				  if($tmp[0]=='VISITORALL')
				   //{$qryAdd = "(visitor_type LIKE '%VISITOR%' or visitor_type LIKE '%GENERAL PUBLIC%') ".$qryDate;}
				   {$qryAdd = "(visitor_type LIKE '%GUEST%' or visitor_type LIKE '%GENERAL PUBLIC%') ".$qryDate;}
				  else
				   {$qryAdd = "visitor_type LIKE '%".$tmp[0]."%' ".$qryDate;}
				 }


				 //else {$qryAdd=" cutoff='1' AND ";}

			    //$data['results'] = $this->vrs_read_model->loadRec2("SELECT country,count(trade_code) as total1 FROM v_contact_profile WHERE ".$qryAdd." ".$rstat." AND deleted='0' and trade_code LIKE '".$data['fcode']."%' GROUP by country");


				 $data['results'] = $this->vrs_read_model->loadRec2("SELECT A.country,count(B.fair_code) as total1 FROM v_contact_profile as A INNER JOIN v_attendance as B ON A.rep_code = B.rep_code WHERE ".$qryAdd." ".$rstat." and A.deleted = '0' and B.fair_code LIKE '".$data['fcode']."%' GROUP by country");


			    //if(empty($data['results'])) {die("errrrrror");}
			   //$this->db->_error_message();

			    if($data['results']=="") { $data['results'] =null; }
			  break;
			  case "perDaily":
			   // $data['results'] = $this->vrs_read_model->loadRec2("SELECT date_apply,count(trade_code) as total1 FROM v_contact_profile WHERE ".$qryAdd." ".$rstat." AND deleted='0' and trade_code LIKE '".$data['fcode']."%' GROUP by date(date_apply)");

			  	 $data['results'] = $this->vrs_read_model->loadRec2("SELECT B.date_apply,count(B.fair_code) as total1 FROM v_contact_profile as A INNER JOIN v_attendance as B ON A.rep_code = B.rep_code WHERE ".$qryAdd." ".$rstat." and A.deleted = '0' and B.fair_code LIKE '".$data['fcode']."%' GROUP by date(B.date_apply)");

			    if($data['results']=="") { $data['results'] =null; }
			  break;
			  case "perType":
			  	//$data['results'] = $this->vrs_read_model->loadRec2("SELECT if(UCASE(country)<>'PHILIPPINES','FOREIGN','LOCAL') as BUYERTYPE, SUM(IF(UCASE(buyer_type)='NEW',1,0 )) AS NEW, SUM(IF(UCASE(buyer_type)='REGULAR',1,0 )) AS REGULAR, count(buyer_type) as total1 FROM v_contact_profile WHERE ".$qryAdd." ".$rstat." AND deleted='0' and trade_code LIKE '".$data['fcode']."%' GROUP by BUYERTYPE");

			  	$data['results'] = $this->vrs_read_model->loadRec2("SELECT if(UCASE(A.country)<>'PHILIPPINES','FOREIGN','LOCAL') as BUYERTYPE, SUM(IF(UCASE(B.visitor_status)='NEW',1,0 )) AS NEW, SUM(IF(UCASE(B.visitor_status)='REGULAR',1,0 )) AS REGULAR, count(B.visitor_status) as total1 FROM v_contact_profile as A INNER JOIN v_attendance as B ON A.rep_code = B.rep_code WHERE ".$qryAdd." ".$rstat." and A.deleted = '0' and B.fair_code LIKE '".$data['fcode']."%' GROUP by BUYERTYPE");

			    if($data['results']=="") { $data['results'] =null; }
			  break;
			  case "perCompany":
			  	//$data['results'] = $this->vrs_read_model->loadRec2("SELECT COUNT(DISTINCT(co_name)) as total1,if(UCASE(country)<>'PHILIPPINES','FOREIGN','LOCAL') as ORIGIN FROM v_contact_profile WHERE ".$qryAdd." ".$rstat." AND deleted='0' and trade_code LIKE '".$data['fcode']."%' GROUP by ORIGIN");

			  	$data['results'] = $this->vrs_read_model->loadRec2("SELECT COUNT(DISTINCT(A.co_name)) as total1,if(UCASE(A.country)<>'PHILIPPINES','FOREIGN','LOCAL') as ORIGIN FROM v_contact_profile as A INNER JOIN v_attendance as B ON A.rep_code = B.rep_code WHERE ".$qryAdd." ".$rstat." and A.deleted = '0' and B.fair_code LIKE '".$data['fcode']."%' GROUP by ORIGIN");


			    if($data['results']=="") { $data['results'] =null; }
			  break;
			  case "perPreg":
			    //$data['results'] = $this->vrs_read_model->loadRec2("SELECT country,SUM(IF(visitor_type LIKE 'TRADE BUYER%', 1, 0)) as TRADE,
	            //                                               SUM(IF(visitor_type= 'GENERAL PUBLIC', 1, 0)) as GENPUB,SUM(IF(visitor_type LIKE 'VISITOR%', 1, 0)) as VISITOR,
                //                                               SUM(IF(visitor_type='MEDIA', 1, 0)) as MEDIA,count(visitor_type) as total1
				//											   FROM v_contact_profile WHERE ".$qryAdd." pre_reg='P' AND
				//											   reg_status='T' AND deleted='0' and trade_code LIKE '%".$data['fcode']."%' GROUP by country");
			  	$data['results'] = $this->vrs_read_model->loadRec2("SELECT country,SUM(IF(visitor_type LIKE 'TRADE BUYER%', 1, 0)) as TRADE,
	                                                           SUM(IF(visitor_type= 'GENERAL PUBLIC', 1, 0)) as GENPUB,SUM(IF(visitor_type LIKE 'GUEST%', 1, 0)) as GUEST,
                                                               SUM(IF(visitor_type='MEDIA', 1, 0)) as MEDIA,count(visitor_type) as total1
															   FROM v_contact_profile WHERE ".$qryAdd." pre_reg='P' AND
															   reg_status='T' AND deleted='0' and trade_code LIKE '%".$data['fcode']."%' GROUP by country");

			    /*
			    $resultPregArrive = $this->vrs_read_model->loadRec2("SELECT SUM(IF(visitor_type LIKE 'TRADE BUYER%', 1, 0)) as TRADE,
                                                               SUM(IF(visitor_type= 'GENERAL PUBLIC', 1, 0)) as GENPUB,SUM(IF(visitor_type LIKE 'VISITOR%', 1, 0)) as VISITOR,
                                                               SUM(IF(visitor_type='MEDIA', 1, 0)) as MEDIA FROM v_contact_profile WHERE ".$qryAdd."
															   pre_reg='P' AND reg_status='T' AND deleted='0' and trade_code LIKE '%".$data['fcode']."%' ");
				*/
				$resultPregArrive = $this->vrs_read_model->loadRec2("SELECT SUM(IF(visitor_type LIKE 'TRADE BUYER%', 1, 0)) as TRADE,
                                                               SUM(IF(visitor_type= 'GENERAL PUBLIC', 1, 0)) as GENPUB,SUM(IF(visitor_type LIKE 'GUEST%', 1, 0)) as GUEST,
                                                               SUM(IF(visitor_type='MEDIA', 1, 0)) as MEDIA FROM v_contact_profile WHERE ".$qryAdd."
															   pre_reg='P' AND reg_status='T' AND deleted='0' and trade_code LIKE '%".$data['fcode']."%' ");
				/*
				$resultPregTotal  = $this->vrs_read_model->loadRec2("SELECT SUM(IF(visitor_type LIKE 'TRADE BUYER%', 1, 0)) as TRADE,count(visitor_type) as PregTOTAL,
                                                               SUM(IF(visitor_type= 'GENERAL PUBLIC', 1, 0)) as GENPUB,SUM(IF(visitor_type LIKE 'VISITOR%', 1, 0)) as VISITOR,
                                                               SUM(IF(visitor_type='MEDIA', 1, 0)) as MEDIA FROM v_contact_profile WHERE ".$qryAdd." pre_reg='P'
															   AND deleted='0' and trade_code LIKE '%".$data['fcode']."%' ");
				*/

				$resultPregTotal  = $this->vrs_read_model->loadRec2("SELECT SUM(IF(visitor_type LIKE 'TRADE BUYER%', 1, 0)) as TRADE,count(visitor_type) as PregTOTAL,
                                                               SUM(IF(visitor_type= 'GENERAL PUBLIC', 1, 0)) as GENPUB,SUM(IF(visitor_type LIKE 'GUEST%', 1, 0)) as GUEST,
                                                               SUM(IF(visitor_type='MEDIA', 1, 0)) as MEDIA FROM v_contact_profile WHERE ".$qryAdd." pre_reg='P'
															   AND deleted='0' and trade_code LIKE '%".$data['fcode']."%' ");

				foreach ($resultPregArrive as $rPreg)
				  {
				    $data['tradeb'] = $rPreg['TRADE'];
					//$data['visitor'] = $rPreg['VISITOR'];
					$data['visitor'] = $rPreg['GUEST'];
					$data['genpub'] = $rPreg['GENPUB'];
					$data['media'] = $rPreg['MEDIA'];
				  }
				foreach ($resultPregTotal as $rPreg)
				  {
				    $data['TOTtradeb'] = $rPreg['TRADE'];
					//$data['TOTvisitor'] = $rPreg['VISITOR'];
					$data['TOTvisitor'] = $rPreg['GUEST'];
					$data['TOTgenpub'] = $rPreg['GENPUB'];
					$data['TOTmedia'] = $rPreg['MEDIA'];
					$data['TOTPreg'] = $rPreg['PregTOTAL'];
				  }
				 //SUM(IF(visitor_type='VIP', 1, 0)) as VIP,
                 //SUM(IF(visitor_type='ATTEND CONFERENCE', 1, 0)) as CONFERENCE,
                 //SUM(IF(visitor_type='VIB', 1, 0)) as VIB,
				if($data['results']=="") { $data['results'] =null; }
			  break;
			  default:
			    $data['results'] =null;
			  break;
             }
		  }
	   else
	      {
			redirect('vrs/index');
		  }
	   $this->load->view('vrs_report',$data);
	 }


     public function browseRec()
     {
      //=== load main menu ========================
	  $data['browseBar'] = "class='active'";
	  //===========================================

       $set_data = $this->session->all_userdata();

	   if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {
		    $data['read_set_value'] = $set_data['sessionData']['realname'];
			$data['sessionRights'] = $set_data['sessionData']['urights'];
			$data['loginuser'] = $set_data['sessionData']['loginuser'];

			$data['sysName'] = $set_data['sessionData']['sysName'];
			$data['systitle'] = $set_data['sessionData']['systitle'];
			$data['eventDB'] = $set_data['sessionData']['eventDB'];

			$data['fcode'] = $set_data['sessionData']['fcode'];
			$data['sector'] = $set_data['sessionData']['sector'];
			$data['fairdesc'] = $set_data['sessionData']['fdesc'];
			$data['diyTerminal'] = $set_data['sessionData']['terminalNo']; //echo "aaa=".$data['fcode']; die();

			$data['myVenue'] = $set_data['sessionData']['venue'];
			$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			$data['controller_CI'] = $set_data['sessionData']['controller_CI'];

			$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

			//$data['results'] = $this->vrs_model->loadRec("v_contact_profile","where cutoff='1' and reg_status='T' and deleted='0' and trade_code LIKE '%".$data['fcode']."%' order by co_name");

			$data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where reg_status='T' and deleted='0' and trade_code LIKE '%".$data['fcode']."%' order by co_name");


			//$data['results']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","A.*,B.visitor_type,B.visitor_status as buyer_type,B.buyerclass","A.rep_code = B.rep_code","A.deleted= '0' and B.reg_status='T' and B.fair_code = '".$data['fcode']."' order by A.co_name","");


			     if($data['results']=="")
			      {
				    $data['results'] =null;
				  }

		  }
	   else
	      {
			redirect('vrs/index');
		  }
	   $this->load->view('vrs_browse',$data);
	 }


     public function selectedRec()
     {
	  //$this->load->library('encrypt2');
      //=== load main menu ========================
	  //$data['homeBar'] = "class='active'";
	  //===========================================

       $set_data = $this->session->all_userdata();

	   if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {
		    $data['read_set_value'] = $set_data['sessionData']['realname'];
			$data['sessionRights'] = $set_data['sessionData']['urights'];
			$data['username'] = $set_data['sessionData']['user'];
			$data['loginuser'] = $set_data['sessionData']['loginuser'];

			$data['fcode'] = $set_data['sessionData']['fcode'];
			$data['sysName'] = $set_data['sessionData']['sysName'];
			$data['systitle'] = $set_data['sessionData']['systitle'];
			$data['eventDB'] = $set_data['sessionData']['eventDB'];

			$data['sector'] = $set_data['sessionData']['sector'];
			$data['sectorName'] = $set_data['sessionData']['sectorName'];

			$data['fairdesc'] = $set_data['sessionData']['fdesc'];
			$data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

			$data['myVenue'] = $set_data['sessionData']['venue'];
			$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			$data['default_form_type'] = $set_data['sessionData']['default_form_type'];

			$data['controller_CI'] = $set_data['sessionData']['controller_CI'];


			$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");
			//$data['RegStatus']=$this->master_model->loadRec("v_reference","where switch='MN6' order by c_profile");


			$data['vurl']= (isset($_POST['vurl']) ? $_POST['vurl'] : (isset($_GET['vurl']) ? $_GET['vurl'] : ""));
			$data['mmenu']= (isset($_POST['mmenu']) ? $_POST['mmenu'] : (isset($_GET['mmenu']) ? $_GET['mmenu'] : ""));
			//$data['mmenu']= ($_POST['mmenu']=="") ? $_GET['mmenu'] : $_POST['mmenu'];
			//$data['fcode']= $_POST['fcode'];
			//$data['sector']= $_POST['sector'];
			//die("aaa = ".$data['mmenu']);


			if($this->input->post('btn_submit')=='cancel') {redirect("vrs/search/".$data['vurl']);}

			$data['barcodeMessage']="";
			if($data['mmenu'] == "barcodeSearch")  // using scanner

			{
			 	 $data["barcode"] = (isset($_POST['vbarcode']) ? trim($_POST['vbarcode']) : (isset($_GET['vbarcode']) ? trim($_GET['vbarcode']) : ""));

			 	 //die("bbb=".$_POST['vbarcode']);
			 	 //$test = "BEGIN:EESY_PROFILE|FN:aaaaaaa|LN:zzzzz|EMAIL:<EMAIL>|END:EESY_PROFILE";
				 $xQRvalue = explode("|J",$data["barcode"]);


			     //if(strtoupper($xQRvalue[0])=="BEGIN:EESY_PROFILE")
			   	 if(strtoupper($xQRvalue[0])=="BEGIN:VCARD")
				 {
				 	$fn ="";$ln ="";$email ="";$comp ="";$ctry ="";$buss ="";$tel ="";$salutation ="";$qrFrmVRS="";
				 	$xval = count($xQRvalue);

				 	//die("aa=".$xval);

				 	for ($x = 0; $x <= $xval-1; $x++) {

				 		//die ($xQRvalue[$x]."<br>");

				 	  //if(strtoupper($xQRvalue[$x]) == 'BEGIN:EESY_PROFILE')
				 	  //if(strtoupper($xQRvalue[0])=="BEGIN:VCARD")
				 	  // {
				 			$zValue = explode(":",$xQRvalue[$x]);  //print_r($zValue); die("   aaa=");

				 			switch (strtoupper($zValue[0])) {
				 			case 'FN':
				 					$fn = trim($zValue[1]); //die("zzz=".$fn);
				 				break;
				 			case 'N':
				 					$ln = trim($zValue[1]);
				 				break;
				 			case 'EMAIL':
				 					$email = trim($zValue[1]);
				 				break;
				 			case 'COMPANY':
				 			case 'ORG':
				 					$comp = trim($zValue[1]);
				 				break;
				 			case 'COUNTRY':
				 			case 'ADR':
				 					$ctry = trim($zValue[1]);
				 				break;
				 			case 'TEL':
				 					$tel = trim($zValue[1]);
				 				break;
				 			case 'PREFIX':
				 					$salutation = trim($zValue[1]);
				 				break;
				 			case 'BUSINESS':
				 					$buss = trim($zValue[1]);
				 				break;
				 			case 'VRS':
				 					$qrFrmVRS = trim($zValue[1]); //die("aaa=".$qrFrmVRS);
				 				break;
				 			}
				 	   //}
				 	}
				 	//die

				 	  // ==== pointr west ==================
				 	  //$recExist = $this->vrs_read_model->getRec("v_contact_profilexx","where cont_per_ln='".$ln."' and cont_per_fn='".$fn."' and co_name='".$comp."' and  email = ? limit 1",$email,'');
				 	  //====================================
				 	  $data['results'] = $this->vrs_read_model->getRec("v_contact_profile","where co_name='".$comp."' and  email = ? limit 1",$email,'');

				 		//print_r($recExist);
				 	  //die("aaa=".$qrFrmVRS);

				 	  if($data['results']=="")
					 	{
					 		$data['barcodeMessage']="Record not Found...";	//.$data['fairdesc']." APP....";
							$data['mmenu'] = "scan";
					 	}
					  else
					    {
						   	if(isset($qrFrmVRS) && $qrFrmVRS <> "P" ) {

						   		//$data['barcodeMessage']="Not Valid as Pre-registered";
								//$data['mmenu'] = "scan";

								$data['repcode'] = $data['results'][0]["rep_code"];

						     } else {
						  		$data['repcode'] = $data['results'][0]["rep_code"];
						 	}
					    }

					 				/*
					 				========== USED in pointwest APP to add/update records ============================

									 	  if($recExist=="")
									 	  	{
									 	  		// ==== pointr west ==================
									 	  		$refId = $this->vrs_model->insertRecord("v_contact_profile");
									 	  		$barcodeValue = $refId + 2000000;
										  		$vpid = $this->generatePID($data['fcode']);  //die("aaa = ".$pid);

										  		$encVal = $this->encryption->encrypt($vpid);
					         					$data['pid'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);

									 	  		$vnum = $this->vrs_model->insertRecord("v_attendance");
									 	  		// ===================================
									 	  		//die("xxx");

									 	  		// $data['mmenu'] = "scan";
								   				// $this->load->view('vrs_main',$data);

									 	  		//$refId = "";
								   				//$data['results'] = null;
								   				//$data['barcodeMessage']="Record not Foundz....";

									 	  	}
									 	  else
									 	  	{

									 	  		$refId = $recExist[0]["rep_code"];
									 	  		$barcodeValue = $recExist[0]["barcode"];
										  		$vpid = $recExist[0]["pid"];

										  		$encVal = $this->encryption->encrypt($vpid);
					         					$data['pid'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);

										  		$attenExist = $this->vrs_read_model->getRec("v_attendance","where rep_code='".$refId."' and sector='".$data['sector']."' and  fair_code = ? limit 1",$data['fcode'],'');
										  		if($attenExist=="")
										  			{
										  				$vnum = $this->vrs_model->insertRecord("v_attendance");
										  				$this->vrs_model->updateAttendance("v_attendance",$refId,$data['fcode'],$data['sector'],$vnum);
														$this->vrs_model->updateRecTable("v_attendance","barcode",$barcodeValue,$data['fcode'],$data['sector'],$refId);
														$this->vrs_model->updateRecTable("v_attendance","visitor_status",$recExist[0]["buyer_type"],$data['fcode'],$data['sector'],$refId);
														$this->vrs_model->updateRecTable("v_attendance","visitor_type",$recExist[0]["visitor_type"],$data['fcode'],$data['sector'],$refId);
														$this->vrs_model->updateRecTable("v_attendance","buyerclass",$recExist[0]["buyerclass"],$data['fcode'],$data['sector'],$refId);
														$this->vrs_model->updateRecTable("v_attendance","pre_reg",$recExist[0]["pre_reg"],$data['fcode'],$data['sector'],$refId);
										  			}
										  		else { $vnum = $attenExist[0]["refno"]; }

									 	  	}
									 	  	//die("aaa=".$qrFrmVRS);

									 	  if($qrFrmVRS=="1")
										  {
										  		$data['results'] = $recExist;
										  		$data['mmenu'] = "barcodeSearch";
										  		$data['repcode'] = $refId;		//die($data['repcode']);
										  }
										  else
										  {

										 	  $data['results'] = $recExist;
											  $data['mmenu'] = "scan";
											  if($refId <> "")
											  {
											  	$data['barcodeMessage']= $recExist[0]["salutation"]." ".$fn." was marked Arrive....";
											  	$this->vrs_model->updateRecord("v_contact_profile","reg_status","T","rep_code",$refId);
											  	$this->vrs_model->updateRecord("v_contact_profile","deleted","0","rep_code",$refId);
											  	$this->vrs_model->updateRecord("v_contact_profile","pre_reg","M","rep_code",$refId);
											  	$this->vrs_model->updateRecord("v_contact_profile","date_apply",date('Y-m-d H:i:s'),"rep_code",$refId);
											  	$this->vrs_model->updateRecTable("v_attendance","date_apply",date('Y-m-d H:i:s'),$data['fcode'],$data['sector'],$refId);

											  }
											  // die("aaa=".$qrFrmVRS);

											 // ==== point west ==================
											 // ==================================
											  $data['vurl']	= $refId;
											  // ==== update field marked = 1 for subserver use
											  $this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","venue",$data['myVenue'],"rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","barcode",$barcodeValue,"rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","pid",$vpid,"rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","trade_code",$data['fcode'],"rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","sector",$data['sector'],"rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","co_name",$comp,"rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","cont_per_fn",$fn,"rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","cont_per_ln",$ln,"rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","email",$email,"rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","mobile",$tel,"rep_code",$refId);

											  $vsalutation = ($salutation=="" ? "Mr." : $salutation);
											  $this->vrs_model->updateRecord("v_contact_profile","salutation",$vsalutation,"rep_code",$refId);
											  switch($vsalutation)
												 {
												   case "Mr." :
										             $this->vrs_model->updateRecord("v_contact_profile","gender","Male","rep_code",$refId);
												   break;
												   default:
										             $this->vrs_model->updateRecord("v_contact_profile","gender","Female","rep_code",$refId);
												 }

											  $vctry = ($ctry=="" ? "Philippines" : $ctry);
											  $this->vrs_model->updateRecord("v_contact_profile","country",$vctry,"rep_code",$refId);

											  if(trim($vctry)<>"")
											  	{
											  		$this->vrs_model->updateRecord("v_contact_profile","continent",$this->master_model->getContinent($vctry),"rep_code",$refId);
												}

											  $this->vrs_model->updateRecord("v_contact_profile","buyer_type","NEW","rep_code",$refId);
											  $this->vrs_model->updateRecord("v_contact_profile","exhi_conf_gov","MOBILEAPP","rep_code",$refId);

											  // ==== update ATTENDANCE =======================

											  $this->vrs_model->updateAttendance("v_attendance",$refId,$data['fcode'],$data['sector'],$vnum);
											  $this->vrs_model->updateRecTable("v_attendance","barcode",$barcodeValue,$data['fcode'],$data['sector'],$refId);
											  $this->vrs_model->updateRecTable("v_attendance","visitor_status","NEW",$data['fcode'],$data['sector'],$refId);

											  // ==============================================

											  // ==== update representation ===================
											  // if($buss<>"")
											  // { //die("aaa=".$buss);
												 // $vbustype = $this->master_model->loadRec("v_reference","where switch='B2' and c_profile='".$buss."' limit 1");
												 // //=== delete record in sub table first =============
												 // $this->vrs_model->deleteRecTable("v_representation",$refId,$data['fcode'],$data['sector']);
												 // $this->vrs_model->insertRecTable("v_representation",$refId,$barcodeValue,$vbustype[0]['c_code'],$buss,$data['fcode'],$data['sector']);
											  // }
											  //==============================================


										 	  $data['barcodeMessage']="Hi Mr/Ms. ".$fn. " Welcome to ".$data['fairdesc'];
											  $data['mmenu'] = "mobileapp"; // "edit"; // "barcodeSearch" return to scan module
											  $data['repcode'] = $refId;
											  $data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where barcode='".$barcodeValue."' order by co_name");
											 //===== PRINT ID =======
											 // ==== point west ==================
											 // ==================================

										  } 	// end ($qrFrmVRS=="1")
					  				==========  end USED in pointwest APP to add/update records ============================
					  				*/

				 	//die();

				 }
				 elseif(!ctype_digit($data["barcode"]))
				 { // die("xxx = ".$data["barcode"]);
				 	//$data['results'] = $this->vrs_read_model->loadRec("v_voucher_code","where item_code='".$data['barcode']."' and (isnull(barcode) or barcode=0)");
				 	$data['results'] = $this->vrs_read_model->loadRec("v_voucher_code","where item_code='".$data['barcode']."'");
				 	if($data['results']=="")
				 	{
				 		$data['barcodeMessage']="Invalid Invitation Code";
						$data['mmenu'] = "scan";
				 	}
				 	else
				 	{
				 		$getValueCode = $data['results'][0]["barcode"]; //die($getValueCode);

				 		if(is_null($getValueCode) or $getValueCode==0)
				 		{
				 			$data['barcodeMessage']="Invitation Code Valid";
				 			$data['mmenu'] = "openForm";
				 		}
				 		else
				 		{
				 			$data['rsValue'] = $this->vrs_read_model->loadRec("v_contact_profile","where barcode='".$getValueCode."'");

				 			$this->vrs_model->loadRec2("update v_voucher_code set number_of_use = number_of_use + 1 where "."item_code='".$data['barcode']."'","update");

				 			$data['barcodeMessage']="Sorry, Invitation Code already used by <br>".$data['rsValue'][0]["salutation"]." ".$data['rsValue'][0]["cont_per_fn"]." ".$data['rsValue'][0]["cont_per_ln"];;
				 			$data['mmenu'] = "scan";
				 		}

				 		$data['results']= "";


				 	}
				 }
				 else
				 {
				 	$data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where barcode='".$data['barcode']."' order by co_name");

				 	if($data['results']=="")	// if not found CHECK rfid field if use
				 	{
				 		$data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where rfid='".$data['barcode']."'");
				 	}


				     if($data['results']=="" || $data["barcode"]=="")
				      {


					    $data['barcodeMessage']="Record not Found...";		//" / use ".$data['fairdesc']." APP....";
						$data['mmenu'] = "scan"; // return to scan module


						if(isset($_POST['wristBand']) && $_POST['wristBand']=="yes")     // ======= CREATEPHILS assign BARCODE to WRISTBAND =========
							{
								//die("xxx");
								$data['barcodeMessage']= "Record Updated....";
								$this->vrs_model->updateRecord("v_contact_profile","rfid",$_POST['vbarcode'],"rep_code",$_POST["repcode"]);

								redirect("vrs/search/".$_POST["repcode"]);
								//$data['mmenu'] = "";
							}
				      }
					 else
					  {
				        foreach ($data['results'] as $r1)
				         {
						   $data['repcode'] = $r1['rep_code'];
				         }
					  }
				 }
            }
            #################################
            elseif($data['mmenu'] == "secondDayTAG")  // using scanner
			{
				//die("sss=".$data['vtype']);

				 $data["barcode"] = (isset($_POST['vbarcode']) ? trim($_POST['vbarcode']) : (isset($_GET['vbarcode']) ? trim($_GET['vbarcode']) : ""));
			 	 $data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where rfid='".$data['barcode']."' limit 1");

			     if($data['results']=="" || $data["barcode"]=="")
			      {
				    $data['barcodeMessage1']="Record not Found.....";
					$data['mmenu'] = "secondDay"; // return to scan module
			      }
				 else
				  {
					$this->vrs_model->updateRecord("v_contact_profile","remarks","DAY 2","rfid",$data["barcode"] );
					$data['mmenu'] = "secondDay";
					$data['barcodeMessage2']="Welcome back ".$data['results'][0]['salutation']." ".$data['results'][0]['cont_per_fn']." ".$data['results'][0]['cont_per_ln'];
					 //redirect("vrs/printLuxe/".$data["repcode"]."/".$data['mmenu']);
					 //redirect("vrs/printid/".$data["repcode"]."/".$data['mmenu']."/".$data['vtype']);
					 //die("aaa");
				  }

			}
			#################################
			elseif($data['mmenu'] == "StubforClaim")  // using scanner
			{
				//die("sss=".$data['vtype']);
				$data["barcode"] = (isset($_POST['vbarcode']) ? trim($_POST['vbarcode']) : (isset($_GET['vbarcode']) ? trim($_GET['vbarcode']) : ""));

				$xQRvalue = explode("|J",$data["barcode"]);

				 if(strtoupper($xQRvalue[0])=="BEGIN:EESY_PROFILE")
				 {
				 	$fn ="";$ln ="";$email ="";$comp ="";$ctry ="";$buss ="";$tel ="";$salutation ="";$qrFrmVRS="";
				 	$xval = count($xQRvalue);

				 	for ($x = 0; $x <= $xval-1; $x++) {

				 		//echo $xQRvalue[$x]."<br>";

				 		if(strtoupper($xQRvalue[$x]) <> 'BEGIN:EESY_PROFILE')
				 		{
				 			$zValue = explode(":",$xQRvalue[$x]);

				 			switch (strtoupper($zValue[0])) {
				 			case 'FN':
				 					$fn = $zValue[1];
				 				break;
				 			case 'LN':
				 					$ln = $zValue[1];
				 				break;
				 			case 'EMAIL':
				 					$email = $zValue[1];
				 				break;
				 			case 'COMPANY':
				 					$comp = $zValue[1];
				 				break;
				 			case 'COUNTRY':
				 					$ctry = $zValue[1];
				 				break;
				 			case 'TEL':
				 					$tel = $zValue[1];
				 				break;
				 			case 'PREFIX':
				 					$salutation = $zValue[1];
				 				break;
				 			case 'BUSINESS':
				 					$buss = $zValue[1];
				 				break;
				 			case 'VRS':
				 					$qrFrmVRS = $zValue[1];
				 				break;
				 			}
				 		}
				 	}

				 	  $recExist = $this->vrs_read_model->getRec("v_contact_profile","where cont_per_ln='".$ln."' and cont_per_fn='".$fn."' and co_name='".$comp."' and  email = ? limit 1",$email,'');

				 	  if($recExist=="")
				 	  	{
				 	  		$refId = $this->vrs_model->insertRecord("v_contact_profile");
				 	  		$barcodeValue = $refId;   // + 3000000;
					  		$vpid = $this->generatePID($data['fcode']);  //die("aaa = ".$pid);

					  		$encVal = $this->encryption->encrypt($vpid);
         					$data['pid'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);

				 	  		$vnum = $this->vrs_model->insertRecord("v_attendance");
				 	  	}
				 	  else
				 	  	{

				 	  		$refId = $recExist[0]["rep_code"];
				 	  		$barcodeValue = $recExist[0]["barcode"];
					  		$vpid = $recExist[0]["pid"];

					  		$encVal = $this->encryption->encrypt($vpid);
         					$data['pid'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);

					  		$attenExist = $this->vrs_read_model->getRec("v_attendance","where rep_code='".$refId."' and sector='".$data['sector']."' and  fair_code = ? limit 1",$data['fcode'],'');
					  		if($attenExist=="") {$vnum = $this->vrs_model->insertRecord("v_attendance");}
					  		else { $vnum = $attenExist[0]["refno"]; }

				 	  	}

						  $this->vrs_model->updateRecord("v_contact_profile","reg_status","T","rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","date_apply",date('Y-m-d H:i:s'),"rep_code",$refId);
						  // ==== update field marked = 1 for subserver use
						  $this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","venue",$data['myVenue'],"rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","barcode",$barcodeValue,"rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","pid",$vpid,"rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","trade_code",$data['fcode'],"rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","sector",$data['sector'],"rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","co_name",$comp,"rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","cont_per_fn",$fn,"rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","cont_per_ln",$ln,"rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","email",$email,"rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","mobile",$tel,"rep_code",$refId);
						  $vsalutation = ($salutation=="" ? "Mr." : $salutation);
						  $this->vrs_model->updateRecord("v_contact_profile","salutation",$vsalutation,"rep_code",$refId);
						  switch($vsalutation)
							 {
							   case "Mr." :
					             $this->vrs_model->updateRecord("v_contact_profile","gender","Male","rep_code",$refId);
							   break;
							   default:
					             $this->vrs_model->updateRecord("v_contact_profile","gender","Female","rep_code",$refId);
							 }

						  $vctry = ($ctry=="" ? "Philippines" : $ctry);
						  $this->vrs_model->updateRecord("v_contact_profile","country",$vctry,"rep_code",$refId);

						  if(trim($vctry)<>"")
						  	{
						  		$this->vrs_model->updateRecord("v_contact_profile","continent",$this->master_model->getContinent($vctry),"rep_code",$refId);
							}

						  $this->vrs_model->updateRecord("v_contact_profile","buyer_type","NEW","rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","visitor_type","GENERAL PUBLIC","rep_code",$refId);
						  $this->vrs_model->updateRecord("v_contact_profile","exhi_conf_gov","MOBILEAPP","rep_code",$refId);

						  // ==== update ATTENDANCE =======================
						  //$vnum = $this->vrs_model->insertRecord("v_attendance");
						  $this->vrs_model->updateAttendance("v_attendance",$refId,$data['fcode'],$data['sector'],$vnum);
						  $this->vrs_model->updateRecTable("v_attendance","barcode",$barcodeValue,$data['fcode'],$data['sector'],$refId);
						  $this->vrs_model->updateRecTable("v_attendance","visitor_status","NEW",$data['fcode'],$data['sector'],$refId);
						  $this->vrs_model->updateRecTable("v_attendance","visitor_type","GENERAL PUBLIC",$data['fcode'],$data['sector'],$refId);
						  // ==============================================

						  // ==== update representation ===================
						  if($buss<>"")
						  { //die("aaa=".$buss);
							 $vbustype = $this->master_model->loadRec("v_reference","where switch='B2' and sector='30' and c_profile='".$buss."' limit 1");

							 //=== delete record in sub table first =============
							 $this->vrs_model->deleteRecTable("v_representation",$refId,$data['fcode'],$data['sector']);

							 $this->vrs_model->insertRecTable("v_representation",$refId,$barcodeValue,$vbustype[0]['c_code'],$buss,$data['fcode'],"02");
						  }
					  // ==============================================

					$data["barcode"] = $barcodeValue;

				 }	//BEGIN:EESY_PROFILE


				 $data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where barcode='".$data["barcode"]."'");
			 	 $data['repcode'] = $data['results'][0]["rep_code"];

			 	 if ($data["barcode"]=="" || !ctype_digit($data["barcode"]))
			      {
			      	$data['mess_alert']="alert alert-danger text-center";
			      	$data['barcodeMessage1']="Invalid Entry.....";
					$data['mmenu'] = "assignID"; // return to scan module
			      }
			     elseif($data['results']=="")
			      {
				    //$data['mess_alert']="alert alert-warning text-center";
				    //$data['barcodeMessage1']="Record not Found, Inserting Record";
					$data['mmenu'] = "assignIDclaimStub"; // assignID before

					$data['rs1'] = $this->vrs_read_model->loadRec("v_wristband_code","where barcode='".$data["barcode"]."'");
					if($data['rs1']=="") {
						$data['repcode'] = $this->vrs_model->insertRecord("v_wristband_code");
					}
					else{
						$data['repcode'] = $data['rs1'][0]["refno"];
					}
					$this->vrs_model->updateRecord("v_wristband_code","barcode",$data["barcode"],"refno",$data['repcode']);
					$this->vrs_model->updateRecord("v_wristband_code","fair_code",$data["fcode"],"refno",$data['repcode']);
					$this->vrs_model->updateRecord("v_wristband_code","sector",$data["sector"],"refno",$data['repcode']);
					$this->load->view('vrs_main',$data);

			      }
				 else
				  {
					//$this->vrs_model->updateRecord("v_contact_profile","rfid",$_POST['vbarcode'],"rep_code",$_POST["repcode"]);
					//$data['mess_alert']="alert alert-success text-center";
					//$data['mmenu'] = "assignID";
					//$data['barcodeMessage1']="Wristband with ID ".$_POST['vbarcode']." was assigned to ".$data['results'][0]['salutation']." ".$data['results'][0]['cont_per_fn']." ".$data['results'][0]['cont_per_ln'];

					//redirect("vrs/search/".$_POST["repcode"]);
					//$this->load->view('vrs_main',$data);


					$data['mmenu'] = "assignIDclaimStub";
					$this->load->view('vrs_main',$data);
				  }

			}
			#######################################
            elseif($data['mmenu'] == "assignIDtag")  // using scanner
			{
				//die("sss=".$data['vtype']);

				 $data["barcode"] = (isset($_POST['vbarcode']) ? trim($_POST['vbarcode']) : (isset($_GET['vbarcode']) ? trim($_GET['vbarcode']) : ""));
				 $data["sector"] = (isset($_POST['sector']) ? trim($_POST['sector']) : (isset($_GET['sector']) ? trim($_GET['sector']) : ""));

			 	 $data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where rep_code='".$_POST["repcode"]."'");

			 	 $data['repcode'] = $_POST["repcode"];

			 	 if ($data["barcode"]=="")
			      {
			      	$data['mess_alert']="alert alert-danger text-center";
			      	$data['barcodeMessage1']="Invalid Entry.....";
					$data['mmenu'] = "assignID"; // return to scan module
			      }
			     elseif($data['results']=="")
			      {
				    $data['mess_alert']="alert alert-warning text-center";
				    $data['barcodeMessage1']="Wristband with ID ".$_POST['vbarcode']." was assigned to ".$data['repcode'];

				    $this->vrs_model->updateRecord("v_wristband_code","item_code",$_POST['vbarcode'],"refno",$data['repcode']);

					$data['mmenu'] = "assignID"; // return to scan module
			      }
				 else
				  { 		//die("aaa");
					$this->vrs_model->updateRecord("v_contact_profile","rfid",$_POST['vbarcode'],"rep_code",$data['repcode']);
					$this->vrs_model->updateRecord("v_contact_profile","reg_status","T","rep_code",$data['repcode']);
					$this->vrs_model->updateRecord("v_contact_profile","date_apply",date('Y-m-d H:i:s'),"rep_code",$data['repcode']);
					$this->vrs_model->updateRecord("v_contact_profile","visitor_type","GENERAL PUBLIC","rep_code",$data['repcode']);

					//$vnum = $this->vrs_model->insertRecord("v_attendance");
					//$this->vrs_model->updateRecTable("v_attendance","visitor_type","GENERAL PUBLIC",$data['fcode'],$data['sector'],$data['repcode']);

					$data['chkAttendance']= $this->vrs_read_model->getRec("v_attendance","where fair_code='".$data['fcode']."' and rep_code = ? limit 1",$data['repcode'],"");
		  			  if ($data['chkAttendance']=="")
		  		      {
		  	            $this->vrs_model->insertRecTable('v_attendance',$data['repcode'],$data['results'][0]['barcode'] ,'','',strtoupper($data['fcode']),$data['sector']);
		  		      }

					$this->vrs_model->updateRecTable('v_attendance','date_apply',date('Y-m-d H:i:s'),strtoupper($data['fcode']),$data['sector'],$data['repcode']);
		  			$this->vrs_model->updateRecTable("v_attendance","visitor_type","GENERAL PUBLIC",$data['fcode'],$data['sector'],$data['repcode']);

					$data['mess_alert']="alert alert-success text-center";
					$data['barcodeMessage1']="Wristband with ID ".$_POST['vbarcode']." was assigned to ".$data['results'][0]['salutation']." ".$data['results'][0]['cont_per_fn']." ".$data['results'][0]['cont_per_ln'];
					$data['mmenu'] = "assignID";
					//redirect("vrs/search/".$_POST["repcode"]);
				  }

			}
			########## END RAFFLE ###########
            #################################
			#     LUXE RAFFLE TICKET        #
			#################################
			elseif($data['mmenu'] == "barcodeTicketSearch")  // using scanner
			{
				//die("sss=".$data['vtype']);

				 $data["barcode"] = (isset($_POST['vbarcode']) ? trim($_POST['vbarcode']) : (isset($_GET['vbarcode']) ? trim($_GET['vbarcode']) : ""));

				 $xQRvalue = explode("|J",$data["barcode"]);

			   //if(strtoupper($xQRvalue[0])=="BEGIN:EESY_PROFILE")
				 if(strtoupper($xQRvalue[0])=="BEGIN:VCARD") {}

			 	 $data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where barcode='".$data['barcode']."' order by co_name");


			     if($data['results']=="" || $data["barcode"]=="")
			      {
				    $data['barcodeMessage']="Record not Found";
					$data['mmenu'] = "ticket"; // return to scan module
			      }
				 else
				  {
			        foreach ($data['results'] as $r1)
			         {
					   $data['repcode'] = $r1['rep_code'];

					   if($r1['visitor_type']=='TRADE BUYER') {$data['vtype']='TB';}
				       else if($r1['visitor_type']=='GENERAL PUBLIC') {$data['vtype']='GP';}
				       else {$data['vtype']=$r1['visitor_type'];}
			         }
					 $data['mmenu'] = "printticket";
					 $data['barcodeMessage']="";
					 //redirect("vrs/printLuxe/".$data["repcode"]."/".$data['mmenu']);
					 redirect("vrs/printid/".$data["repcode"]."/".$data['mmenu']."/".$data['vtype']);
					 //die("aaa");
				  }

			}
			########## END RAFFLE ###########

			#################################
			#     Raffle Draw Entry         #
			#################################
			elseif($data['mmenu'] == "raffleTicketSearch")  // using scanner
			{

				//die("sss");
				 $data["barcode"] = (isset($_POST['vbarcode']) ? trim($_POST['vbarcode']) : (isset($_GET['vbarcode']) ? trim($_GET['vbarcode']) : ""));
			 	 $data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where barcode='".$data['barcode']."' order by co_name");

			 	 $data['mmenu'] = "raffledraw"; // return to RAFFLE DRAW INCLUSION

			     if($data['results']=="" || $data["barcode"]=="")
			      {
				    $data['barcodeMessage']="(bummer)...Record not Found";
			      }
				 else
				  {
			        foreach ($data['results'] as $r1)
			         {
					   $data['repcode'] = $r1['rep_code'];
			         }

			         $this->vrs_model->updateRecord("v_contact_profile","tag","1","rep_code",$data["repcode"]);
			         $this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$data["repcode"]);
			         $data['barcodeMessage']="Successfully Added in Raffle";
				  }
				 //$this->load->view('vrs_main',$data);
			}
			########## END Raffle Draw Entry ###########

			elseif($data['mmenu'] == "RFIDSearch")  // using scanner
			{

				//die("sss");
				 $data["repcode"] = (isset($_POST['repcode']) ? trim($_POST['repcode']) : (isset($_GET['repcode']) ? trim($_GET['repcode']) : ""));
				 $data["vrfid"] = (isset($_POST['vbarcode']) ? trim($_POST['vbarcode']) : (isset($_GET['vbarcode']) ? trim($_GET['vbarcode']) : ""));
			 	 $data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where rep_code='".$data['repcode']."' order by co_name limit 1");
			     $data['rsRFID'] = $this->vrs_read_model->loadRec("v_contact_profile","where rfid='".$data['vrfid']."' order by co_name limit 1");	//check if rfid was previous assigned

			     if($data['rsRFID']<>"")
			      {
			     	foreach ($data['rsRFID'] as $r1)
			         {
					   $data['repcode2'] = $r1['rep_code'];
			         }
			      }
			 	 $data['mmenu'] = "Assignrfid"; // return to Assignrfid in vrs_main

			     if($data['results']=="" || $data["vrfid"]=="")
			      {
				    $data['barcodeMessage']="(bummer)...No CARD Detected";
			      }
				 else
				  {
			     //    foreach ($data['results'] as $r1)
			     //     {
					   // $data['repcode'] = $r1['rep_code'];
			     //     }
				  	 if($data['rsRFID']<>"")
				  	  {
			         	$this->vrs_model->updateRecord("v_contact_profile","rfid","","rep_code",$data["repcode2"]);      				//clear previous RFID
			         	$this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$data["repcode2"]); 	//Tag marked field to update live
			          }

			         $this->vrs_model->updateRecord("v_contact_profile","rfid",$data["vrfid"],"rep_code",$data["repcode"]);	       		//assign new RFID
			         $this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$data["repcode"]); 		//Tag marked field to update live
			         //$data['barcodeMessage']="Successfully Assigned RFID";
			         echo "<script type='text/javascript'>
			         alert('Successfully Assigned RFID');
			         window.location='".site_url("vrs/search/".$data['repcode'])."';
					</script>";
				  }
				 //$this->load->view('vrs_main',$data);

			}

			elseif($data['mmenu'] == 'mobileappSearch')
			{
				//die("hmmm");
				$this->vrs_model->updateRecord("v_contact_profile","visitor_type",$_POST["visitorType"],"rep_code",$_POST["repcode"]);
				$this->vrs_model->updateRecTable("v_attendance","visitor_type",$_POST["visitorType"],$data['fcode'],$data['sector'],$_POST["repcode"]);
				$this->vrs_model->updateRecTable("v_attendance","remarks","MOBILEAPP",$data['fcode'],$data['sector'],$_POST["repcode"]);

				if($_POST["visitorType"]=="GENERAL PUBLIC") { $this->vrs_model->updateRecord("v_contact_profile","reg_status","F","rep_code",$_POST["repcode"]);}

				redirect("vrs/search/".$_POST["repcode"]);
			}

			else

			{
			 // =================== LOAD Records ==============================================================================================

			 $data["repcode"] = (isset($_POST['repcode']) ? trim($_POST['repcode']) : (isset($_GET['repcode']) ? trim($_GET['repcode']) : ""));

			 //$data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where rep_code='".$data['repcode']."' order by co_name");

				$qry1 = "SELECT a.rep_code,a.barcode,a.rfid,attendance.reg_status as reg_status,attendance.pre_reg,a.co_name,a.cont_per_ln,a.cont_per_fn,attendance.visitor_type as visitor_type,attendance.visitor_status as buyer_type,a.deleted,attendance.buyerclass,a.country,a.add_st,a.email,a.position,a.add_city,a.zipcode,a.continent,a.tel_off,a.mobile,a.webpage,a.sector,a.gender,a.age_group,a.salutation,a.cutoff,a.email2,a.venue,a.gcclaimed,a.date_apply,a.remarks,a.add_value,a.tag,a.pid,a.trade_code,attendance.item_code as promoCode,attendance.remarks as isPaid
				FROM v_contact_profile AS a
				LEFT JOIN
				( SELECT rep_code,v_attendance.visitor_type,fair_code,reg_status,pre_reg,buyerclass,visitor_status,item_code,remarks FROM v_attendance WHERE fair_code='".$data['fcode']."' ORDER BY refno DESC) as attendance
				ON a.rep_code = attendance.rep_code
				WHERE a.rep_code ='".$data['repcode']."' GROUP BY a.rep_code ORDER BY a.co_name, a.cont_per_ln";

				$data['results'] = $this->vrs_read_model->loadRec2($qry1);

			//die("zzz=".$data["mmenu"]." ==".$data["repcode"]);
			// =================== LOAD Records ==============================================================================================
			}

			//die("xxx=".$data["mmenu"]);
			##################################################################################################################################################

	        if($data['results'] == "" && $data['mmenu']<>"assignID" && $data['mmenu']<>"add_company" && $data['mmenu']<>"scan" && $data['mmenu']<>'openForm' && $data['mmenu']<>"ticket" && $data['mmenu']<>"raffledraw" && $data['mmenu']<>"secondDay" && $data['mmenu']<>"assignIDclaimStub")  ##### INSERT $data['mmenu']<>"ticket"
		  	//if($data['results'] == "" && ($data['mmenu']<>"add_company" || $data['mmenu']<>"scan" || $data['mmenu']<>"Search"))
			 {
			  //die(site_url());
			  echo "<script type='text/javascript'>
			         alert('No Record Selected');
			         window.location='".site_url("vrs/search/".$data['vurl'])."';
			         //window.location='".site_url("vrs/main/norec")."';
					</script>";
			 }

			if($data['mmenu'] == "Assignrfid")
			 {
			   $data['mmenu'] = "Assignrfid";

			   foreach ($data['results'] as $r1)
			       	{
			        	$data['vdeleted'] = $r1['deleted'];
					}
			   if($data['vdeleted']== '1')
                   {
                    echo "<script type='text/javascript'>
			               alert('Record is Deleted');
						   window.location='".site_url("vrs/search/".$data["repcode"])."';
		                  </script>";
                   }

			   $data['repcode'] = $this->input->post('repcode');
			   $this->load->view('vrs_main',$data);

			 }
			if($data['mmenu'] == "edit2" || $data['mmenu'] == "add_person2")
			 {

			  foreach($data['results'] as $re1)
			  	{   //die("waxx");

			  		if($re1['deleted']== '1')
                   	{
                    echo "<script type='text/javascript'>
			               alert('Record is Deleted');
						   window.location='".site_url("vrs/search/".$data["repcode"])."';
		                  </script>";
                   	}
			   		//$data['pid'] = $this->encrypt2->encode2($re1['pid']);  // change PID to REP_CODE na

			   		$pid = $this->encryption->encrypt($re1['pid']);
        			$data['pid'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $pid);



			   		$data['vtype1'] = $re1['visitor_type'];
			   		if($data['vtype1']=='TRADE BUYER') {$data['vtype1']='TB'; $data['fcode1']=$data['fcode'];}
				    else if($data['vtype1']=='GENERAL PUBLIC') {$data['vtype1']='GP'; $data['fcode1']=$data['fcode']."-GP";  }
				    //else if($data['vtype1']=='VISITOR') {$data['vtype1']='VISITOR'; $data['fcode1']=$data['fcode']."-VISITOR";  }
				    else if($data['vtype1']=='GUEST') {$data['vtype1']='GUEST'; $data['fcode1']=$data['fcode']."-GUEST";  }
				    else if($data['vtype1']=='MEDIA') {$data['vtype1']='MEDIA'; $data['fcode1']=$data['fcode']."-MEDIA";  }
				    else
				    	{
				    		if($data['default_form_type']=='TRADE BUYER') {$data['vtype1']='TB'; $data['fcode1']=$data['fcode'];}
				    		else if($data['default_form_type']=='GENERAL PUBLIC') {$data['vtype1']='GP'; $data['fcode1']=$data['fcode']."-GP";  }
				    		//else if($data['default_form_type'] =='VISITOR') {$data['vtype1']='VISITOR'; $data['fcode1']=$data['fcode']."-VISITOR";  }
				    		else if($data['default_form_type'] =='GUEST') {$data['vtype1']='GUEST'; $data['fcode1']=$data['fcode']."-GUEST";  }
				    		else if($data['default_form_type']=='MEDIA') {$data['vtype1']='MEDIA'; $data['fcode1']=$data['fcode']."-MEDIA";  }
				    		//else {$data['vtype1']='VISITOR'; $data['fcode1']=$data['fcode']."-VISITOR";  }
				    		else {$data['vtype1']='GUEST'; $data['fcode1']=$data['fcode']."-GUEST";  }
				    	}

				}
			   $data['mmenu'] = $data['mmenu'];  			//"add_person2"
			   $this->load->view('vrs_main',$data);
			 }


			if($data['mmenu'] == "mobileapp")
			 {
			   $data['RegType']=$this->master_model->loadRec("v_reference","where switch='BSTAT' and sector like '%".$data['sector']."%' order by sortfield");
			   $data['mmenu'] = "mobileapp";
			   $this->load->view('vrs_main',$data);
			 }

			if($data['mmenu'] == "scan")
			 {
			   $data['mmenu'] = "scan";
			   $this->load->view('vrs_main',$data);
			 }

			if($data['mmenu']== "openForm")
			 {
			   $data['mmenu'] = "openForm";
			   $this->load->view('vrs_main',$data);
			 }

			if($data['mmenu'] == "secondDay")
			 {
			   $data['mmenu'] = "secondDay";
			   $this->load->view('vrs_main',$data);
			 }
			if($data['mmenu'] == "assignID")
			 {
			   $data['mmenu'] = "assignID";
			   $this->load->view('vrs_main',$data);
			 }
			########################################
			#   RETURN TO RAFFLE DATA TO vrs_main  #
			########################################
			if($data['mmenu'] == "ticket")
			 {
			   $data['mmenu'] = "ticket";
			   $this->load->view('vrs_main',$data);
			 }

			if($data['mmenu'] == "raffledraw")
			 {
			   $data['mmenu'] = "raffledraw";
			   $this->load->view('vrs_main',$data);
			 }

			############ END ######################
			if($data['mmenu'] == "print")
			 {

				  //redirect("vrs/main/".$data["repcode"]);
				  foreach ($data['results'] as $r1)
			       {
			        $data['vMark'] = $r1['reg_status'];
					$data['vdeleted'] = $r1['deleted'];
					//====== DIY use ====================
					$data['vtype'] = $r1['visitor_type'];
					$data['pid'] = $r1['pid'];
					$data['barcode'] = $r1['barcode'];
					//===================================

					//$vsalutation = (trim($r1['salutation'])=="" ? "\\n - Title/Salutation" : "");
					$vsalutation = "";
					$vcont_per_fn = (trim($r1['cont_per_fn'])=="" ? "\\n - First Name" : "");
					$vcont_per_ln = (trim($r1['cont_per_ln'])=="" ? "\\n - Last Name" : "");
					$vcountry = (trim($r1['country'])=="" ? "\\n - Country" : "");
					$vemail = (trim($r1['email'])=="" ? "\\n - Email" : "");

					//if($data['vtype']=='GENERAL PUBLIC' || $data['vtype']=='GP' || $data['vtype']=='VISITOR')
					if($data['vtype']=='GENERAL PUBLIC' || $data['vtype']=='GP' || $data['vtype']=='GUEST' || $data['vtype']=='MEDIA')
					{	$vco_name = ""; }
					else{
					 	$vco_name = (trim($r1['co_name'])=="" ? "\\n - Company Name" : "");
					}
					$vvisitor_type = (trim($r1['visitor_type'])=="" ? "\\n - Visitor Type" : "");

			       }
				  //====== DIY use ========================================
				  //$getIP = explode('.',$_SERVER['REMOTE_ADDR']);
				  //$data['diyTerminal'] = $getIP[3];
				  if($data['vtype']=='TRADE BUYER') {$data['vtype']='TB';}
				  if($data['vtype']=='GENERAL PUBLIC') {$data['vtype']='GP';}
				  //=======================================================

				  if($data['vdeleted']== '1')
                   {
                    echo "<script type='text/javascript'>
			               alert('Record is Deleted');
						   window.location='".site_url("vrs/search/".$data["repcode"])."';
		                  </script>";
                   }

				 //die("xxx = ".$data['vMark']);

				  else if($data['vMark']=="F" || $data['vMark']=="")
				   {
					echo "<script type='text/javascript'>
			               alert('Record not Arrived');
						   window.location='".site_url("vrs/search/".$data["repcode"])."';
		                  </script>";
				   }
				  else if($vsalutation<>'' || $vcont_per_ln<>'' || $vcont_per_fn<>'' || $vco_name<>'' || $vcountry<>'' || $vemail<>'' || $vvisitor_type<>'' )
				   {
				   		//if($data['vtype']=='VISITOR') {
				   		 if($data['vtype']=='GUEST') {
				   			if($vsalutation<>'' || $vcont_per_ln<>'' || $vcont_per_fn<>'' || $vcountry<>'' || $vemail<>'' || $vvisitor_type<>'')
				   				{	//die("xxxx");
				   					echo "<script type='text/javascript'>
		                  			alert('The following are required: \\n".$vsalutation.$vcont_per_ln.$vcont_per_fn.$vcountry.$vemail.$vvisitor_type."');
					      			window.location='".site_url("vrs/search/".$data["repcode"])."';
	                      			</script>";
				   				}
				   			else { redirect("vrs/printid/".$data["repcode"]."/".$data['mmenu']."/".$data['vtype']); }

				   		}
				   		else {	//die("zzz");
				   			echo "<script type='text/javascript'>
		                  		alert('The following are required: \\n".$vsalutation.$vcont_per_ln.$vcont_per_fn.$vco_name.$vcountry.$vemail.$vvisitor_type."');
					      		window.location='".site_url("vrs/search/".$data["repcode"])."';
	                      		</script>";
				   		}
				   }
				  else
				   {
				    //die("xxx = ".$data['vMark']);
				    redirect("vrs/printid/".$data["repcode"]."/".$data['mmenu']."/".$data['vtype']);
				    //$this->load->view('vrs_main',$data);	 //die();
				   }
			 }
			//========================================================================================
			if($data['mmenu'] == "mark" || $data['mmenu'] == "barcodeSearch")  // update field marked = 1 for subserver use
			 {    //die("aaa=".$this->uri->segment(4));

			 	  //$printGPsticker = 'PrintSticker';	 // use if stub is scanned "SCan QRcode/Barcode" sticker will be printed

			      foreach ($data['results'] as $r1)
			       {
			        $vMark = ($r1['reg_status']=="" ? "F" : $r1['reg_status']); //die("aa=".$data["repcode"]);
					$vdeleted = $r1['deleted'];
					//$vsalutation = (trim($r1['salutation'])=="" ? "\\n - Title/Salutation" : "");
					$vsalutation = "";
					$vcont_per_fn = (trim($r1['cont_per_fn'])=="" ? "\\n - First Name" : "");
					$vcont_per_ln = (trim($r1['cont_per_ln'])=="" ? "\\n - Last Name" : "");
					$vcountry = (trim($r1['country'])=="" ? "\\n - Country" : "");
					$vemail = (trim($r1['email'])=="" ? "\\n - Email" : "");  						//  "\\n - Email"

					//==================== DEFAULT ====================================================
					$vbuyer_type = (trim($r1['buyer_type'])=="" ? "\\n - Visitor Status" : "");

					$vbuyer_type = ""; // for CIIE2018 only remove after EVENT
					//=================================================================================

					$vvisitor_type = (trim($r1['visitor_type'])=="" ? "\\n - Visitor Type" : "");

					$vcutoff	= $r1['cutoff'];

					$xemail1 = $r1['email'];
					$xemail2 = $r1['email2'];

					$data['barcode'] = $r1['barcode'];
					$data['prereg'] = $r1['pre_reg'];
					//====== DIY use ====================
					$data['vtype'] = $r1['visitor_type'];
					$data['pid'] = $r1['pid'];
					//===================================

					//if($data['vtype']=='GENERAL PUBLIC' || $data['vtype']=='GP' || $data['vtype']=='VISITOR')
					if($data['vtype']=='GENERAL PUBLIC' || $data['vtype']=='GP' || $data['vtype']=='GUEST' || $data['vtype']=='MEDIA')
					{	$vco_name = ""; }
					else{
					 	$vco_name = (trim($r1['co_name'])=="" ? "\\n - Company Name" : "");
					}
			       }
			      //====== check if record is local visitor but pre-reg... message to proceed to paying counter ========


			        $data['chkIF_Paying'] = "no";

			        //====================================================================================================================================================
			        //================ DETERMINE PRE-REG AS GENERAL PUBLIC ===============================================================================================
			        //====================================================================================================================================================
			        if(($data['vtype']=="GENERAL PUBLIC" || $data['vtype']=="GP") && $data['prereg']=='P' && $data['mmenu'] == "barcodeSearch" && $data['sector'] <> "20")
			        {
			        	//redirect("vrs/searchforpaying/".$data["repcode"]);
			        }
			        //====================================================================================================================================================
			        //====================================================================================================================================================
			        //====================================================================================================================================================

			      //====================================================================================================

				  //====== DIY use ========================================
				  //$getIP = explode('.',$_SERVER['REMOTE_ADDR']);
				  //$data['diyTerminal'] = $getIP[3];
				  if($data['vtype']=='TRADE BUYER') {$data['vtype']='TB';}
				  if($data['vtype']=='GENERAL PUBLIC') {$data['vtype']='GP';}
				  //=======================================================
                  if($vdeleted == '1')
                   {
                    echo "<script type='text/javascript'>
			               alert('Record is Deleted');
						   window.location='".site_url("vrs/search/".$data["repcode"])."';
		                  </script>";
                   }
				  else
				   {
				    if($vMark=="F" || $data['mmenu'] == "barcodeSearch")
					 { //die("xxx");
					  if ($vsalutation=='' && $vcont_per_ln=='' && $vcont_per_fn=='' && $vco_name=='' && $vcountry=='' && $vemail=='' && $vbuyer_type=='' && $vvisitor_type=='')
					   {

					   		 //======= CREATE CSV for pointwest APP ===================================================
					         //======= CREATE CSV for pointwest APP ===================================================
					   		  if($data['vtype']=='TRADE BUYER' || $data['vtype']=='TB')
					   		  {
					            //$this->createcsv($xemail1,$xemail2,$data['barcode']);
					      	  }
					         //========================================================================================
					         //========================================================================================


						     $this->vrs_model->updateRecord("v_contact_profile","reg_status","T","rep_code",$data["repcode"]);
						     $this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$data["repcode"]);
							 $this->vrs_model->updateRecord("v_contact_profile","trade_code",$data['fcode'],"rep_code",$data["repcode"]);

							 $this->vrs_model->updateRecord("v_contact_profile","venue",$data['myVenue'],"rep_code",$data["repcode"]);

							 $vStation = $data['diyTerminal']; // + 1000;
							// $this->vrs_model->updateRecord('v_contact_profile','rfid',$vStation,"rep_code",$data["repcode"]); // ======== temp station number

							 if ($vcutoff<>'1' || $vcutoff === null )
							  { //die("xxx");
							   $this->vrs_model->updateRecord("v_contact_profile",'date_apply',date('Y-m-d H:i:s'),"rep_code",$data["repcode"]);

							   $chkAttend = $this->vrs_read_model->loadRec2("select * from v_attendance where sector='".$data['sector']."' and fair_code='".$data['fcode']."' and rep_code = ".$data["repcode"]);
							   if($chkAttend<>"")
							   	 {
							        $this->vrs_model->updateRecTable('v_attendance','date_apply',date('Y-m-d H:i:s'),strtoupper($data['fcode']),$data['sector'],$data["repcode"]);

							        $this->vrs_model->updateRecTable('v_attendance','reg_status','T',strtoupper($data['fcode']),$data['sector'],$data["repcode"]);
							     }
							   	     //die("aaa= ".$uResult);
							   else  //if ($this->db->affected_rows()==0)
							   	 {
							   	 	$this->vrs_model->insertAttendance('v_attendance',$data["repcode"],$data['barcode'],trim($r1['buyer_type']),trim($r1['visitor_type']),trim($r1['buyerclass']),$data['fcode'],$data['sector']);

							   	 	$this->vrs_model->updateRecTable('v_attendance','reg_status','T',strtoupper($data['fcode']),$data['sector'],$data["repcode"]);

							   	 }

							   	//====== added 6/9/2022============= add GENERIC code

							   	 	$chkSectorName = $this->vrs_read_model->loadRec2("select * from v_attendance where sector='".$data['sector']."' and fair_code='".$data['sectorName']."' and rep_code = ".$data["repcode"]);
							   	 	if($chkSectorName=="")
							   	 	{
							   	 		$this->vrs_model->insertAttendance('v_attendance',$data["repcode"],$data['barcode'],trim($r1['buyer_type']),trim($r1['visitor_type']),trim($r1['buyerclass']),$data['sectorName'],$data['sector']);
							   	 	}
							   	//===================================
							   	//===================================   MUST include FAIRCODE in child table
							   	//===================================   if marked Arrived
							   	//===================================

							   //if ($vsucceed) {die("yes");} else{die("no");}
							  }

							  //die("zz= ".$_POST['wristband']);
							 if ($data['sector'] == "20" && $_GET['wristband']=="yes" )
							 	{
							 		$data['barcodeMessage']= "";
							 		$data['mmenu']='scan';
							 		$data['getWristbandCode'] = "yes";
							 		//redirect("vrs/search/".$data["repcode"]);
							 		//redirect('vrs/index');
							 		$this->load->view('vrs_main',$data);
							 	}
							 else
							    { 	// ======= redirect to this when also barcodeSearch ============================
							    	//die("vrs/printid/".$data["repcode"]."/".$data['mmenu']."/".$data['vtype']);


							    	redirect("vrs/printid/".$data["repcode"]."/".$data['mmenu']."/".$data['vtype']."/".$data['chkIF_Paying']);
							    	//redirect("vrs/printid/".$data["repcode"]."/"."scan"."/".$data['vtype']."/".$data['chkIF_Paying']);
							    }

					   }
					  else
					   {
					     echo "<script type='text/javascript'>
			                  alert('The following are required: \\n".$vsalutation.$vcont_per_ln.$vcont_per_fn.$vco_name.$vcountry.$vemail.$vbuyer_type.$vvisitor_type."');
						      window.location='".site_url("vrs/search/".$data["repcode"])."';
		                      </script>";
					   }
					 }
				    else
					 {
					  if($vcutoff==1 && $data['sessionRights']<>1)
					  	{
					  		echo "<script type='text/javascript'>
			                  alert('Cannot Unmarked. Already included in CUT-OFF. Check with Administrator');
			                  window.location='".site_url("vrs/search/".$data["repcode"])."';
		                      </script>";
					  	}
					  else
					  	{


					  		if(isset($_GET['payingGP'])) { $vpayingGP= $_GET['payingGP']; } else { $vpayingGP="";  }

					  		//if(!isset($_GET['payingGP']) && $_GET['payingGP']<>"print")   // will not unmark if barcode scanned again

					  		if($vpayingGP<>"print")											// will not unmark if barcode scanned again !!!CHECK THIS
					  		{
						  		$vStation = $data['diyTerminal']; //+ 1000;
							 	//$this->vrs_model->updateRecord('v_contact_profile','rfid',$vStation,"rep_code",$data["repcode"]); // ======== temp station number
						  		$this->vrs_model->updateRecord("v_contact_profile","reg_status","F","rep_code",$data["repcode"]);
						  		$this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$data["repcode"]);
						  		$this->vrs_model->updateRecord("v_contact_profile",'date_input',date('Y-m-d H:i:s'),"rep_code",$data["repcode"]);
						  		//$this->vrs_model->updateRecTable('v_attendance','date_input',date('Y-m-d H:i:s'),strtoupper($data['fcode']),$data['sector'],$data["repcode"]);

						  		//$this->vrs_model->deleteRecTable("v_attendance",$data["repcode"],strtoupper($data['fcode']),$data['sector']);
						  		//$this->vrs_model->deleteRecTable("v_attendance",$data["repcode"],strtoupper($data['sectorName']),$data['sector']);

						  		$this->vrs_model->updateRecTable('v_attendance','reg_status','F',strtoupper($data['fcode']),$data['sector'],$data["repcode"]);
						  		$this->vrs_model->updateRecTable('v_attendance','date_apply',date('Y-m-d H:i:s'),strtoupper($data['fcode']),$data['sector'],$data["repcode"]);

					  		}
					  		redirect("vrs/search/".$data["repcode"]);
					  	}
					 }
				   }
			 } //($data['mmenu'] == "mark")

			if($data['mmenu'] == "raffleClaimed")
			 {
			 	  foreach ($data['results'] as $r1)
			       {
			        $vclaimed = $r1['gcclaimed']; //die($sector);
			       }

				   if($vclaimed=="0") {$this->vrs_model->updateRecord("v_contact_profile","gcclaimed","1","rep_code",$data["repcode"]);}
				   else {$this->vrs_model->updateRecord("v_contact_profile","gcclaimed","0","rep_code",$data["repcode"]);}

				   $this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$data["repcode"]);

				   redirect("vrs/search/".$data["repcode"]);
			 }
			//========================================================================================
			if($data['mmenu'] == "DelRestore")  // update field marked = 2 for subserver use
			 {  //die("ssss");
			      foreach ($data['results'] as $r1)
			       {
			        $vDel = $r1['deleted']; //die($sector);
			        $vCutoff = $r1['cutoff'];
			       }
			      $refId = $this->input->post('repcode');
			     // die("aaa= ".$refId);
			    //   if($refId=="")
			    //   	{	echo "<script type='text/javascript'>
			    //      		alert('No Record Selectedz');
			    //      		window.location='".site_url("vrs/search/".$data['vurl'])."';
							// </script>";

				  	// }

			      if($vCutoff==1 && $data['sessionRights']<>1)
			      	{
			      		echo "<script type='text/javascript'>
			                  alert('Cannot Delete/Restore Record. Already included in CUT-OFF. Check with Administrator');
			                  window.location='".site_url("vrs/search/".$refId)."';
		                      </script>";
				  	}
				  else
				  	{
				  		if($vDel=="0") {$this->vrs_model->updateRecord("v_contact_profile","deleted","1","rep_code",$refId);}
				  		else {$this->vrs_model->updateRecord("v_contact_profile","deleted","0","rep_code",$refId);}

				  		$this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$refId);

				  		redirect("vrs/search/".$refId);
				  	}
			 }

			//========================================================================================

			// if($data['mmenu'] == "Assignrfid")  // update field marked = 2 for subserver use
			//  {  //die("ssss");
			//       foreach ($data['results'] as $r1)
			//        {
			//         $vDel = $r1['deleted']; //die($sector);
			//         //$vCutoff = $r1['cutoff'];
			//        }
			//       $refId = $this->input->post('repcode');

			//       if($data['sessionRights']<>1)
			//       	{
			//       		echo "<script type='text/javascript'>
			//                   alert('Cannot Delete/Restore Record. Already included in CUT-OFF. Check with Administrator');
			//                   window.location='".site_url("vrs/search/".$refId)."';
		 //                      <!/script>";
			// 	  	}
			// 	  else
			// 	  	{
			// 	  		if($vDel=="0") {$this->vrs_model->updateRecord("v_contact_profile","deleted","1","rep_code",$refId);}
			// 	  		else {$this->vrs_model->updateRecord("v_contact_profile","deleted","0","rep_code",$refId);}

			// 	  		$this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$refId);

			// 	  		redirect("vrs/search/".$refId);
			// 	  	}
			//  }

			$data['countryList']=$this->master_model->loadRec("v_reference","where switch='R1' order by c_profile");
			$data['TitleList']=$this->master_model->loadRec("v_reference","where switch='MN9' order by c_profile");
			$data['RegStatus']=$this->master_model->loadRec("v_reference","where switch='MN6' order by c_profile");
			$data['RegType']=$this->master_model->loadRec("v_reference","where switch='BSTAT' and sector like '%".$data['sector']."%' order by sortfield");
			$data['RegClass']=$this->master_model->loadRec("v_reference","where switch='CLASS' AND sector like '%".$data['sector']."%'order by sortfield");
            // === get fieldnames to display ==================================================================
			$fields = $this->db->field_data('v_contact_profile');
			//======================================================================================================
            foreach ($fields as $field) //echo $field->name; echo $field->type; echo $field->max_length; echo $field->primary_key;
            {
			  // ===== load form DB =========
			  if($data['results']<>"")
			  {
			    foreach ($data['results'] as $r1)
			     {
			     	if(isset($r1[$field->name])) {
			        	$data[$field->name] = $r1[$field->name];
			    	}
			     }
				//$data['notfound']="0";
		  	  }
			  //================
			  if($data['mmenu'] == "edit" || $data['mmenu'] == "edit2" || $data['mmenu'] == "add_person2")
			   {
			    $data['v'.$field->name] = ($this->input->post($field->name) == "" ? (isset($data[$field->name]) ? $data[$field->name] : '') : $this->input->post($field->name));
			    if($field->name == "pid")
			    	{
			    		//$data['pid_ENC'] = $this->encrypt2->encode2($data['v'.$field->name]);

			    		$pid = $this->encryption->encrypt($data['v'.$field->name]);
        				$data['pid_ENC'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $pid);

			    	}

				//if($field->name == "visitor_type") {die("sss=".$data['vvisitor_type']);}

			   }
			  if($data['mmenu'] == "add_person")  //add_person
			   {
			     if($field->name=='co_name' || $field->name=='rep_code' || $field->name=='country')
				  {
			       $data['v'.$field->name] = ($this->input->post($field->name) == "" ? (isset($data[$field->name]) ? $data[$field->name] : '') : $this->input->post($field->name));
				  }
				 else
				  {$data['v'.$field->name] = ($this->input->post($field->name) == "" ? "" : $this->input->post($field->name));}
			   }
			  if($data['mmenu'] == "add_company")  //add_company
			   {
				//$data['v'.$field->name] = "";
				  $data['v'.$field->name] = ($this->input->post($field->name) == "" ? "" : $this->input->post($field->name));
			   }

			} //foreach fields
			//====================================================================================

		    if($this->input->post('btn_submit')=='update') // ================== using vrs_modifyrec
			  { //die("aaa= ".$this->input->post('visitor_type'));
			  	$vrequired = ( $this->input->post('visitor_type')<>"VISITOR" ? "|required" : "|required"); // $this->input->post('visitor_type')=="MEDIA"

			  	$vrequired = ( $this->input->post('visitor_type')<>"GUEST" ? "|required" : "|required");

			  	$this->form_validation->set_error_delimiters('<div style="color:#000000; background-color:#FF6666;">', '</div>');
		        $this->form_validation->set_rules('salutation', 'Title', 'trim|required');
		        $this->form_validation->set_rules('cont_per_fn', 'First Name', 'trim|required');
				$this->form_validation->set_rules('cont_per_ln', 'Last Name', 'trim|required');
				$this->form_validation->set_rules('co_name', 'Company Name', 'trim|required');
				$this->form_validation->set_rules('country', 'Country', 'trim|required');
				$this->form_validation->set_rules('email', 'Email', 'trim|valid_email'.$vrequired);
				$this->form_validation->set_rules('buyer_type', 'Status', 'trim|required');
				$this->form_validation->set_rules('visitor_type', 'Type', 'trim|required');

			  }

			if($this->input->post('btn_submit')=='update_status')
			  { //die("aaa= ".$this->input->post('visitor_type')."<br>bbb= ".$this->input->post('mmenu'));

				$vrequired = ( $data['sector'] == "20" ? "" : "|required");

				$this->form_validation->set_rules('buyer_type', 'Status', 'trim'.$vrequired);
				$this->form_validation->set_rules('visitor_type', 'Type', 'trim|required');

			  }

			  //die($data['mmenu']);

		    if($this->form_validation->run() == FALSE)
              {
              	$data['disableEDIT'] = $this->input->post('disableEDIT'); 	//die($data['mmenu']);

			  //if($data['mmenu'] <> "print" && $data['mmenu'] <> "scan" && $data['mmenu'] <> "barcodeSearch" && $data['mmenu'] <> "mark" )
				if($data['mmenu'] <> "Assignrfid" && $data['mmenu'] <> "print" && $data['mmenu'] <> "scan" && $data['mmenu'] <> "openForm" && $data['mmenu'] <> "barcodeSearch" && $data['mmenu'] <> "mark" && $data['mmenu'] <> "ticket" && $data['mmenu']<>"raffledraw" && $data['mmenu']<>"raffleClaimed" && $data['mmenu'] <> "ticket" && $data['mmenu']<>"barcodeTicketSearch" && $data['mmenu']<>"edit2" && $data['mmenu']<>"add_person2" && $data['mmenu'] <> "secondDay" && $data['mmenu'] <> "assignID" && $data['mmenu'] <>"assignIDclaimStub" && $data['mmenu'] <> "mobileapp")

				  {$this->load->view('vrs_modifyrec',$data);}
              }
            else
		      { //die($data['mmenu']);
			   if($this->input->post('btn_submit')=="update" || $this->input->post('btn_submit')=="update_status")
				{
				  switch($data['mmenu'])
				   {
				     case "edit":   		// ( $data['mmenu'] == "edit" || $data['mmenu'] == "mobileapp"):
					  //die("edit");

				      //die($data['mmenu']);
					  $refId = $this->input->post('repcode');
					  $barcodeValue = $this->input->post('barcode');
					  $pid = $this->input->post('pid');

					  	if($this->input->post('visitor_type')=="MEDIA") {$vsector = "22";}
						else {$vsector = $set_data['sessionData']['sector'];}

						$vsector = $set_data['sessionData']['sector'];

					  // ==== update field marked = 1 for subserver use
					  	$this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$refId);

        				$this->vrs_model->updateRecord("v_contact_profile","barcode",$barcodeValue,"rep_code",$refId);
						$this->vrs_model->updateRecord("v_contact_profile","pid",$pid,"rep_code",$refId);
						$this->vrs_model->updateRecord("v_contact_profile","trade_code",$data['fcode'],"rep_code",$refId);
						$this->vrs_model->updateRecord("v_contact_profile","sector",$vsector,"rep_code",$refId);
					  	$this->vrs_model->updateRecord("v_contact_profile","buyer_type",$this->input->post('buyer_type'),"rep_code",$refId);
					  	$this->vrs_model->updateRecord("v_contact_profile","visitor_type",$this->input->post('visitor_type'),"rep_code",$refId);
					  	$this->vrs_model->updateRecord("v_contact_profile","buyerclass",$this->input->post('buyerclass'),"rep_code",$refId);

					  	$this->vrs_model->updateRecord("v_contact_profile","add_value",$this->input->post('contact_replacement'),"rep_code",$refId);


					  	$data['typeAttendance']= $this->vrs_read_model->getRec("v_attendance","where fair_code='".$data['fcode']."' and sector='".$vsector."' and rep_code = ? limit 1",$refId,'');

					  	//getRec("v_attendance","where fair_code='".$data['fcode']."' and rep_code = ?"." ORDER BY fair_code DESC limit 1",$refId,'');


					  	if ($data['typeAttendance'] == "" ) // chk if rec exist in attendance table
			      		    {
			      		     $this->vrs_model->insertRecTable('v_attendance',$refId,$barcodeValue,'','',strtoupper($data['fcode']),$vsector);
			      		    }

						$this->vrs_model->updateRecTable("v_attendance","barcode",$barcodeValue,$data['fcode'],$vsector,$refId);
						$this->vrs_model->updateRecTable("v_attendance","visitor_status",$this->input->post('buyer_type'),$data['fcode'],$vsector,$refId);
						$this->vrs_model->updateRecTable("v_attendance","visitor_type",$this->input->post('visitor_type'),$data['fcode'],$vsector,$refId);
						$this->vrs_model->updateRecTable("v_attendance","buyerclass",$this->input->post('buyerclass'),$data['fcode'],$vsector,$refId);
					  //$this->vrs_model->updateRecTable('v_attendance','date_input',date('Y-m-d H:i:s'),strtoupper($data['fcode']),$data['sector'],$data["repcode"]);
					  //if ($this->db->affected_rows()==0)
					  // { die("insert attendance if not exist");}

						  //die("aaa=".$this->input->post('visitor_type'));

				    	  //======= CREATE CSV for pointwest APP ===================================================
				          //======= CREATE CSV for pointwest APP ===================================================
				   		  if($this->input->post('visitor_type')=='TRADE BUYER' || $this->input->post('visitor_type')=='TB')
				   		  {

				            //$this->createcsv($data['email'],"",$barcodeValue);

				      	  }
				          //========================================================================================
				          //========================================================================================

					 break;
					 case "add_person":
					  //die("add_person");
				      // $refId = $this->vrs_model->insertRecord("v_regcode");         // get rep_code first
				      // $tmpId = $this->vrs_model->insertRecord("v_contact_profile");
				      // $this->vrs_model->updateRecord("v_contact_profile","rep_code",$refId,"rep_code",$tmpId);

					  // $this->vrs_model->updateRecord("v_contact_profile","reg_status","T","rep_code",$refId);
					  // $barcodeValue = $refId;   // + 3000000;
					  // $pid = $this->generatePID($data['fcode']);
					  // $data['vurl']	= $refId;
					  // // ==== update field marked = 1 for subserver use
					  // $this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$refId);
					  // $this->vrs_model->updateRecord("v_contact_profile","venue",$data['myVenue'],"rep_code",$refId);
					  // // ==== update ATTENDANCE =======================
					  // $vnum = $this->vrs_model->insertRecord("v_attendance");
					  // //$vnum = $this->db->insert_id();
					  // $this->vrs_model->updateAttendance("v_attendance",$refId,$data['fcode'],$data['sector'],$vnum);
					  // // ==============================================
					  // $this->updateRegistration($refId,$barcodeValue,$pid,$data['mmenu']);
					 break;
					 case "add_company":
					  //die("add_company");
					  // $refId = $this->vrs_model->insertRecord("v_regcode");         // get rep_code first
				      // $tmpId = $this->vrs_model->insertRecord("v_contact_profile");
				   	  // $this->vrs_model->updateRecord("v_contact_profile","rep_code",$refId,"rep_code",$tmpId);

					  // $this->vrs_model->updateRecord("v_contact_profile","reg_status","T","rep_code",$refId);
					  // $barcodeValue = $refId;   // + 3000000;
					  // $pid = $this->generatePID($data['fcode']);  //die("aaa = ".$pid);
					  // $data['vurl']	= $refId;
					  // // ==== update field marked = 1 for subserver use
					  // $this->vrs_model->updateRecord("v_contact_profile","marked",$data['myVenueNum'],"rep_code",$refId);
					  // $this->vrs_model->updateRecord("v_contact_profile","venue",$data['myVenue'],"rep_code",$refId);
					  // // ==== update ATTENDANCE =======================
					  // $vnum = $this->vrs_model->insertRecord("v_attendance");
					  // //$vnum = $this->db->insert_id();
					  // $this->vrs_model->updateAttendance("v_attendance",$refId,$data['fcode'],$data['sector'],$vnum);
					  // //$this->vrs_model->updateAttendance("v_attendance","rep_code",$refId,$vnum);
					  // // ==============================================
					  // $this->updateRegistration($refId,$barcodeValue,$pid,$data['mmenu']);
					 break;
				   }

				}

			//die($data['mmenu'])	;

			   if ($data['mmenu']=='edit')
			    {
				  //die("aaa=".$data["repcode"]);
			      //$data['mmenu']='edit2';
			      //$data['fcode1']=$data['fcode'];
			      //$this->load->view('vrs_main',$data);
			      redirect("vrs/search/".$data['vurl']);
				}
			   else // if add company or person proceed to print
			    {
				 // die("bbb=".$data["repcode"]);
				  $data['mmenu']='print';
				  $data['repcode']=$refId;
				  $data['barcode']=$barcodeValue;

				  //====== DIY use ========================================
				  //$getIP = explode('.',$_SERVER['REMOTE_ADDR']);
				  //$data['diyTerminal'] = $getIP[3];
				  $data['vtype'] = ($this->input->post('visitor_type')=='TRADE BUYER' ? 'TB' : $this->input->post('visitor_type'));
				  $data['pid'] = $pid;
				  //=======================================================
				  //echo ("vrs/printid/".$data["repcode"]."/".$data['mmenu']."/".$data['vtype']);

				  //die("wazz");

				  redirect("vrs/printid/".$data["repcode"]."/".$data['mmenu']."/".$data['vtype']);


				  //$this->load->view('vrs_main',$data);
				}

				//die("wazz2");
			  }
		  }
	   else
	      {
			redirect('vrs/index');
		  }
	   //$this->load->view('vrs_modifyrec',$data);
	 }

public function generatePID($fairCode)
	{
		$code = sha1(mt_rand(10000,99999).time().$fairCode);  //die("aaa= ". $fairCode);
		return $code;
	}

public function reference()
     {
	   //=== set as active menu in MENU.php ========================
	   $data['userBar'] = "class='dropdown active'";
	   //===========================================================

       $set_data = $this->session->all_userdata();

	   if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {
		      $data['read_set_value'] = $set_data['sessionData']['realname'];
			  $data['sessionRights'] = $set_data['sessionData']['urights'];
			  $data['username'] = $set_data['sessionData']['user'];
			  $data['loginuser'] = $set_data['sessionData']['loginuser'];

			  $data['sysName'] = $set_data['sessionData']['sysName'];
			  $data['systitle'] = $set_data['sessionData']['systitle'];
		  	  $data['eventDB'] = $set_data['sessionData']['eventDB'];

			  $data['fcode'] = $set_data['sessionData']['fcode'];
		 	  $data['sector'] = $set_data['sessionData']['sector'];
			  $data['fairdesc'] = $set_data['sessionData']['fdesc'];
			  $data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

			  $data['myVenue'] = $set_data['sessionData']['venue'];
			  $data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			  $data['controller_CI'] = $set_data['sessionData']['controller_CI'];

			  //chk if admin =====================
			  if ($data['sessionRights']<>'1') {redirect('vrs/main');}
			  //==================================
			  //get the posted values

			  $data['userId'] = $this->input->post("userId");

			  $data['vswitch'] = (isset($_GET['ref']) ? $_GET['ref'] : $this->input->post("ref"));

			  if ($this->input->post("btn_userUpdate") == "Delete")
		 		{
		   			$this->master_model->deleteUserTable("v_reference","id",$data['userId']);
		   			redirect('vrs/reference?ref='.$data['vswitch']);
		 		}


		 	  $data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			  $data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			  $data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

			  $data['vReference']= $this->master_model->loadRec("v_reference"," where switch='".$data['vswitch']."' and sector LIKE '%".$data['sector']."%' order by sortfield");  // cut-off
			  $data['RefProfile']= $this->master_model->loadRec("v_reference","where id ='".$data['userId']."'");

			  $data['refTitle']="REFERENCE [ ".$data['vswitch']." ]";
			  $data['userId']= "";
			  $data['vc_profile']= "";
	          $data['vc_code']= "";
			  $data['vsector']= "";
			  $data['vsortfield']= "";


			  $btn_press= $this->input->post("btn_userUpdate");
			  if ($btn_press != "")
			    {$data['updateButon'] = "Update";}
			  else {$data['updateButon'] = "Add";}
			  //$this->initUser();

			  if(isset($data['RefProfile']) && $data['RefProfile'] != "")
			  {
			   foreach($data['RefProfile'] as $r1)
			   {
			     $data['userId'] = $r1['id'];
			     $data['vc_profile'] = $r1['c_profile'];
				 $data['vc_code'] = $r1['c_code'];
				 $data['vsector'] = $r1['sector'];
				 $data['vsortfield'] = $r1['sortfield'];
			   }
			  }

			  if($this->input->post("btn_userUpdate")=="Add" || $this->input->post("btn_userUpdate")=="Update")
			  	{
			  		$this->form_validation->set_error_delimiters('<div>', '</div>');
			  		$this->form_validation->set_rules('c_profile', 'Name', 'trim|required');
	   		  		$this->form_validation->set_rules('c_code', 'Value', 'trim|required');
	   		  		$this->form_validation->set_rules('sector', 'Sector', 'trim|required');
	   		  		$this->form_validation->set_rules('sortfield', 'Sort field', 'trim|required');
	   		  	}
	   		  if ($this->form_validation->run() == FALSE)
            	{
               		//validation fails
			   		if($this->input->post("btn_userUpdate")=="Add")
			   			{$data['updateButon'] = "Add";}

			   		$this->load->view('vrs_reference',$data);
            	}
              else
                {
                	//$switch = "cutof";
                	if ($this->input->post("btn_userUpdate") == "Add")
			    		{
			      			$this->master_model->insertRefTable("v_reference",$this->input->post("c_profile"),$this->input->post("c_code"),$this->input->post("sector"),$this->input->post("sortfield"),$data['vswitch']);
						}
					else
						{
							$this->master_model->updateReftable("v_reference","c_profile",$this->input->post("c_profile"),"id",$data['userId']);
							$this->master_model->updateReftable("v_reference","c_code",$this->input->post("c_code"),"id",$data['userId']);
							$this->master_model->updateReftable("v_reference","sector",$this->input->post("sector"),"id",$data['userId']);
							$this->master_model->updateReftable("v_reference","sortfield",$this->input->post("sortfield"),"id",$data['userId']);
						}
					redirect('vrs/reference?ref='.$data['vswitch']);
                }

			}
	   else
	        {
		  //$data['read_set_value'] = 'Please Set Session Value First !';
			  redirect('vrs/index');
		    }

	   //$this->load->view('vrs_reference',$data);
	 }

	 public function users()
     {
	   //=== set as active menu in MENU.php ========================
	   $data['userBar'] = "class='dropdown active'";
	   //===========================================================

       $set_data = $this->session->all_userdata();

	   if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {
		      $data['read_set_value'] = $set_data['sessionData']['realname'];
			  $data['sessionRights'] = $set_data['sessionData']['urights'];
			  $data['username'] = $set_data['sessionData']['user'];
			  $data['loginuser'] = $set_data['sessionData']['loginuser'];

			  $data['sysName'] = $set_data['sessionData']['sysName'];
			  $data['systitle'] = $set_data['sessionData']['systitle'];
		  	  $data['eventDB'] = $set_data['sessionData']['eventDB'];

			  $data['fcode'] = $set_data['sessionData']['fcode'];
		 	  $data['sector'] = $set_data['sessionData']['sector'];
			  $data['fairdesc'] = $set_data['sessionData']['fdesc'];
			  $data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

			  $data['myVenue'] = $set_data['sessionData']['venue'];
			  $data['myVenueNum'] = $set_data['sessionData']['venueNum'];
			  //chk if admin =====================
			  if ($data['sessionRights']<>'1') {redirect('vrs/main');}
			  //==================================
			  //get the posted values

              //$uId = (isset($_GET['u']) ? $_GET['u'] : $this->input->post("userId"));
              $uId = $this->input->post("userId");

			  $data['userAccnt']= $this->master_model->loadRec("v_users"," order by name");
			  $data['userProfile']= $this->master_model->loadRec("v_users","where id ='".$uId."'");

			  $data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			  $data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");
			  $data['userIDsize']= $this->master_model->loadRec("v_reference","where switch ='VRSID' and exclude=0 order by sortfield");
			  $data['v_useraccess']= $this->master_model->getRec("v_useraccess","where sector = '".$data['sector']."' and rep_code = ?",$uId,"");

			  $data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

			  //$data['RegStatus']=$this->master_model->loadRec("v_reference","where switch='MN6' order by c_profile");
			  $data['RegType']=$this->master_model->loadRec("v_reference","where switch='BSTAT' and sector like '%".$data['sector']."%' order by sortfield");
			  //$data['v_useraccess']= $this->vrs_model->getRec("v_useraccess","where fair_code='".$data['fcode']."' AND rep_code = ?",$uId,"");

			  // $data['userCountry']= $this->vrs_model->loadRec("v_country","where switch='1' order by description");
			  // $data['caseSource']= $this->vrs_model->loadRec("v_reference","where switch='7' order by sort");

			  $data['userId']= "";
			  $data['tname']= "";
	          $data['uname']= "";
			  $data['pword']= "";
			  $data['uemail']= "";
			  $data['vrights']="";
			  $data['vvenue']="";
			  $data['vvenueNum']="";
			  $data['vdefault_type']="";
			  //$data['vgroup']="";
			  //$data['venable_vrs_diy_trade']= "";
			  //$data['venable_vrs_diy_visitor']= "";

			  $btn_press= $this->input->post("btn_userUpdate");
			  if ($btn_press != "")
			    {$data['updateButon'] = "Update";}
			  else {$data['updateButon'] = "Add User";}
			  //$this->initUser();

			  if(isset($data['userProfile']) && $data['userProfile'] != "")
			  {
			   foreach($data['userProfile'] as $r1)
			   {
			     $data['userId'] = $r1['id'];
			     $data['tname'] = $r1['name'];
				 $data['uname'] = $r1['username'];
				 //$data['pword'] = $this->encrypt->decode($r1['password']);

				 $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $r1['password']);
        		 $data['pword'] = $this->encryption->decrypt($decryptX);

				 $data['uemail'] = $r1['email'];
				 $data['vrights'] = $r1['rights'];
				 $data['vvenue'] = $r1['sector'];
				 $data['vvenueNum'] = $r1['status'];
				 $data['vdefault_type']=$r1['default_type'];

			   }
			  }
			}
	   else
	        {
		  //$data['read_set_value'] = 'Please Set Session Value First !';
			  redirect('vrs/index');
		    }
		    //die("aaa=".$data['vvenue']);

	   $this->load->view('vrs_users',$data);
	 }

     public function usersProfil()
     {
	   $set_data = $this->session->all_userdata();
	   if (!isset($set_data['sessionData'])) {redirect('labor/index');}

	   $data['read_set_value'] = $set_data['sessionData']['realname'];
	   $data['sessionRights'] = $set_data['sessionData']['urights'];
	   $data['username'] = $set_data['sessionData']['user'];
	   $data['loginuser'] = $set_data['sessionData']['loginuser'];

	   $data['sysName'] = $set_data['sessionData']['sysName'];
	   $data['systitle'] = $set_data['sessionData']['systitle'];
	   $data['eventDB'] = $set_data['sessionData']['eventDB'];

	   $data['fcode'] = $set_data['sessionData']['fcode'];
	   $data['sector'] = $set_data['sessionData']['sector'];
	   $data['fairdesc'] = $set_data['sessionData']['fdesc'];
	   $data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

	   $data['myVenue'] = $set_data['sessionData']['venue'];
	   $data['myVenueNum'] = $set_data['sessionData']['venueNum'];

	   //chk if admin =====================
	   if ($data['sessionRights']<>'1') {redirect('labor/main');}
	   //==================================
	   //=== set as active menu in MENU.php ========================
	   $data['userBar'] = "class='dropdown active'";
	   //===========================================================
	   //$data['userCountry']= $this->vrs_model->loadRec("labor_country","where switch='1' order by description");
	   //$data['caseSource']= $this->vrs_model->loadRec("labor_reference","where switch='1' order by sort");

	   $data['userId'] = $this->input->post("userId");
	   $data['tname'] = $this->input->post("realname");
	   $data['uname'] = $this->input->post("username");
	   $data['pword'] = $this->input->post("password");
	   $data['uemail'] = $this->input->post("email");
	   $data['vrights'] = $this->input->post("userRights");
	   $data['vvenue'] = $this->input->post("venue");
	   $data['vvenueNum']= $this->input->post("venueNum");
	   //$data['vgroup'] = $this->input->post("vgroup");
	   $data['vdefault_type']=$this->input->post("default_type");

	   $data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
	   $data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

	   $data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

	   $data['userIDsize']= $this->master_model->loadRec("v_reference","where switch ='VRSID' and exclude=0 order by sortfield");
	   $data['RegType']=$this->master_model->loadRec("v_reference","where switch='BSTAT' and sector like '%".$data['sector']."%' order by sortfield");

	   $data['v_useraccess']= $this->master_model->getRec("v_useraccess","where sector = '".$data['sector']."' and rep_code = ?",$this->input->post("userId"),"");
	   //$data['v_useraccess']= $this->vrs_model->getRec("v_useraccess","where fair_code='".$data['fcode']."' AND rep_code = ?",$this->input->post("userId"),"");


	   //$data['vcountry'] = ($this->input->post("country") == "" ? (isset($data['vcountry']) ? $data['vcountry'] : '') : $this->input->post("country"));

	   if ($this->input->post("btn_userUpdate") == "Delete")
		 {
		   $this->master_model->deleteUserTable("v_users","id",$data['userId']);
		   $this->master_model->deleteUserTable("v_useraccess","rep_code",$data['userId']);
		   redirect('vrs/users');
		 }

	   $data['updateButon'] = "Update";

	   $data['userAccnt']= $this->master_model->loadRec("v_users"," order by name");
	   //$data['userProfile']= $this->login_model->loadRec("v_users","where id ='".$this->input->post("userId")."'");

	   $this->form_validation->set_error_delimiters('<div>', '</div>');
	   $this->form_validation->set_rules('realname', 'Complete Name', 'trim|required'); //xss_clean removes malicious data
	   $this->form_validation->set_rules('username', 'Username', 'trim|required'); //xss_clean removes malicious data
	   $this->form_validation->set_rules('password', 'Password', 'trim|required'); //xss_clean removes malicious data
	   $this->form_validation->set_rules('email', 'Email', 'valid_email|trim'); 	//'required|valid_email|trim|xss_clean'
	   $this->form_validation->set_rules('venue', 'Venue', 'trim|required');
	   $this->form_validation->set_rules('userRights', 'User Rights', 'trim|required');
	   $this->form_validation->set_rules('venueNum', 'Venue Number', 'trim|required');
	   $this->form_validation->set_rules('default_type', 'Default Form Type', 'trim|required');
	   //$this->form_validation->set_rules('country', 'Country Representation', 'trim|required|xss_clean');
	   //$this->form_validation->set_rules('vgroup', 'Group', 'trim|required|xss_clean');
      // $set_data = $this->session->all_userdata();

		    if ($this->form_validation->run() == FALSE)
            {
               //validation fails
			   if($this->input->post("btn_userUpdate")=="Add User")
			   {$data['updateButon'] = "Add User";}

			   $this->load->view('vrs_users',$data);
            }
            else
            {
			  if ($this->input->post("btn_userUpdate") == "Update")
			    {
			      //$this->vrs_model->updateRecord("v_contact_profile","barcode",$bcode,$rId);
				  $this->master_model->updateRecord("v_users","name",$data['tname'],"id",$data['userId']);
				  $this->master_model->updateRecord("v_users","username",$data['uname'],"id",$data['userId']);

				  $tmp = $this->encryption->encrypt($data['pword']);
        		  $encrypted_string = str_replace(array('+', '/', '='), array('-', '_', '^'), $tmp);
				  $this->master_model->updateRecord("v_users","password",$encrypted_string,"id",$data['userId']);


				  $this->master_model->updateRecord("v_users","email",$data['uemail'],"id",$data['userId']);
				  $this->master_model->updateRecord("v_users","rights",$data['vrights'],"id",$data['userId']);
				  $this->master_model->updateRecord("v_users","sector",$data['vvenue'],"id",$data['userId']);		 // ***** USED to STORE VENUE *******
				  $this->master_model->updateRecord("v_users","status",$data['vvenueNum'],"id",$data['userId']);
				  $this->master_model->updateRecord("v_users","default_type",$data['vdefault_type'],"id",$data['userId']);
				  //$this->master_model->updateRecTable("v_users","country",$data['vcountry'],$data['userId'],"","");
				  //$this->vrs_model->updateRecTable("v_users","sector",$data['vgroup'],$data['userId'],"","");
			      $data['updateButon'] = "Add User";
			      $uId = $data['userId'];
				  // refresh users
				  //$data['userAccnt']= $this->login_model->loadRec("v_users"," order by username");
				}
			  if ($this->input->post("btn_userUpdate") == "Add User")
			    {
				                      //insertUserTable($companyTbl,$vname,$vuser,$vpass,$vemail,$vrights)
			      $tmp = $this->encryption->encrypt($data['pword']);
        		  $encrypted_string = str_replace(array('+', '/', '='), array('-', '_', '^'), $tmp);

			      $uId = $this->master_model->insertUserTable("v_users",$data['tname'],$data['uname'],$encrypted_string,$data['uemail'],$data['vrights']);

			      $this->master_model->updateRecord("v_users","sector",$data['vvenue'],"id",$uId);
			      $this->master_model->updateRecord("v_users","status",$data['vvenueNum'],"id",$uId);
			      $this->master_model->updateRecord("v_users","default_type",$data['vdefault_type'],"id",$uId);

				}
				    //updateSurvey($vtable,$rcode,$vbarcode,$vfcode,$vsec,$vfield,$vremarks)
			   $this->updateSurvey('v_useraccess',$uId,'','',$data['sector'],'accessRights','');

			  // load function users to initialize
			  redirect('vrs/users?u='.$data['userId']);
			}
	   //$this->load->view('users',$data);
	 }

	 public function chkTicketExpire()
	 {
	 	die("wazz");
	 }

     public function payment()
     {															//==============================================================================================================

      //$this->load->model('event_model');

      //=== load main menu ========================
	  $data['dashPay'] = "class='active'";
	  //===========================================

       $set_data = $this->session->all_userdata();

	   if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {
		  	if(!isset($set_data['sessionData']['fcode'])) {redirect('vrs/index');}

		    $data['read_set_value'] = $set_data['sessionData']['realname'];
			$data['sessionRights'] = $set_data['sessionData']['urights'];
			$data['loginuser'] = $set_data['sessionData']['loginuser'];

			$data['sysName'] = $set_data['sessionData']['sysName'];
			$data['systitle'] = $set_data['sessionData']['systitle'];
			$data['eventDB'] = $set_data['sessionData']['eventDB'];

			$data['fcode'] = $set_data['sessionData']['fcode'];
			$data['sector'] = $set_data['sessionData']['sector'];
			$data['fairdesc'] = $set_data['sessionData']['fdesc'];
			$data['diyTerminal'] = $set_data['sessionData']['terminalNo']; //echo "aaa=".$data['fcode']; die();

			$data['myVenue'] = $set_data['sessionData']['venue'];
			$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");


            //===========================================
            //======== get RIGHTS procedure =============
            //===========================================
            foreach ($data['userAccessRef'] as $r1)
            {

              $founds=0;
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

               $xxx = $set_data['sessionData'][$x."r"];
               																					// Using ${} is a way to create dynamic variables, ex.====> ${'vrs_'.$r1['c_code']} = "";
               if ($r1['c_code']==$xxx) { $data['vrs_'.$r1['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r1['c_code']] = ""; }
              //echo $data['vrs_'.$r1['c_code']]."<br>";

            }
            foreach ($data['userAccessMod'] as $r2)
            {

              $founds=0;
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

               $xxx = $set_data['sessionData'][$x."r"];
               // Using ${} is a way to create dynamic variables,
               if ($r2['c_code']==$xxx) { $data['vrs_'.$r2['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r2['c_code']] = ""; }
              //echo $data['vrs_'.$r2['c_code']]."<br>";

            }
            //===========================================
            //===========================================

			//========== get as of DATE for dashboard ===================================================
			$data['asofDate'] = date('F d, Y h:i A');
			//==========================================================================================


			if($_POST) {
				//print_r($_POST); die();

				if(!isset($_POST['item_code']))	{

					$data['messageX'] = "Please select a Reference #";
				}
				else {
					//$date = $_POST['or_date'];
					$tmp = explode('#',$_POST['item_code']);
					$xdate = date('Y-m-d H:i:s',strtotime($_POST['or_date']));

					$data['project']= $this->master_model->getRec("busmatch_date","where fair_code = ?",strtoupper($data['fcode']),'');
   					if ($data['project']=='') {die("Under Construction.....");}

					$data['profiles']= $this->vrs_read_model->getRec("v_contact_profile","where rep_code = ?",$tmp[0],'');
					if ($data['profiles']=="") {die("repcode not found");}

					foreach($data['profiles'] as $rcode)
					 {
					   	$barcode = $rcode['barcode'];
					   	$visitorType = $rcode['visitor_type'];
					  	$email = $rcode['email'];
						$ctry = $rcode['country'];
					 }
					$data['vtype'] = $visitorType;

					$this->vrs_model->updateAllRecord("v_payment_order","or_number",$_POST['or_number'],"item_code='".$tmp[1]."' and fair_code='".$data['fcode']."'");
					$this->vrs_model->updateAllRecord("v_payment_order","date_or",$xdate,"item_code='".$tmp[1]."' and fair_code='".$data['fcode']."'");
					//$this->vrs_model->updateAllRecord("v_payment_order","status","Paid","item_code='".$tmp[1]."' and fair_code='".$data['fcode']."'");
					//$this->vrs_model->updateAllRecord("v_payment_order","amount",$_POST['or_pay'],"item_code='".$tmp[1]."' and fair_code='".$data['fcode']."'");


				          //====== check if for CREATE PHILS ============================================================
				          //=============================================================================================
				          if($data['sector']=="20")
				          {
				            $this->barcode($barcode,$data['fcode']);

				            $vcontact = $data['profiles'][0]['cont_per_fn']." ".$data['profiles'][0]['cont_per_ln'];

				            $urlMess = "Welcome aboard, fellow creative mind! You just secured yourself a slot in the <strong>CREATE Philippines</strong> Masterclasses.
			                      <p>
			                      To learn fresh creative ideas and meet our top-caliber speakers, kindly present this confirmation e-mail at the Onsite Registration Counter together with your delegate code below:
			                      </p>
			                      <p style='text-align:center;'>
			                      <img src='".base_url('assets/images/barcode/barcode'.$data['fcode'].'_'.$barcode.'.gif')."' height='60' alt='".$barcode."'><br>
			                      ".$barcode."
			                      </p>
			                      <p>
			                      Want to maximize your participation in <strong>CREATE Philippines</strong>? Join the different pocket events below for <strong>FREE</strong>:
			                      </p>
			                      <p>
			                      <ul>
			                      	<li><strong>Portfolio exhibition</strong> – a showcase of creative works of different artists, designers and creative studios including illustration, graphic design, motion graphics and many more.
			                      	</li><br>
			                      	<li><strong>Workshop</strong> – enjoy immersive and fun workshop with fellow creative heads courtesy of Crown Supply.</li>
			                      	<li><strong>Seminar</strong> – Beyond the Box is conducting seminars during the event. Stay tuned for the topics!</li>
			                      	<li><strong>Business-matching</strong> – meet potential creative entrepreneurs, artists and designers who can provide customized creative packages for your company. Sign up <a href='http://www.citem.com.ph/createphilippines/businessmatching.html' target='_blank'>here!</a>
			                      	</li>
			                      </ul>
			                      </p>
			                      <p>
			                      Note: For <strong>students</strong>, kindly present your <strong>school ID</strong> at the registration counter for validation.
			                      </p>
			                      <p>
			                      To stay updated on the latest about the event including speakers and masterclass schedules, follow or like our
			                       <a href='https://www.facebook.com/createphilippines' target='_blank'>Facebook</a>,
                    			   <a href='https://twitter.com/CreatePHILS' target='_blank'>Twitter</a> and
                    			   <a href='https://www.instagram.com/createphilippines' target='_blank'>Instagram</a> accounts.
			                      </p>
			                      <p>
			                      For any question or concern, you may reach us through our e-mail at <a href='mailto:<EMAIL>'><EMAIL></a> or call us at +63-2.832-5044.
			                      </p>
			                      <p>We are thrilled to share creative ideas with you!</p>
			                      <br>
			                      <p>Cheers,</p>
			                      <br>
			                      <p>
			                      Your CREATE Philippines Team
			                      </p>
				            ";

			            	$message= $this->messageReferer($data['fairdesc'],$urlMess,"",$data['project'][0]['bannerpic'],$vcontact,$data['sector'],$tmp[1]);

			            	//die($message);

			                $this->load->library('email');
			                $this->email->set_newline("\r\n");
			                $this->email->from($data['project'][0]['emailto_registration'],$data['fairdesc']); // change it to yours
			                $this->email->reply_to($data['project'][0]['emailto_registration']);

			                $this->email->to($email);// change it to yours ==== sendto
			                $this->email->cc("<EMAIL>");
			                $this->email->subject($data['fairdesc']." Masterclasses");
			                //$this->email->set_custom_header("X-MC-Subaccount", $subAccount); // uses application/libraries/MY_Email.php
			                $this->email->message($message);
			                 if($this->email->send())
			                   { $data['messageX'] = "Update Successful"; }
			                 else
			                   {
			                     //show_error($this->email->print_debugger());
			                     $data['emailErr'] = "Error in Sending Email... (s6)";
			                   }

				          //====== for CREATE PHILS =====================================================================
				          //=============================================================================================
				          }

				}


			} //($_POST)

			if(isset($_GET['view']) && $_GET['view'] =="all") {
				$data['rsProfile'] = $this->vrs_read_model->loadRec("citemcom_epayment.tbl_onlinepayment","");
				//$data['rsProfile']= $this->vrs_read_model->joinTable3("v_payment_order as A","v_payment_online as B","v_contact_profile as C","A.rep_code,A.item_code,C.co_name,C.cont_per_ln,C.cont_per_fn,A.date_register,A.date_expire,A.status,A.or_number as or_num,A.date_or as or_date,A.amount as amount_value,A.date_payment,A.mode_payment,B.invoiceNo","A.item_code = B.referenceCode","C.rep_code = A.rep_code","deleted= '0' and A.fair_code = '".$data['fcode']."'","inner");
			}
			else {
				$data['rsProfile'] = $this->vrs_read_model->loadRec("citemcom_epayment.tbl_onlinepayment","where status = 'Paid'");
				//$data['rsProfile']= $this->vrs_read_model->joinTable3("v_payment_order as A","v_payment_online as B","v_contact_profile as C","A.rep_code,A.item_code,C.co_name,C.cont_per_ln,C.cont_per_fn,A.date_register,A.date_expire,A.status,A.or_number as or_num,A.date_or as or_date,A.amount as amount_value,A.date_payment,A.mode_payment,B.invoiceNo","A.item_code = B.referenceCode","C.rep_code = A.rep_code","deleted= '0' and A.status='Paid' and A.fair_code = '".$data['fcode']."'","inner");
			}


		  }
	   else
	      {
			redirect('vrs/index');
		  }
	   $this->load->view('vrs_payment',$data);
	 }


	 public function sendEmail()
     {															//==============================================================================================================
		error_reporting(0);
   		ini_set('error_reporting', 0);
   		ini_set('display_errors',0);

      //=== load main menu ========================
	  //$data['dashPay'] = "class='active'";
	  //===========================================


	   //if (!isset($_POST['inc']))  { die($_POST['inc']); redirect('vrs/index'); }

			//========== get as of DATE  ===================================================
			$data['asofDate'] = date('F d, Y h:i A');
			//==========================================================================================


            //if(isset($_GET['d']) && $_GET['d']<>"")
			//  {
					$data['xdash'] = $_POST['xdash']; 	//die($data['xdash']);
					$data['inc'] = $_POST['inc'];
				  	$data['fcode'] = $_POST['fcode'];
				  	$data['vtype'] = $_POST['vtype'];
				  	$data['sector'] = $_POST['sector'];
				  	$repcode = $_POST['repcode'];

			   		$data['project']= $this->master_model->getRec("busmatch_date","where fair_code = ?",strtoupper($data['fcode']),'');
   					if ($data['project']=='') {die("invalid faircode.....");}

					$data['profiles']= $this->vrs_read_model->getRec("v_contact_profile","where rep_code = ?",$repcode,'');
					if ($data['profiles']=="") {die("contact not found");}

					foreach($data['profiles'] as $rcode)
					 {
					   	$barcode = $rcode['barcode'];
					   	$pid = $rcode['pid'];
					   	//$visitorType = $rcode['visitor_type'];
					  	$email = $rcode['email'];
						$ctry = $rcode['country'];
					 }
					//$data['vtype'] = $visitorType;


					$pid = $this->encryption->encrypt($pid);
			        $pid = str_replace(array('+', '/', '='), array('-', '_', '^'), $pid);

			        $vtype = ($data['vtype']=="TRADE BUYER" ? "TB" : $data['vtype']);

			      	$step2link = $data['project'][0]['url_event_page1']."&pid=".$pid."&vt=".$vtype."&preg=1&app=5";		//die($step2link);

			      	$link_here = "<a href='".$step2link."' target='_blank'>LINK</a>";


	    			switch ($vtype) {
	    				case "TRADE BUYER":
	    					$swValue = "MESS1";
	    					break;
	    				case "GUEST":
	    					$swValue = "MESS2";
	    					break;
	    				case "GENERAL PUBLIC":
	    					$swValue = "MESS3";
	    					break;
	    				case "MEDIA":
	    					$swValue = "MESS4";
	    					break;
	    			}

				          //====== check if for CREATE PHILS ============================================================
				          //=============================================================================================
				          //if($data['sector']=="03")
				          //{
				            //$this->barcode($barcode,$data['fcode']);


			    	    	$getContent = $this->master_model->loadRec("v_reference","where switch ='".$swValue."' and exclude=0 AND sector like '%".$data['sector']."%' order by sortfield LIMIT 1");
			    	    	$messContent = $getContent[0]['content_message'];

			    	    	$mess0 = str_replace("{link_here}",$link_here,$messContent);
							//$mess1 = str_replace("{last_name}",$_POST['cont_per_ln'],$mess0);
							//$mess2 = str_replace("{co_name}",$_POST['co_name'],$mess1);
							//$mess3 = str_replace("{email}",$_POST['email'],$mess2);
							//$mess4 = str_replace("{url}",$_POST['url'],$mess3);


				            $vcontact = $data['profiles'][0]['cont_per_fn']." ".$data['profiles'][0]['cont_per_ln'];

				            //$vtxt2 = "";
				            //$urlMess = "You previously tried to pre-register, but was not able to finished. Please click the
				            //<a href='".$step2link."' target='_blank'>LINK</a>
				            //to proceed with your pre-registration for the ".trim($data['project'][0]['description']." ".$vtxt2).".<br><br>
				            //To avoid any inconvenience, please add ".$data['project'][0]['emailto_registration']." to your contact email-address
				            //or safe sender list.<br><br>Thank you. <br><br>";

							   //       $urlMess = "You are now successfully registered to the Logistics Services Philippines (LSPH) Conference 2018, happening in Meeting Room 1 at the Philippine International Convention Center (PICC) on 06 December 2018.
						       //                <p>
													// Present this confirmation email at the Registration Counter to get your DELEGATE ID. Registration opens at 9:00AM.
						       //                </p>
						       //                <p style='text-align:center;'>
						       //                <img src='".base_url('assets/images/barcode/barcode'.$data['fcode'].'_'.$barcode.'.gif')."' height='60' alt='".$barcode."'><br>
						       //                ".$barcode."
						       //                </p>

						       //                <p>
						       //                Kindly observe below guidelines upon entry to the event venue:
						       //                </p>

						       //                <p>
						       //                <ul>
						       //                	<li>Only those with Delegate IDs will be allowed entry at the venue.</li>
						       //                	<li>Children below 12 years old are strictly prohibited from entering the event venue. </li>
						       //                	<li>Proper business attire is encouraged.</li>
						       //                </ul>
						       //                </p>
						       //                <p>
						       //                	Should you have any question or concern, you may send an email to <a href='mailto:<EMAIL>'><EMAIL></a> or <a href='mailto:<EMAIL>'><EMAIL></a> or contact CITEM at 831-2201 local 226.

						       //                </p>
							      //       ";

			            	$message= $this->messageReferer($data['project'][0]['description'],$mess0,"",$data['project'][0]['bannerpic'],$vcontact,$data['sector'],"");

			            	//die($message);

			               //  $this->load->library('email');
			               //  $this->email->set_newline("\r\n");
			               //  $this->email->from($data['project'][0]['emailto_registration'],$data['project'][0]['description']); // change it to yours
			               //  $this->email->reply_to($data['project'][0]['emailto_registration']);

			               //  $this->email->to($email);// change it to yours ==== sendto
			               //  $this->email->cc($data['project'][0]['emailto']);
			               //  $this->email->subject($data['project'][0]['description']."");
			               //  $this->email->message($message);
			               //   if($this->email->send())
			               //     {
			               //     	 $data['messageX'] = "Email Sent";
			           			 // // $this->vrs_model->updateAllRecord("v_contact_profile","emailed","1","rep_code=".$repcode);
			           		  //  }
			               //   else
			               //     {
			               //       //show_error($this->email->print_debugger());
			               //       $data['errDesc'] = $this->email->print_debugger(array('headers'));
			               //       //$data['messageX'] = show_error($this->email->print_debugger());
			               //       $data['messageX'] = "Error in Sending Email... (s6)";
			               //     }

			                //=======================================================
			                $naSend = "";
			                $vfrom = $data['project'][0]['emailto_registration'];
			                $vcc = $data['project'][0]['emailto'];

                            $naSend = $this->sendEmailproc($email,$vfrom,"",$vcc,"",$data['project'][0]['description'],$message);

                            if($naSend<>"") {
                                    //die($naSend);
                                    $data['errDesc'] = $naSend;
                                    $data['messageX'] = "We are very sorry but there was a problem in Sending Email, please try again later... (s1)";
                                    $this->writeError($data['errDesc'],$subj,$repcode,$barcodeValue,$data['fcode'],$data['vsector'],$sendto,"step1 - s1");
                            }
                            else {$data['messageX'] = "Email Sent"; }
                            //========================================================

 							if($data['messageX'] == "Email Sent") {
			                		$this->vrs_model->loadRec2("update v_attendance set emailed = emailed + 1 where "."rep_code=".$repcode." and fair_code='".$data['fcode']."' and sector='".$data['sector']."'","update");
			                }
			                // $addQry =  "update v_attendance set emailed = emailed + 1 where "."rep_code=".$repcode." and fair_code='".$data['fcode']."' and sector='".$_GET['sector']."'"
			                // $this->db->query($addQry);
				          //=============================================================================================
				          //=============================================================================================
				          //}

			//} //($_POST)
			$data['tableVIEW'] = "1";
			$data['vrsMode'] = "3";

			if ($_POST['inc']=="1") {
				$data['nomenu'] = "1";
				$data['vrsMode'] = "2";
				$data['ViewHeader'] = $_POST['header'];
			}



			//$qryAdd = "(B.visitor_type LIKE '%VISITOR%' or B.visitor_type LIKE '%GENERAL PUBLIC%' or B.visitor_type LIKE '%TRADE BUYER%') and ";		//.$qryDate;
			$qryAdd = "(B.visitor_type LIKE '%GUEST%' or B.visitor_type LIKE '%GENERAL PUBLIC%' or B.visitor_type LIKE '%TRADE BUYER%') and ";

			$data['results']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","A.rep_code,A.co_name,A.email,A.email2,A.work_email,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,B.emailed,B.buyerclass,B.date_apply,B.visitor_type,B.url_form","A.rep_code = B.rep_code",$qryAdd." A.deleted= '0' and B.pre_reg='INC' and B.fair_code = '".$data['fcode']."'","");


	   		$this->load->view('vrs_view',$data);
	 }


	  function sendEmailproc($vRecipient,$vFrom,$vReplyto,$vCC,$vBCC,$vSubject,$vContent) {

        // Load PHPMailer library
        $this->load->library('phpmailer_lib');

        // PHPMailer object
        $mail = $this->phpmailer_lib->load();

        // SMTP configuration
        $mail->isSMTP();
        $mail->Host     = SMTP_HOST;
        $mail->SMTPAuth = SMTP_AUTH;
        $mail->SMTPAutoTLS = SMTP_AutoTLS;
        $mail->Username = SMTP_USER;
        $mail->Password = SMTP_PASS;
        $mail->SMTPSecure = SMTP_ENCRYPT;     // 'ssl'    // tsl
        $mail->Port     = SMTP_PORT;

        $mail->setFrom($vFrom);                  //  ('<EMAIL>', 'CodexWorld');
        $mail->addReplyTo($vReplyto);            //  ('<EMAIL>', 'CodexWorld');

        // Add a recipient
        $mail->addAddress($vRecipient);

        // Add cc or bcc
        $mail->addCC($vCC);                       //('<EMAIL>');
        $mail->addBCC($vBCC);                     //('<EMAIL>');

        // Email subject
        $mail->Subject = $vSubject;

        // Set email format to HTML
        $mail->isHTML(true);

        // Email body content
        $mailContent = $vContent;
        $mail->Body = $mailContent;

        // Send email
        if(!$mail->send()){
           // echo 'Message could not be sent.';
           // echo 'Mailer Error: ' . $mail->ErrorInfo;
           $result = $mail->ErrorInfo;
        }else{
            //echo 'Message has been sent';
           $result = "";
        }

    return $result;
  }

    function writeError($errDesc,$errSubj,$rcode,$bcode,$fcode,$sector,$errEmail,$vfunc)
	  {

	  	  $this->load->model('site_model');

	      $errRef = $this->site_model->insertRecord("v_email_send_status");
	      $this->site_model->updateField("v_email_send_status","remarks",$errDesc,"refno=".$errRef);
	      $this->site_model->updateField("v_email_send_status","subject",$errSubj,"refno=".$errRef);
	      $this->site_model->updateField("v_email_send_status","rep_code",$rcode,"refno=".$errRef);
	      $this->site_model->updateField("v_email_send_status","barcode",$bcode,"refno=".$errRef);
	      $this->site_model->updateField("v_email_send_status","fair_code",$fcode,"refno=".$errRef);
	      $this->site_model->updateField("v_email_send_status","sector",$sector,"refno=".$errRef);
	      $this->site_model->updateField("v_email_send_status","func_module",$vfunc,"refno=".$errRef);
	      $this->site_model->updateField("v_email_send_status","email",$errEmail,"refno=".$errRef);

	  }

 	public function invite()		// ====== vrs/invite?m={email add}&x=1
	  {
		//=== set as active menu in MENU.php ========================
	   	//$data['userBar'] = "class='dropdown active'";
	   	//===========================================================

 			$data['fcode'] = "";
 			if(isset($_GET['x']) )  {$data['fcode'] = $_GET['x'];} 		//{	die("Setting incomplete.....(1)"); }
 			if(isset($_GET['fc']) ) {$data['fcode'] = $_GET['fc'];}
 			// elseif (!isset($_GET['fc']) || $_GET['fc']=="" ) {die("Setting incomplete.....(2)"); }
 			// else { }

 			$data['project']= $this->master_model->getRec("busmatch_date","where fair_code = ?",strtoupper($data['fcode']),'');

 			if($data['project']=="") {	die("Setting incomplete.....(1)");}

 			$data['sector'] = $data['project'][0]['sector'];
 			$data['getWebCode'] = $data['project'][0]['url_busmatch'];

 			//die("aaa=".$data['getWebCode']);



			$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

			$vemail = (isset($_GET['m']) && $_GET['m']<>"" ? $_GET['m'] : "err" );

			$temp = $this->encryption->encrypt($vemail);
    		$vemail= str_replace(array('+', '/', '='), array('-', '_', '^'), $temp);



			//====== rdirect to hide email if ecard used thru Mktg cloud ============================================================

			(isset($_GET['x']) && $_GET['x']<>"" ? redirect('vrs/invite?m='.$vemail.'&mc=1&fc='.$data['fcode']) : "err");

			//=======================================================================================================================

			//====== rdirect to hide email used if dashboard used in vrs ============================================================

			//(isset($_GET['vrs']) && $_GET['vrs']=="1" ? redirect('vrs/invite?m='.$vemail.'&vrs=1') : "err");

			//=======================================================================================================================



			 if(isset($_GET['mc']) && $_GET['mc']=="1" )
			 {

			 	$decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['m']);
	      	 	$emailValue = $this->encryption->decrypt($decryptX);

	      	 	$vemail = filter_var($emailValue, FILTER_SANITIZE_EMAIL);			// Remove all illegal characters from email
		  	    if (filter_var($vemail, FILTER_VALIDATE_EMAIL)) {					// Validate e-mail


		  	    	$getEmail  = $this->vrs_read_model->getRec("v_contact_profile","where email = ? limit 1",$vemail,'');

		  	    	if($getEmail=="") {		//======== email not found =================

		  	    		$sendto = "<EMAIL>";
		  	    		$vfrom = "<EMAIL>";
		  	    		$vcc = "<EMAIL>";
		  	    		$subject = "Email not found from ".$data['getWebCode'];
		  	    		$message = "ATTENTION.....<br><br>Cannot send 'confirmation email' to ".$vemail.".<br>Possible, CITEM db not sync to ".$data['getWebCode']." or not approved.<br><br> From: VRS";

		  	    		$naSend = $this->sendEmailproc($sendto,$vfrom,"",$vcc,"",$subject,$message);

		  	    		$data['errDesc'] = "Send to ".$sendto;
		  	    		if($naSend<>"") {

                            $data['errDesc'] = $naSend." ====== ".$message;
                        }

                        $this->writeError($data['errDesc'],$subject,"","",$data['fcode'],$data['sector'],$vemail,"invite(getEmail)");

                        $data['errorMessage'] = "We are very sorry but there was a problem in Sending Email, please make sure to use a validated email... (inv1)";
		  	    		//die("invalid email");

		  	    		// ===== store email value not existing ========
		  	    		// ======= display message ======================

		  	    	} else {

			  	    	$getrepcode = "A.rep_code ='".$getEmail[0]['rep_code']."' and ";

			            $qryAdd = "";			//"B.validation_status = 'APPROVED TRADE' and ";

			            //========================================================
						 $geTfcode = ($data['getWebCode'] == "" ? "B.fair_code='".$data['fcode']."'" : "B.fair_code like '".$data['getWebCode']."%'");
						//========================================================

			            $getProfile= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","A.rep_code,A.barcode,A.co_name,A.position,A.email,A.email2,A.work_email,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,A.region,B.buyerclass,B.visitor_type,B.visitor_status,B.date_apply,A.pid,B.url_form,B.validation_status,B.validated,B.date_validated,B.emailed,A.continent","A.rep_code = B.rep_code",$getrepcode." ".$qryAdd." ".$geTfcode." and A.deleted='0'  ORDER BY B.validated","");


			            if ($getProfile<>"")
					  		 {
					  		 	foreach($getProfile as $prof2)
					  			  {
					  			   $pid     = $prof2['pid'];
					  			   $repcode = $prof2['rep_code'];
					  			   $barcodeValue = $prof2['barcode'];
					  			   $registeredEmail = $prof2['email'];
								   $mobile = $prof2['mobile'];
								   $vtype = $prof2['visitor_type'];

						           //$preregister = ($prof2['pre_reg'] == "P" ? "P" : "INC");

						           $coname = (trim($prof2['co_name'])=="" ? $this->input->post('coname') : $prof2['co_name']);
						           $fname  = (trim($prof2['cont_per_fn'])=="" ? $this->input->post('fname') : $prof2['cont_per_fn']);
						           $lname  = (trim($prof2['cont_per_ln'])=="" ? $this->input->post('lname') : $prof2['cont_per_ln']);
						           $title1 = (trim($prof2['position'])=="" ? $this->input->post('title1') : $prof2['position']);
						           $ctry   = (trim($prof2['country'])=="" ? $this->input->post('country') : $prof2['country']);
					  			  }


						  		  		switch ($vtype) {
							    				case "TRADE BUYER":
							    					$swValue = "CMESS1";
							    					$confirmationPage = "CPAGE1";
							    					break;
							    				case "GUEST":
							    					$swValue = "CMESS2";
							    					$confirmationPage = "CPAGE1";
							    					break;
							    				case "GENERAL PUBLIC":
							    					$swValue = "CMESS3";
							    					$confirmationPage = "CPAGE1";
							    					break;
							    				case "MEDIA":
							    					$swValue = "CMESS4";
							    					$confirmationPage = "CPAGE1";
							    					break;
							    		}

							    		// ============ default message Confirmation email ==========
							    		//$swValue = "MESS5";
							    		// ==========================================================

							    		$data['project']= $this->master_model->getRec("busmatch_date","where fair_code = ?",strtoupper($data['fcode']),'');

							    		$sendfrom= "<EMAIL>";
										$subj= $data['project'][0]['description'];

							    	    $getContent = $this->master_model->loadRec("v_reference","where switch ='".$swValue."' and exclude=0 AND sector like '%".$data['sector']."%' order by sortfield LIMIT 1");
							    	    $messContent = $getContent[0]['content_message'];
							    	    $messTitle = $getContent[0]['c_profile'];

							    	    // =============== confirmation page message for message.php ==========================
							    	    $getCPage = $this->master_model->loadRec("v_reference","where switch ='".$confirmationPage."' and exclude=0 AND sector like '%".$data['sector']."%' order by sortfield LIMIT 1");
							    	    $data['PageMessage'] = $getCPage[0]['content_message'];
							    	    // =====================================================================================

							    	    // ===== CREATE qrcode ================
						  		  		//$qrcods = $this->createQR($vtype,$title1,$fname,$lname,$registeredEmail,$mobile,$coname,$ctry,$repcode,$data['fcode'],"yes","P");
						  		  		$qrcods = $this->createQR_rcode($barcodeValue,$data['fcode']);

							    	    $qrValue = base_url("idbadge/".$data['fcode']."-".$barcodeValue.".png");

							    	    // =================================================================================
							    		// ======== send email =============================================================

							    	    $qrcode = " <img class='gitna' src='".$qrValue."' alt='Your QRcode' width='150' height='150'> ";

							    	    $proofPaymentURL = "https://citem.com.ph/paymentproof/?loc=email&m=".$registeredEmail."&vcode=".$repcode;

								        $ankor1 = "<a href='".$proofPaymentURL."' style='text-decoration: none; color:blue;' target='_blank'>";
								        $ankor2 = "</a>";


								        $nameProper1 = ucwords(strtolower($fname));
								        $nameProper2 = ucwords(strtolower($lname));



								    	$mess0 = str_replace("{first_name}",$nameProper1,$messContent);
										$mess1 = str_replace("{last_name}",$nameProper2,$mess0);
										$mess2 = str_replace("{co_name}",$coname,$mess1);
										$mess3 = str_replace("{email}",$registeredEmail,$mess2);
										$mess4 = str_replace("{qrcode}",$qrcode,$mess3);
										$mess5 = str_replace("{repcode}",$repcode,$mess4);

										$mess6 = str_replace("{a1}",$ankor1,$mess5);
								        $mess7 = str_replace("{a2}",$ankor2,$mess6);
										//$urlMes = str_replace("{tag2}","</a>",$mess2);

										$vcontact = $fname." ".$lname;

										$data['message'] = $mess7;
										//$message = $this->messageReferer($data['project'][0]['description'],$mess4,"",$data['project'][0]['bannerpic'],$vcontact,$data['sector'],$repcode);

										//die($data['message']);

										//=======================================================
						                $naSend = "";
						                $sendto= $registeredEmail;
						                $vfrom = $data['project'][0]['emailto_registration'];
						                $vcc = $data['project'][0]['emailto'];

						                // ==== REMOVE comment BELOW ============================================================================

				                        $naSend = $this->sendEmailproc($sendto,$vfrom,"",$vcc,"",$data['project'][0]['description'],$data['message']);

				                        // ==== REMOVE comment ABOVE ============================================================================

				                        if($naSend<>"") {

			                                $data['errDesc'] = $naSend."/n/n".$data['message'];
			                                $data['errorMessage'] = "We are very sorry but there was a problem in sending Email, please try again later... (inv2)";
			                                $this->writeError($data['errDesc'],$subj,$repcode,$barcodeValue,$data['fcode'],$data['sector'],$sendto,"invite()");
			                                $updateTable = "0";

			                                //die($data['messageX']);  // ====== landing page here =============
				                        }
				                        else
				                        {

				                        	$data['chkAttendance']= $this->vrs_read_model->getRec("v_attendance","where fair_code='".$data['fcode']."' and rep_code = ? limit 1",$repcode,"");

								  			  if ($data['chkAttendance']=="")
								  		      {

								  	            $this->vrs_model->insertRecTable('v_attendance',$repcode,$barcodeValue,'','',$data['fcode'],$data['sector']);

								  		      }

				                        	$this->vrs_model->updateRecTable('v_attendance','visitor_type',$vtype,$data['fcode'],$data['sector'],$repcode);
								            $this->vrs_model->updateRecTable('v_attendance','pre_reg',"P",$data['fcode'],$data['sector'],$repcode);
								            $this->vrs_model->updateRecTable('v_attendance','date_apply',date('Y-m-d H:i:s'),$data['fcode'],$data['sector'],$repcode);
								            $this->vrs_model->updateRecTable('v_attendance','date_input',date('Y-m-d H:i:s'),$data['fcode'],$data['sector'],$repcode);

				                        }
				                    	// ======== send email =============================================================
					                	//==================================================================================


					  		 } else {

					  		 	$data['errorMessage'] = "We are very sorry but there was a problem in sending Email, record does not exist... (inv3)";

					  		 	//===== not found email ===========
					  		 	//die("email not found");
					  		 	//=================================
					  		 }
					}

		          } else {
		          	//====== email wrong format ======
		          	$data['errorMessage'] = "We are very sorry but there was a problem in sending Email, invalid email address detected... (inv4)";
		          	//================================
		          }


			 } else {

			 	$data['errorMessage'] = "We are very sorry but there was a problem in Sending Email, please try again later... (inv5)";

			 }	//if(isset($_GET['mc']) && $_GET['mc']=="1" )


			 if(isset($data['errorMessage']) && $data['errorMessage']<>"" ) {
			 	// =============== get email template message.php ======================================

	    	    $getCPage = $this->master_model->loadRec("v_reference","where switch ='template' and exclude=0 AND sector like '%".$data['sector']."%' order by sortfield LIMIT 1");
	    	    $data['template'] = $getCPage[0]['content_message'];

	    	    $data['PageMessage'] = str_replace("{message}",$data['errorMessage'],$data['template'] );

	    	    //die("aaaa");

	    	    // =====================================================================================


			 }


			$this->load->view('vrs_message',$data);

	}

	public function editMessage()
	{
		//=== set as active menu in MENU.php ========================
	   	$data['userBar'] = "class='dropdown active'";
	   	//===========================================================

     	$set_data = $this->session->all_userdata();

     	if(!isset($set_data['sessionData']['fcode'])) {redirect('vrs/index');}

		    $data['read_set_value'] = $set_data['sessionData']['realname'];
			$data['sessionRights'] = $set_data['sessionData']['urights'];
			$data['loginuser'] = $set_data['sessionData']['loginuser'];

			$data['sysName'] = $set_data['sessionData']['sysName'];
			$data['systitle'] = $set_data['sessionData']['systitle'];
			$data['eventDB'] = $set_data['sessionData']['eventDB'];

			$data['fcode'] = $set_data['sessionData']['fcode'];
			$data['sector'] = $set_data['sessionData']['sector'];
			$data['fairdesc'] = $set_data['sessionData']['fdesc'];
			$data['diyTerminal'] = $set_data['sessionData']['terminalNo']; //echo "aaa=".$data['fcode']; die();

			$data['myVenue'] = $set_data['sessionData']['venue'];
			$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

			$data['messSetting'] = (isset($_GET['m']) && $_GET['m']<>"" ? $_GET['m'] : ($_POST['messSetting']<>"" ? $_POST['messSetting'] : "1") );

			//die("aaa=".$data['messSetting']);

			if($this->input->post("btn_submit") == "Submit")
			 {
			 	foreach ($data['emailTemplate'] as $rsTemplate) {
          				if($rsTemplate['switch'] == $data['messSetting'] && $this->input->post("btn_submit") == "Submit"){

          						$this->master_model->updateField("v_reference","content_message",$_POST['content_message'] ,"switch='".$data['messSetting']."' AND sector like '%".$data['sector']."%'");

          						// ===== to update recordset for loading ========
          						$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

          				}

              		}

				// if($data['messSetting']=="1") {
				// 	//if($rsNominator=="") {$recID = $this->site_model->insertRecord("v_reference");}
				// 	$this->master_model->updateField("v_reference","content_message",$_POST['messNominator'] ,"switch='MESS1' AND sector like '%".$data['sector']."%'");
				// } else {

				// 	//if($rsNominee=="") {$recID = $this->site_model->insertRecord("v_reference");}
				// 	$this->master_model->updateField("v_reference","content_message",$_POST['messNominee'] ,"switch='MESS2' AND sector like '%".$data['sector']."%'");
				// }
			 }

            if (!is_null($data['emailTemplate'])) {

	          		foreach ($data['emailTemplate'] as $rsTemplate) {
          				if($rsTemplate['switch'] == $data['messSetting']){

          					$data['content_message'] = $rsTemplate['content_message'];
          					$data['content_title']  = $rsTemplate['c_profile'];

          				}

              		}
            }

            //if($this->input->post("btn_submit") == "Preview") {
            if(isset($_GET['prev']) && $_GET['prev']=="1") {

            	 $data['previewMessage']= $data['content_message'];
            	 //$data['previewMessage']= $this->messageReferer($data['fairdesc'],$data['content_message'],"","","",$data['sector'],"");
        	}

			$this->load->view('vrs_message_setting',$data);

	}


    public function viewpreg()
     {
      //==============================================================================================================

      //$this->load->model('event_model');

      //=== load main menu ========================
	  $data['dashBar'] = "class='active'";
	  //===========================================

	  $data['nomenu'] = "";
	  if(isset($_GET['x']))
	  {
		  $fcode = $_GET['fcode'];
		  $data['nomenu'] = "1";
		  $data['vrs_view_preg_dashboard'] = "1";


		  $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['x']);
	      $eValue = $this->encryption->decrypt($decryptX);


	      //===== chk if valid by searching '#' =========
		  //$xValid = strpos($myDay,'#');    					//die("aaa = ". $myDay); //[0]."   bbb= ".$tmp[1]);
		  if($eValue<>"CITEMsmd") {redirect('vrs/index');}
		  //=============================================
		  //die("wazzz-".$fcode);
		  //$tmp = explode('#',$myDay);

		  $getValue = $this->master_model->loadRec("busmatch_date","where fair_code ='".$fcode."' limit 1");

		  	$data['read_set_value'] = "CITEMSMDD";
			$data['sessionRights'] = "2";
			$data['loginuser'] = TRUE;

			$data['sysName'] = 'vrs';
			$data['systitle'] = 'Visitor Registration System';
			$data['eventDB'] = EVENT_DB;

			$data['fcode'] = $fcode;
			$data['sector']= $getValue[0]['sector'];
			$data['fairdesc'] =$getValue[0]['description'];
			$data['diyTerminal'] = 0;

			$data['myVenue'] = "";
			$data['myVenueNum'] = "";
			$data['controller_CI'] = "registration";
			$data['numrights'] = 0;
	  }
	  //===============================================================================================================


       $set_data = $this->session->all_userdata();


	   if ( (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL) || ($eValue=="CITEMsmd") )
		  {
		  	//die("wazzz22");
		  	if(!isset($eValue)) {

			   	  // if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
				  // {
				  	if(!isset($set_data['sessionData']['fcode'])) {redirect('vrs/index');}

				    $data['read_set_value'] = $set_data['sessionData']['realname'];
					$data['sessionRights'] = $set_data['sessionData']['urights'];
					$data['loginuser'] = $set_data['sessionData']['loginuser'];
					$data['userid'] = $set_data['sessionData']['userid'];

					$data['sysName'] = $set_data['sessionData']['sysName'];
					$data['systitle'] = $set_data['sessionData']['systitle'];
					$data['eventDB'] = $set_data['sessionData']['eventDB'];

					$data['fcode'] = $set_data['sessionData']['fcode'];
					$data['sector'] = $set_data['sessionData']['sector'];
					$data['getWebCode'] = $set_data['sessionData']['getWebCode'];

					$data['fairdesc'] = $set_data['sessionData']['fdesc'];
					$data['diyTerminal'] = $set_data['sessionData']['terminalNo']; //echo "aaa=".$data['fcode']; die();

					$data['myVenue'] = $set_data['sessionData']['venue'];
					$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

					$data['RegType']=$this->master_model->loadRec("v_reference","where switch='BSTAT' and sector like '%".$data['sector']."%' order by sortfield");

					$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
					$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

					$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

					$data['baseURL'] = $this->config->base_url();
		            //===========================================
		            //======== get RIGHTS procedure =============
		            //===========================================
		            foreach ($data['userAccessRef'] as $r1)
		            {

		              $founds=0;
		              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

		               $xxx = $set_data['sessionData'][$x."r"];
		               																					// Using ${} is a way to create dynamic variables, ex.====> ${'vrs_'.$r1['c_code']} = "";
		               if ($r1['c_code']==$xxx) { $data['vrs_'.$r1['c_code']] = $xxx; $founds=1; }
		               // ================================================================
		              }
		              if($founds==0) { $data['vrs_'.$r1['c_code']] = ""; }
		              //echo $data['vrs_'.$r1['c_code']]."<br>";

		            }
		            foreach ($data['userAccessMod'] as $r2)
		            {

		              $founds=0;
		              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

		               $xxx = $set_data['sessionData'][$x."r"];
		               // Using ${} is a way to create dynamic variables,
		               if ($r2['c_code']==$xxx) { $data['vrs_'.$r2['c_code']] = $xxx; $founds=1; }
		               // ================================================================
		              }
		              if($founds==0) { $data['vrs_'.$r2['c_code']] = ""; }
		              //echo $data['vrs_'.$r2['c_code']]."<br>";

		            }
		            //================================================================================
		    }

		    if(isset($_POST['validate']) && $_POST['validate']<>"") {		//die("aaa=".$_POST[$validate]); //


		    	$data['project']= $this->master_model->getRec("busmatch_date","where fair_code = ?",strtoupper($data['fcode']),'');


		    	$_GET['d'] = $_POST['xView'];		//die("zz=".$_GET['d']);

		    	$tmp = "visitorType".$_POST["repcode"];
		    	$vtype = $_POST[$tmp];
		    	//$vtype = (isset($_POST[$tmp]) ? $_POST[$tmp] : $_POST['vtype'] ) ;

		    	$updateTable = "1";
		    	//if($_POST['validate'] =="1") { //die("xxx");

			    	// =================================================================================
			    	// ======== get email content ======================================================
			    		if($_POST['validate']=="Send Invite") {
			    			switch ($vtype) {
			    				case "TRADE BUYER":
			    					$swValue = "MESS1";
			    					break;
			    				case "GUEST":
			    					$swValue = "MESS2";
			    					break;
			    				case "GENERAL PUBLIC":
			    					$swValue = "MESS3";
			    					break;
			    				case "MEDIA":
			    					$swValue = "MESS4";
			    					break;
			    			}
			    		}
			    		else if($_POST['validate']=="Send QRcode") {

			    			switch ($vtype) {
			    				case "TRADE BUYER":
			    					$swValue = "CMESS1";
			    					break;
			    				case "GUEST":
			    					$swValue = "CMESS2";
			    					break;
			    				case "GENERAL PUBLIC":
			    					$swValue = "CMESS2";
			    					break;
			    				case "MEDIA":
			    					$swValue = "CMESS4";
			    					break;
			    			}

			    		}
			    		else if($_POST['validate']=="Ask Payment") {

			    			$swValue = "CMESS3";

			    		}
			    		else if($_POST['validate']=="Reset" || $_POST['validate']=="Approve" || $_POST['validate']=="Disapprove") {
			    			$swValue = "";
			    		}
			    		else {
			    			$swValue = "";							//=== disapproved message

			    			die("Please report to SMDD mess(4)");	// must replace with diff template
			    		}

			    		if($swValue<>"") {		//die($_POST['validate']);

							$sendfrom= "<EMAIL>";
							$subj= $data['project'][0]['description'];
				    	    $getContent = $this->master_model->loadRec("v_reference","where switch ='".$swValue."' and exclude=0 AND sector like '%".$data['sector']."%' order by sortfield LIMIT 1");
				    	    $messContent = $getContent[0]['content_message'];
				    	    $messTitle = $getContent[0]['c_profile'];

					    	if($_POST['validate']<>"Reset") {  //die("zzz1");


					    	//========================================================================================================================================
					  //   	$getProfile2  = $this->vrs_read_model->getRec("v_contact_profile","where rep_code = ? limit 1",$_POST["repcode"],'');

							// if($getProfile2<>"") {
			    // 	  		foreach($getProfile2 as $prof2)
					  // 			  {
					  // 			   $repcode = $prof2['rep_code'];
					  // 			   $barcodeValue = $prof2['barcode'];
					  // 			  }
					  // 		} else {
					  // 			die("Please report to SMDD mess(4.1)");
					  // 		}

			    			// ===== CREATE qrcode ================
			  		  		//$qrcods = $this->createQR($vtype,$getEmail[0]['position'],$fname,$lname,$registeredEmail,$mobile,$coname,$ctry,$repcode,$data['fcode'],"yes","P");

					    	$repcode = $_POST["repcode"];

			  		  		$qrcods = $this->createQR_rcode($_POST["barcode"],$data['fcode']);

				    	    $qrValue = base_url("idbadge/".$data['fcode']."-".$_POST["barcode"].".png");

				    	    $qrcode = " <img class='gitna' src='".$qrValue."' alt='Your QRcode' width='150' height='150'> ";

				    	    //die($qrcode);
				    	    //==========================================================================================================================================


					    	// =================================================================================
					    	// ======== send email =============================================================

					   			// switch($data['sector'])
								// {
								// 	case "01":
								// 		$project = "IFEX Philippines";
								// 	break;
								// 	case "02":
								// 		$project = "Manila FAME";
								// 	break;
								// 	case "25":
								// 		$project = "SSX";
								// 	break;
								// }

					    		if($_POST['validate']=="Send QRcode" || $_POST['validate']=="Ask Payment" || $vtype=="GENERAL PUBLIC") {

					    		$inviteURL = "https://citem.com.ph/paymentproof/?loc=email&m=".$_POST['email']."&vcode=".$repcode;

					    		} else {

					    		$inviteURL = base_url("vrs/invite?m=".$this->input->post('email')."&x=".$data['fcode']);
					    		}

								$ankor1 = "<a href='".$inviteURL."' style='text-decoration: none; color:blue;'>";
								$ankor2 = "</a>";

						    	$mess0 = str_replace("{first_name}",$_POST['cont_per_fn'],$messContent);
								$mess1 = str_replace("{last_name}",$_POST['cont_per_ln'],$mess0);
								$mess2 = str_replace("{co_name}",$_POST['co_name'],$mess1);
								$mess3 = str_replace("{email}",$_POST['email'],$mess2);
								$mess4 = str_replace("{url}",$_POST['url'],$mess3);
								$mess5 = str_replace("{a1}",$ankor1,$mess4);
								$mess6 = str_replace("{a2}",$ankor2,$mess5);

								$isQRcode = ( (isset($qrcode) && $qrcode<>"") ? $qrcode : "" );
								$mess7 = str_replace("{qrcode}",$isQRcode,$mess6);

								$isRepcode = ( (isset($repcode) && $repcode<>"") ? $repcode : "" );
								$mess8 = str_replace("{repcode}",$isRepcode,$mess7);


								//$mess4 = str_replace("{tag1}",$urlMess,$mess3);
								//$urlMes = str_replace("{tag2}","</a>",$mess2);

								$vcontact = $_POST['cont_per_fn']." ".$_POST['cont_per_ln'];

								$message = $mess8;

								//$message= $this->messageReferer($data['project'][0]['description'],$mess5,"",$data['project'][0]['bannerpic'],$vcontact,$data['sector'],"");

								//die($message);

								//=======================================================
				                $naSend = "";
				                $sendto= $this->input->post('email');
				                $vfrom = $data['project'][0]['emailto_registration'];
				                $vcc = $data['project'][0]['emailto'];

				                // ==== REMOVE comment BELOW ============================================================================

		                        $naSend = $this->sendEmailproc($sendto,$vfrom,"",$vcc,"",$data['project'][0]['description'],$message);

		                        // ==== REMOVE comment ABOVE ============================================================================

		                        if($naSend<>"") {
		                                //die($naSend);
		                                $data['errDesc'] = $naSend;
		                                $data['messageX'] = "We are very sorry but there was a problem in Sending Email, please try again later... (v1)";
		                                $this->writeError($data['errDesc'],$subj,$_POST["repcode"],$_POST["barcode"],$data['fcode'],$data['sector'],$sendto,"viewpreg()");
		                                $updateTable = "0";
		                        }
		                        else
		                        {
		                        	$data['messageX'] = $messTitle." Email was sent";
		                        	$updateTable = "1";

		                        	//========================================================
							 		$geTfcode = ($data['getWebCode'] == "" ? "fair_code='".$data['fcode']."'" : "fair_code like '".$data['getWebCode']."%'");
							 		//========================================================

							 		if($_POST['validate']=="Send QRcode" || $_POST['validate']=="Ask Payment") {

							 			$updateTable = "0";
							 			$geTfcode = "fair_code='".$data['fcode']."'";
							 		}


		                        	$this->vrs_model->loadRec2("update v_attendance set emailed = emailed + 1 where "."rep_code='".$_POST["repcode"]."' and ".$geTfcode." and sector='".$data['sector']."'","update");
		                        }
		                    // ======== send email =============================================================
			                //==================================================================================

			                }
		                }

                //}

                		if($vtype<>"" && $updateTable == "1") {

                				$vValue    = ($_POST['validate']=="Approve" ? "3" : "2");
                				$validated = ($_POST['validate']=="Reset" ? "0" : ($_POST['validate']=="Send Invite" ? "0" : $vValue) );
                				//$resetPreg = ($_POST['validate']=="Reset" ? "" : ($_POST['validate']=="Send Invite" ? "0" : $vValue) );

                				if ($_POST['validate']=="Send Invite") {

                					$this->vrs_model->execQry("UPDATE v_attendance SET visitor_type = '".$vtype."' WHERE fair_code like '".$data['getWebCode']."%' AND sector = '".$data['sector']."' AND rep_code = ".$_POST["repcode"]);
                			 	} else {

					    			$this->vrs_model->updateRecTable("v_attendance","visitor_type",$vtype,$data['fcode'],$data['sector'],$_POST["repcode"]);
					    		}

					    		$this->vrs_model->updateRecTable("v_attendance","validated",$validated,$data['fcode'],$data['sector'],$_POST["repcode"]);
					    		$this->vrs_model->updateRecTable("v_attendance","validated_by",$data['userid'],$data['fcode'],$data['sector'],$_POST["repcode"]);

					    		if($_POST['validate']=="Reset") {
					    			$this->vrs_model->updateRecTable("v_attendance","pre_reg","",$data['fcode'],$data['sector'],$_POST["repcode"]);
					    		}

    					}

		    }
		    // ======== END validate Sending =======================================================================================

            if(isset($_GET['d']) && $_GET['d']<>"")
			   	{
			      //die($_GET['d']);

			   	  $data ['xdash'] = (isset($_GET['x']) ? $_GET['x'] : "");		//die($data['xDash']);
				  $data['xView'] = $_GET['d'];
				  $data['ViewHeader'] = "";

				  //if(isset($_GET['item_code']) && $_GET['item_code']<>"") {}

			      //$this->load->library('encrypt2');
				  //$myDay = $this->encrypt2->decode2($_GET['d']);

				  $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['d']);
        		  $myDay = $this->encryption->decrypt($decryptX);


				  //===== chk if valid by searching '#' =========
				  $xValid = strpos($myDay,'#');    					//die("aaa = ". $myDay); //[0]."   bbb= ".$tmp[1]);
				  if($xValid=="") {redirect('vrs/index');}
				  //=============================================

				  $tmp = explode('#',$myDay);           			//die("aaa = ". $tmp[0]); // TRADE BUYER_and date(date_apply) = value

				  $data['vType'] = $tmp[0];

				  $qryDate = ($tmp[1]<>"" ? $tmp[1]." and " : "");

				  if($tmp[0]=='VISITORALL')
				   {$qryAdd = "(B.visitor_type LIKE '%GUEST%' or B.visitor_type LIKE '%GENERAL PUBLIC%' or B.visitor_type LIKE '%MEDIA%') and ".$qryDate;}
				  else if($tmp[0]=='VISITORwBUYER')
				   {$qryAdd = "(B.visitor_type LIKE '%GUEST%' or B.visitor_type LIKE '%GENERAL PUBLIC%' or B.visitor_type LIKE '%TRADE BUYER%' or B.visitor_type LIKE '%MEDIA%') and ".$qryDate;}

				  // =================== app_systemApproved from project websites ===============================================

				  else if($tmp[0]=='app_systemApproved_View')
				   {$qryAdd = "B.validation_status = 'APPROVED TRADE' and ";}

				  else if($tmp[0]=='app_systemNotApproved_View')
				   {$qryAdd = "(B.validation_status <> 'APPROVED TRADE' and B.validation_status <> '' and B.validation_status IS NOT NULL) and ";}


				  // ============================================================================================================

				  else if($tmp[0]=='NeedToValidate')
				   {$qryAdd = "B.validated <> '4' ".$qryDate;}		// 4 - > para show 2 & 3 	// $qryAdd = "B.validated = '0' ".$qryDate;

				  else if($tmp[0]=='disapproved')
				   {$qryAdd = "B.validated = '2' ".$qryDate;}		// 2 -> disapproved ==== 3 - > approved

				  else
				   {
					   	$chkvalid = "";
				 		if($data['vrs_view_preg_dashboard']<>"")
				 		{
				 			//$chkvalid ="and B.validated = '3'";		// 3 ---> approved
				 		}

					   	$qryAdd = "B.visitor_type LIKE '%".$tmp[0]."%' ".$chkvalid." ".$qryDate;
				   }

				   	 $data['chkWhatToDisplayInColHeader'] = $tmp[0];

					 $data['tableVIEW'] = "";			//die("aaa=".$tmp[2]);
					 $data['vrsMode'] = "";

					 if($tmp[2] == "0")
					 {
					 	$data['ViewHeader'] = "Paid Visitors";
					 	//$data['results']= $this->vrs_read_model->joinTable("v_contact_profile","v_payment_order","*","v_contact_profile.rep_code = v_payment_order.rep_code","deleted= '0' and ".$qryAdd." fair_code = '".$data['fcode']."'","");
					 	$data['results']= $this->vrs_read_model->joinTable("v_contact_profile","v_payment_order","*","v_contact_profile.rep_code = v_payment_order.rep_code","deleted= '0' and v_payment_order.status='Paid' and v_payment_order.fair_code = '".$data['fcode']."'","");
					 }
					 // else if($tmp[2] == "1")
					 // {
					 // 	$data['ViewHeader'] = "Incomplete Registration";
					 // 	$data['tableVIEW'] = "1";
					 // 	$data['vrsMode'] = "2";
						// $data['results']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","A.rep_code,A.co_name,A.email,A.email2,A.work_email,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,B.emailed,B.buyerclass,B.date_apply,B.visitor_type,B.url_form","A.rep_code = B.rep_code",$qryAdd." A.deleted= '0' and B.fair_code = '".$data['fcode']."'","");
					 // }
					 else if($tmp[2] == "3" || $tmp[2] == "5" || $tmp[2] == "1" )
					 {
					 	$data['ViewHeader'] = $tmp[3]; 			//"Pre-Registered"		"Need to Validate"		"Incomplete Registration"
					 	$data['tableVIEW'] = "1";
					 	$data['vrsMode'] =  ($tmp[0]=='app_systemApproved_View' ? "3" :  ($tmp[2] == "1" ? "2" : "3") );

					 	//========================================================
					 	$geTfcode = ($data['getWebCode'] == "" ? "B.fair_code='".$data['fcode']."'" : "B.fair_code like '".$data['getWebCode']."%'");
					 	//========================================================

					 	//$data['vt'] = "";
					 	if($tmp[0]=="TRADE BUYER" || $tmp[0]=="MEDIA" || $tmp[0]=="GUEST" || $tmp[0]=="VISITOR" || $tmp[0]=="GENERAL PUBLIC")
					 		{

					 			$geTfcode = "B.fair_code='".$data['fcode']."'";
					 		}

					 	if($tmp[0]=="GENERAL PUBLIC") {

					 		$data['varResults']= $this->vrs_read_model->joinTable3("v_contact_profile as A","v_attendance as B","citemcom_epayment.tbl_onlinepayment as C","A.rep_code,A.barcode,A.co_name,A.email,A.email2,A.work_email,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,A.region,B.buyerclass,B.visitor_type,B.visitor_status,B.date_apply,A.pid,B.url_form,B.validation_status,B.validated,B.date_validated,B.pre_reg,B.emailed,A.continent,C.status as name","A.rep_code = B.rep_code","B.rep_code = C.rep_code",$qryAdd." A.deleted= '0' and ".$geTfcode." ORDER BY B.validated","LEFT");

					 	} else {

					 		$data['varResults']= $this->vrs_read_model->joinTable3("v_contact_profile as A","v_attendance as B",MASTER_DB.".v_users as C","A.rep_code,A.barcode,A.co_name,A.email,A.email2,A.work_email,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,A.region,B.buyerclass,B.visitor_type,B.visitor_status,B.date_apply,A.pid,B.url_form,B.validation_status,B.validated,B.date_validated,B.pre_reg,B.emailed,A.continent,C.name","A.rep_code = B.rep_code","B.validated_by = C.id",$qryAdd." A.deleted= '0' and ".$geTfcode." ORDER BY B.validated","LEFT");
					 	}



					 	//$data['rsPreReg'] = $this->vrs_read_model->loadRec("v_attendance","where pre_reg='P' AND fair_code='".$data['fcode']."' AND sector='".$data['sector']."'");

					 	$data['rsPreReg'] = $this->vrs_read_model->loadRec2("select rep_code from v_attendance where pre_reg='P' AND fair_code='".$data['fcode']."' AND sector='".$data['sector']."'");

					 	$data['rsChkPaid'] = $this->vrs_read_model->loadRec2("select rep_code from citemcom_epayment.tbl_onlinepayment where status='Paid' and fair_code='".$data['fcode']."'");

					 	//print_r($data['varResults']); die();


					 	$data['results'] = array();
					 	if($data['varResults']<>"") {
					 		foreach($data['varResults'] as $rs1) {

					 		$temp = $this->encryption->encrypt($rs1['email']);
    						$vpid= str_replace(array('+', '/', '='), array('-', '_', '^'), $temp);

    					  	$data['results'][] = array("pid"=>$vpid,"rep_code"=>$rs1['rep_code'],"barcode"=>$rs1['barcode'],"co_name"=>$rs1['co_name'],"email"=>$rs1['email'],"email2"=>$rs1['email2'],"work_email"=>$rs1['work_email'],"mobile"=>$rs1['mobile'],"cont_per_fn"=>$rs1['cont_per_fn'],"cont_per_ln"=>$rs1['cont_per_ln'],"country"=>$rs1['country'],"buyerclass"=>$rs1['buyerclass'],"visitor_type"=>$rs1['visitor_type'],"visitor_status"=>$rs1['visitor_status'],"date_apply"=>$rs1['date_apply'],"url_form"=>$rs1['url_form'],"validated"=>$rs1['validated'],"date_validated"=>$rs1['date_validated'],"emailed"=>$rs1['emailed'],"continent"=>$rs1['continent'],"region"=>$rs1['region'],"validation_status"=>$rs1['validation_status'],"pre_reg"=>$rs1['pre_reg'],
    					  		"name"=>$rs1['name']);
    						}
    					}

					 		//$table1,$table2,$table3,$fields,$on2,$on3,$where,$joinKind)

						//$data['results']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","A.rep_code,A.co_name,A.email,A.email2,A.work_email,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,B.buyerclass,B.visitor_type,B.visitor_status,B.date_apply,B.url_form,B.validated,B.date_validated,B.emailed","A.rep_code = B.rep_code",$qryAdd." A.deleted= '0' and B.fair_code = '".$data['fcode']."'",""); //and B.pre_reg='P'


						// $data['results'] = $this->vrs_read_model->loadRec2("SELECT A.rep_code,A.co_name,A.email,A.email2,A.work_email,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,B.buyerclass,B.date_apply,C.subject,C.remarks,C.func_module FROM ".$data['eventDB'].".v_contact_profile AS A
						// 	 INNER JOIN ".$data['eventDB'].".v_attendance AS B ON B.rep_code = A.rep_code
						// 	 INNER JOIN ".$data['eventDB'].".v_email_send_status AS C ON C.rep_code = A.rep_code
						// 	 WHERE ".$qryAdd." A.deleted='0' and C.fair_code LIKE '".$data['fcode']."%' and B.fair_code LIKE '".$data['fcode']."%' and B.pre_reg='P'
						// 	 AND C.sector LIKE '%".$data['sector']."%'");


					 }
					 else if($tmp[2] == "4")
					 {
					 	$data['ViewHeader'] = "Arrival";
					 	$data['tableVIEW'] = "1";
					 	$data['vrsMode'] = "3";
						//$data['results'] = $this->vrs_read_model->loadRec2("SELECT * FROM v_contact_profile WHERE ".$qryAdd." deleted='0' and reg_status='T' and trade_code LIKE '".$data['fcode']."%'");
						$data['results']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","A.rep_code,A.barcode,A.co_name,A.email,A.email2,A.work_email,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,A.continent,A.region,A.pid,A.add_st,A.add_city,A.zipcode,A.email2,A.webpage,B.pre_reg,B.buyerclass,B.date_apply,B.visitor_type,B.validated,B.emailed,B.date_validated,B.sector as name","A.rep_code = B.rep_code",$qryAdd." A.deleted= '0' and A.reg_status='T' and B.fair_code = '".$data['fcode']."'","");

						//print_r($data['results']); die();

					 }

					 else  				//========== same as $tmp[2] == "0" ===============
					 {
					 	$data['ViewHeader'] = "Paid Visitors....TEMP!";
					 	$data['tableVIEW'] = "1";
						$data['results'] = $this->vrs_read_model->joinTable("v_contact_profile","v_payment_order","*","v_contact_profile.rep_code = v_payment_order.rep_code","deleted= '0' and ".$qryAdd." status='Paid' and fair_code = '".$data['fcode']."'","");
					 }

				 }
            //===========================================

			//========== get as of DATE for dashboard ===================================================
			$data['asofDate'] = date('F d, Y h:i A');
			//==========================================================================================

		  }
	   else
	      {
			redirect('vrs/index');
		  }
	   //die("zzz= ".$data['xDash']);
	   $this->load->view('vrs_view',$data);
	 }


     public function dashboard()	//===== ***** PROBLEM ****** IF VISITOR COUNTED IN DASHBOARD, BLANK IN CHARTS / MUST NOT BE INCLUDED IN COUNT
     {								//==============================================================================================================

      $this->load->model('event_model');

      //=== load main menu ========================
	  $data['dashBar'] = "class='active'";
	  //===========================================


	  //====== url for dashborad only =================================================================================
	  //===============================================================================================================

	  //$encVal = $this->encryption->encrypt("CITEMsmd");
	  //$encTmp = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);  die($encTmp);
	  // x = 3b4c774694f4fc0f5aed086ff74ad3a48b1652cd23f8a811aef429134b1eb21fc5429ed8bd2cffc60dbf22ba56591927e7df9595387175754c07ae51cbef8eeejpgTduEP2RwFOeTU_wQfigD7PdJ0U5RHmD20NLmePus^

	  $data['nomenu'] = "";
	  $data['viewPregEncyrptCode'] = ""; 											// ====== for use viewpreg.php if no session =========================

	  if(isset($_GET['x']))
	  {
		  $fcode = $_GET['fcode'];
		  $data['nomenu'] = "1";
		  $data['vrs_view_preg_dashboard'] = "1";

		  $data['viewPregEncyrptCode'] = "&fcode=".$fcode."&x=".$_GET['x'];		// ====== for use viewpreg.php if no session =========================


		  $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['x']);
	      $eValue = $this->encryption->decrypt($decryptX);


	      //===== chk if valid by searching '#' =========
		  //$xValid = strpos($myDay,'#');    					//die("aaa = ". $myDay); //[0]."   bbb= ".$tmp[1]);
		  if($eValue<>"CITEMsmd") {redirect('vrs/index');}
		  //=============================================
		  //die("wazzz= ".$eValue);
		  //$tmp = explode('#',$myDay);

		  $getValue = $this->master_model->loadRec("busmatch_date","where fair_code ='".$fcode."' limit 1");

		  	$data['read_set_value'] = "CITEMSMDD";
			$data['sessionRights'] = "2";
			$data['loginuser'] = TRUE;

			$data['sysName'] = 'vrs';
			$data['systitle'] = 'Visitor Registration System';
			$data['eventDB'] = EVENT_DB;

			$data['fcode'] = $fcode;
			$data['sector']= $getValue[0]['sector'];
			$data['getWebCode']= trim($getValue[0]['url_busmatch']);		// get fair_code of event website

			$data['fairdesc'] =$getValue[0]['description'];
			$data['diyTerminal'] = 0;

			$data['myVenue'] = "";
			$data['myVenueNum'] = "";
			$data['controller_CI'] = "registration";
			$data['numrights'] = 0;
	  }
	  //===============================================================================================================


       $set_data = $this->session->all_userdata();

	   if ( (isset($set_data['sessionData']) && isset($set_data['sessionData']['realname'])  && $set_data['sessionData']['realname'] != NULL) || (isset($eValue) && $eValue=="CITEMsmd") )
		  {
		  	//die("wazzz22");
		  	if(!isset($eValue)) {

				  	if(!isset($set_data['sessionData']['fcode'])) {redirect('vrs/index');}

				    $data['read_set_value'] = $set_data['sessionData']['realname'];
					$data['sessionRights'] = $set_data['sessionData']['urights'];
					$data['loginuser'] = $set_data['sessionData']['loginuser'];

					$data['sysName'] = $set_data['sessionData']['sysName'];
					$data['systitle'] = $set_data['sessionData']['systitle'];
					$data['eventDB'] = $set_data['sessionData']['eventDB'];

					$data['fcode'] = $set_data['sessionData']['fcode'];
					$data['sector'] = $set_data['sessionData']['sector'];
					$data['getWebCode'] = $set_data['sessionData']['getWebCode'];
					$data['fairdesc'] = $set_data['sessionData']['fdesc'];
					$data['diyTerminal'] = $set_data['sessionData']['terminalNo']; //echo "aaa=".$data['fcode']; die();

					$data['myVenue'] = $set_data['sessionData']['venue'];
					$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

					$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
					$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

					$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");


		            //===========================================
		            //======== get RIGHTS procedure =============
		            //===========================================
		            foreach ($data['userAccessRef'] as $r1)
		            {

		              $founds=0;
		              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

		               $xxx = $set_data['sessionData'][$x."r"];
		               																					// Using ${} is a way to create dynamic variables, ex.====> ${'vrs_'.$r1['c_code']} = "";
		               if ($r1['c_code']==$xxx) { $data['vrs_'.$r1['c_code']] = $xxx; $founds=1; }
		               // ================================================================
		              }
		              if($founds==0) { $data['vrs_'.$r1['c_code']] = ""; }
		              //echo $data['vrs_'.$r1['c_code']]."<br>";

		            }
		            foreach ($data['userAccessMod'] as $r2)
		            {

		              $founds=0;
		              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

		               $xxx = $set_data['sessionData'][$x."r"];
		               // Using ${} is a way to create dynamic variables,
		               if ($r2['c_code']==$xxx) { $data['vrs_'.$r2['c_code']] = $xxx; $founds=1; }
		               // ================================================================
		              }
		              if($founds==0) { $data['vrs_'.$r2['c_code']] = ""; }
		              //echo $data['vrs_'.$r2['c_code']]."<br>";

		            }
		            //===========================================
		            //===========================================

            } // !isset($eValue)


			//========== get as of DATE for dashboard ===================================================
			$data['asofDate'] = date('F d, Y h:i A');
			//===========================================================================================


			$day1 ="";$day2 ="";$day3 ="";$day4 =""; $subs ="";

			//if ($data['vrs_view_preg_dashboard']<>"") {}

			$getday = (isset($_GET['d']) ? $_GET['d'] : ($data['vrs_view_preg_dashboard']<>"" ? "prereg" : "all"));

			$data['getCO'] = $this->master_model->loadRec("v_reference","where switch='cutof' and exclude='0' and sector LIKE '%".$data['sector']."%'");
			if($data['getCO']<>"") {
				foreach ($data['getCO'] as $rs)
				{
				  switch($rs['c_profile'])
				   {
				   	  case "DAY 1":
				   	   $day1 = $rs['c_code'];
				   	  break;
				   	  case "DAY 2":
				   	   $day2 = $rs['c_code'];
				   	  break;
				   	  case "DAY 3":
				   	   $day3 = $rs['c_code'];
				   	  break;
				   	  case "DAY 4":
				   	   $day4 = $rs['c_code'];
				   	  break;
				   	  case "Subscribers":
				   	   $subs = $rs['c_code'];
				   	  break;
				   }
				}
			}


			//if($vdate[1]=='DAY 1'){ $vexp='<=';} else {$vexp='=';}
			//$this->vrs_model->updateAllRecord("v_contact_profile","cutoff","1","date(date_apply)".$vexp."'".$vdate[0]."' and reg_status='T' and deleted='0' and trade_code='".$data['fcode']."'");

			//$chkvalid = "";
			//$area = "";

			//if($data['vrs_view_preg_dashboard']<>"") {
			//	$chkvalid =" and B.validation_status = 'APPROVED TRADE'";		// dati === > $chkvalid =" and B.validated = '3'";

			//}

			switch($getday)
			 {
			 	case "all":
			 	  $vexp="and B.reg_status='T'";
			 	  $vexp1=""; $data['dashboardHeader'] = 'Cumulative';
				  $vpub="and period='CUMULATIVE'";
				  $rstat= "B.reg_status='T'";
				  $vinc = "";
				break;
				case "prereg":
			 	  $vexp="and B.pre_reg='P'";
			 	  $vexp1=""; $data['dashboardHeader'] = 'Pre-Registered';
				  $vpub="";
				  $rstat= "B.pre_reg='P'";			// .$chkvalid;
				  $vinc ="B.pre_reg='INC'";
				  //$area ="B.pre_reg='P'";
				break;
			 	default:	//case "subscriber":
				  $vexp="and B.reg_status='F' and B.subscription='Yes' and B.pre_reg='' ";
				  $vexp1=""; $data['dashboardHeader'] = 'Subscribers';
				  $vpub="";
				  $rstat= "B.reg_status='F' and B.subscription='Yes' and B.pre_reg='' ";
				  $vinc = "B.reg_status='F' and (ISNULL(B.subscription) or B.subscription='') and B.pre_reg='' ";



				//default:
				  //$vexp="and date(B.date_apply)="."'".$getday."'";  $data['dashboardHeader'] = $getday;
			 	  //$vexp1="and date(B.date_apply)="."'".$getday."'";
			 	  //$vpub="and period="."'".$getday."'";
			 	  //$rstat= "B.reg_status='T'";
			 	  //$vinc = "";
			 	/*
			 	case "1":
			 	  $vexp="and date(B.date_apply)<="."'".$day1."'"; $data['dashboardHeader'] = 'DAY 1';
			 	  $vexp1="and date(B.date_apply)<="."'".$day1."'"; $data['dashboardHeader'] = 'DAY 1';
			 	  $vpub="and period="."'".$day1."'";
			 	  $rstat= "B.reg_status='T'";
			 	break;
			 	case "2":
			 	  $vexp="and date(B.date_apply)="."'".$day2."'";  $data['dashboardHeader'] = 'DAY 2';
			 	  $vexp1="and date(B.date_apply)="."'".$day2."'";  $data['dashboardHeader'] = 'DAY 2';
			 	  $vpub="and period="."'".$day2."'";
			 	  $rstat= "B.reg_status='T'";
			 	break;
			 	case "3":
			 	  $vexp="and date(B.date_apply)="."'".$day3."'";  $data['dashboardHeader'] = 'DAY 3';
			 	  $vexp1="and date(B.date_apply)="."'".$day3."'";  $data['dashboardHeader'] = 'DAY 3';
			 	  $vpub="and period="."'".$day3."'";
			 	  $rstat= "B.reg_status='T'";
			 	break;
			 	case "4":
			 	  $vexp="and date(B.date_apply)="."'".$day4."'";  $data['dashboardHeader'] = 'DAY 4';
			 	  $vexp1="and date(B.date_apply)="."'".$day4."'";  $data['dashboardHeader'] = 'DAY 4';
			 	  $vpub="and period="."'".$day4."'";
			 	  $rstat= "B.reg_status='T'";
			 	break;
			 	*/
			 }


			//$rstat = ($data['vrs_view_preg_dashboard']=="" ? "B.reg_status='T'" : "B.pre_reg='P'");    // ===== if vrs_view_preg_dashboard has value then SHOW PREREG IN DASHBOARD ONLY
			//die($rstat);

			//======================= SUMMARY ARRIVAL ===========================
			//$data['results'] = $this->vrs_read_model->loadRec2("SELECT visitor_type,count(trade_code) as tcode FROM v_contact_profile WHERE  ".$rstat." ".$vexp." AND deleted='0' and trade_code LIKE '".$data['fcode']."%' GROUP by visitor_type");


			$data['results']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","B.visitor_type,count(B.fair_code) as tcode","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1."  and B.fair_code = '".$data['fcode']."' GROUP by B.visitor_type","");

			// ====== chk if faircode(url_busmatch) for event website exist ===============
			   $geTfcode = ($data['getWebCode'] == "" ? "B.fair_code='".$data['fcode']."'" : "B.fair_code like '".$data['getWebCode']."%'");
			// ============================================================================

			$data['app_systemApproved']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","B.visitor_type,count(B.fair_code) as tcode","A.rep_code = B.rep_code","A.deleted= '0' and ".$geTfcode." and B.validation_status = 'APPROVED TRADE'","");

			$data['app_systemNotApproved']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","B.visitor_type,count(B.fair_code) as tcode","A.rep_code = B.rep_code","A.deleted= '0' and ".$geTfcode." and (B.validation_status <> 'APPROVED TRADE' and B.validation_status <> '' and B.validation_status IS NOT NULL )","");

			//$data['forValidation']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","B.visitor_type,count(B.fair_code) as tcode","A.rep_code = B.rep_code","A.deleted= '0' and B.pre_reg='P' and B.fair_code = '".$data['fcode']."' and B.validated = '0'","");

			//$data['Disapproved']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","B.visitor_type,count(B.fair_code) as tcode","A.rep_code = B.rep_code","A.deleted= '0' and B.pre_reg='P' and B.fair_code = '".$data['fcode']."' and B.validated = '2'","");

			$data['pendingREG']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","B.visitor_type,count(B.fair_code) as tcode","A.rep_code = B.rep_code","A.deleted= '0' ".($vinc=='' ? "" : "and ").$vinc." ".$vexp1."  and B.fair_code = '".$data['fcode']."'","");

			//$data['pendingREG'] = $this->vrs_read_model->loadRec2("SELECT visitor_type,count(trade_code) as tcode FROM v_contact_profile WHERE  pre_reg='INC' ".$vexp." AND deleted='0' and trade_code LIKE '".$data['fcode']."%' ");


			$data['paidREG']= $this->vrs_read_model->joinTable("v_contact_profile","v_payment_order","count(trade_code) as tcode","v_contact_profile.rep_code = v_payment_order.rep_code","deleted= '0' and v_payment_order.status='Paid' and v_payment_order.fair_code = '".$data['fcode']."'","");

			//$data['genpubNew']= $this->event_model->loadRec("v_datachart"," where switch='genpub' ".$vpub." order by sortfield");

			$data['paidCount'] = $data['paidREG'][0]['tcode'];		//die("zzz= ".$data['paidCount']);

			$data['IncompleteRegistration'] = $data['pendingREG'][0]['tcode'];

			$data['tb'] = 0;
			$data['visitor'] = 0;
			$data['guest'] = 0;
			$data['genpub'] = 0;
			$data['media'] = 0;

			$data['TBdaySelected'] = 0;
			$data['VdaySelected'] = 0;
			$data['GPdaySelected'] = 0;
			$data['MdaySelected'] = 0;
			$data['totalVisitor'] = 0;

			// ================================ DASHBOARD FOR PRE-REG ==========================================================
			 $vPreg = "4";
			 if($data['vrs_view_preg_dashboard']<>"")  // ===== if has value SHOW PREREG IN DASHBOARD ONLY
			 {
			 	$vPreg = "3";		//	"3";
			 }
			//die("aaa = ".$data['vrs_view_preg_dashboard']);
			// ================================ DASHBOARD FOR PRE-REG ==========================================================

			if($data['results']<>"")
			{
			  foreach($data['results'] as $vtype)
			  {
			   switch ($vtype['visitor_type'])
			    {
				  case "TRADE BUYER":
					$data['tb'] = $vtype['tcode'];
					//$data['TBdaySelected'] = $this->encrypt2->encode2("TRADE BUYER#".$vexp);

					$encVal = $this->encryption->encrypt("TRADE BUYER#".$vexp."#".$vPreg."#".$data['dashboardHeader']);
        			$data['TBdaySelected'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);
				  break;
				  case "GUEST":
				  case "VISITOR":
					$data['visitor'] = $vtype['tcode'];
					//$encVal = $this->encryption->encrypt("VISITOR#".$vexp."#".$vPreg);
					$encVal = $this->encryption->encrypt("GUEST#".$vexp."#".$vPreg."#".$data['dashboardHeader']);
        			$data['VdaySelected'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);
				  break;
				  case "GENERAL PUBLIC":

				  	if($data['fcode']<>"TNK2017")
				  	 {
					  $data['genpub'] = $vtype['tcode'];
					  //$data['GPdaySelected'] = $this->encrypt2->encode2("GENERAL PUBLIC#".$vexp);

					  $encVal = $this->encryption->encrypt("GENERAL PUBLIC#".$vexp."#".$vPreg."#".$data['dashboardHeader']);
        			  $data['GPdaySelected'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);
					 }

				  break;
				  case "MEDIA":
					$data['media'] = $vtype['tcode'];
					//$data['MdaySelected'] = $this->encrypt2->encode2("MEDIA#".$vexp);

					$encVal = $this->encryption->encrypt("MEDIA#".$vexp."#".$vPreg."#".$data['dashboardHeader']);
        			$data['MdaySelected'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);
				  break;
				}
			  }
			  //====== visitor and gen pub combined ==============================
			  	$data['totalVisitor'] = $data['visitor']; //+ $data['genpub'];

			  	//$data['VdaySelected']        = $this->encrypt2->encode2("VISITORALL#".$vexp."#0");


			  	//if ($data["vrs_view_preg_dashboard"])


			  	if($data["vrs_view_preg_dashboard"]<>"")  		//view PREREG in dashboard in selected					//($data['fcode'] == "LSPH2018")
			  	{
			  		$encVal = $this->encryption->encrypt("VISITORALL#".$vexp."#3"."#".$data['dashboardHeader']);
			  	}
			  	else
			  	{
			  		if($data["vrs_disable_payment"]<>"")
			  		{
			  			$encVal = $this->encryption->encrypt("VISITORALL#".$vexp."#4#");
			  		}
			  		else
			  		{
			  			$encVal = $this->encryption->encrypt("VISITORALL#".$vexp."#0#");		// disable payment is selected
			  		}
			  	}

			  	//=====??????
        		//$data['VdaySelected'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);
        		//=====??????
			  //==================================================================
			}

			//$data['VdaySelectedINCPREG'] = $this->encrypt2->encode2("VISITORALL#".$vexp."#1");

			if($getday=='subscriber') {
		 			$encVal = $this->encryption->encrypt("VISITORwBUYER#".$vinc."#3"."#Incomplete Registration -".$data['dashboardHeader']);
		 			$data['VdaySelectedINCPREG'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);
			}
			else {
					$encVal = $this->encryption->encrypt("VISITORwBUYER#".$vinc."#1"."#Incomplete Registration -".$data['dashboardHeader']);
		    		$data['VdaySelectedINCPREG'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);
			}

			// =================== app_systemApproved from project websites ===============================================
			$encVal = $this->encryption->encrypt("app_systemApproved_View##5"."#Member");
		    $data['app_systemApproved_View'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);

		    $encVal = $this->encryption->encrypt("app_systemNotApproved_View##5"."#Not Member");
		    $data['app_systemNotApproved_View'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);



			// =================== For validation =========================================================================
			$encVal = $this->encryption->encrypt("NeedToValidate#".$vexp."#5"."#Not Validated");
		    $data['NotValidatedSelected'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);
		    // ============================================================================================================

		    $encVal = $this->encryption->encrypt("disapproved#".$vexp."#5"."#Disapproved");
		    $data['disapprovedSelected'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);

		  	//$data['VdaySelectedPAID']    = $this->encrypt2->encode2("VISITORALL#".$vexp."#2");
		  	$encVal = $this->encryption->encrypt("VISITORwBUYER#".$vexp."#0#");
    		$data['VdaySelectedPAID'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);


			//======== for MANUAL input general public ==========================
			if($data['fcode']=="TNK2017")
			{
				$data['genpubNew']= $this->event_model->loadRec("v_datachart"," where switch='genpub' ".$vpub." and sector LIKE '%".$data['sector']."%' order by sortfield");
				if($data['genpubNew']<>"")
				{
				  	foreach($data['genpubNew'] as $vgenpub)
				  	{
				  		$data['genpub'] = $vgenpub['wtc_hall'];
						//$data['GPdaySelected'] = $this->encrypt2->encode2("GENERAL PUBLIC#".$vpub);
						$encVal = $this->encryption->encrypt("GENERAL PUBLIC#".$vpub);
        				$data['GPdaySelected'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);
				  	}
				}
			}
			//===================================================================

			//=================== end SUMMARY ARRIVAL ===========================

			//=================== AREA GRAPH ====================================
			if($data['fcode']<>"TNK2017") {

				$previous = $this->event_model->loadRec2("SELECT tradebuyers,visitors+genpub as nontrade,media,period from v_datachart where day='".$getday."' and switch ='area' and exclude=0 and sector LIKE '%".$data['sector']."%' order by sortfield");

				if($previous<>"")
				{
					$arrCount = count($previous) + 1;
				}
				else
				{$arrCount = 1;}


				$current = array(
							 $arrCount => array(
	                         'tradebuyers' => $data['tb'],
	                         'nontrade' => $data['visitor']+$data['genpub'],
	                         'media' => $data['media'],
	                         'period' => date('Y-m-d')
	                         )
	                       );


				if($previous=="") {$data['area'] = $current;}
				else {
				  $data['area'] = array_merge($previous,$current);     //print_r($data['area']); die();
				 }

				//========== DISABLE AREA IF CREATE PHILS ===================================================================

                if($data['sector']=="20")  {$data['area']="";}

				//===========================================================================================================

				//die(print_r($data['area']));

				// ========= DASHBOARD FOR PRE-REG ===============

				if($data['vrs_view_preg_dashboard']<>"")  // ===== if has value SHOW PREREG IN DASHBOARD ONLY
					{
						//$data['asofDate'] = date('F d, Y h:i A');
						//$pregcount = $this->event_model->loadRec2("SELECT tradebuyers,visitors+genpub as nontrade,media,period from v_datachart where day='".$getday."' and switch ='area' and exclude=0 and sector LIKE '%".$data['sector']."%' order by sortfield");

						//$data['rsDate'] = $this->vrs_read_model->loadRec2("SELECT DATE_FORMAT(date_apply,'%Y-%m-%d') as dates,count(trade_code) as tcode FROM v_contact_profile WHERE  ".$rstat." ".$vexp." AND deleted='0' and trade_code LIKE '".$data['fcode']."%' GROUP by DATE(date_apply)");

						$data['rsDate']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","DATE_FORMAT(B.date_apply,'%Y-%m-%d') as dates,count(B.fair_code) as tcode","A.rep_code = B.rep_code","A.deleted= '0' ".$vexp." ".$vexp1."  and B.fair_code = '".$data['fcode']."' GROUP by DATE(B.date_apply)","");


					}
				// ========= DASHBOARD FOR PRE-REG ===============

			}

			if($data['fcode']<>"TNK2017") {
			//========== foot traffic ==================
				$data['prevTraffic'] = $this->event_model->loadRec2("SELECT wtc_hall,wtc_tent,pttc,hall1,period from v_datachart where switch ='trapik' and exclude=0 order by sortfield");

				if($data['prevTraffic']<>"")
			 	{
			   	  foreach($data['prevTraffic'] as $rs2)
			   	  {
			 	   	$data['wtc_hall'] = $rs2['wtc_hall'];
			 	    $data['wtc_tent'] = $rs2['wtc_tent'];
			 	    $data['pttc'] = $rs2['pttc'];
			 	    $data['hall1'] = $rs2['hall1'];
			      }
			    }
			}
			//print_r($data['prevTraffic']); die();

			//=================== END AREA GRAPH ====================================


			//=================== BAR GRAPH =========================================
			//die("aaa = ".$data['fcode']);
			if($data['sector']=="02") {    // || $data['sector']=="01"	$data['fcode']<>"TNK2017"

				//$data['top10'] = $this->vrs_read_model->loadRec2("SELECT country,count(trade_code) as total1 FROM v_contact_profile WHERE ".$rstat." ".$vexp."  AND deleted='0' and country<>'Philippines' and visitor_type='TRADE BUYER' and trade_code LIKE '".$data['fcode']."%' GROUP by country ORDER BY total1 DESC LIMIT 10");

				$data['top10']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","country,count(B.fair_code) as total1","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1." and country<>'Philippines' and B.visitor_type='TRADE BUYER' and B.fair_code = '".$data['fcode']."' GROUP by country ORDER BY total1 DESC ","");  // --- LIMIT 10
			} else {

				//$data['top10'] = $this->vrs_read_model->loadRec2("SELECT country,count(trade_code) as total1 FROM v_contact_profile WHERE ".$rstat." ".$vexp."  AND deleted='0' and trade_code LIKE '".$data['fcode']."%' GROUP by country ORDER BY total1 DESC LIMIT 10");

				$data['top10']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","country,count(B.fair_code) as total1","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1." and B.fair_code = '".$data['fcode']."' GROUP by country ORDER BY total1 DESC ","");  	// --- LIMIT 10
			}
			//print_r($data['top10']); die();
			//=================== END BAR GRAPH ====================================


			//=================== DONUT CHART BUYER CLASS =========================================
			//die("aaa = ".$data['fcode']);
			//if($data['fcode']=="TNK2017") {

				$data['bclass']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","B.buyerclass as label,count(B.fair_code) as value","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1." and B.buyerclass<>'' and B.fair_code = '".$data['fcode']."' GROUP by label ORDER BY value DESC","");

				//$data['bclass'] = $this->vrs_read_model->loadRec2("SELECT buyerclass as label,count(trade_code) as value FROM v_contact_profile WHERE buyerclass<>'' and ".$rstat." ".$vexp."  AND deleted='0' and trade_code LIKE '".$data['fcode']."%' GROUP by label ORDER BY value DESC");

			//print_r($data['bclass']); die();
			//}
			//=================== END BAR GRAPH ====================================


			//=================== DONUT CHART STATUS (regular/new) =================
			if($data['sector']=="02"|| $data['sector']=="01") {
				//$data['visitType'] = $this->vrs_read_model->loadRec2("SELECT buyer_type as label,count(trade_code) as value FROM v_contact_profile WHERE ".$rstat." ".$vexp." AND deleted='0' and visitor_type='TRADE BUYER' and trade_code LIKE '".$data['fcode']."%' GROUP by label ORDER BY value DESC");

				$data['visitType']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","B.visitor_status as label,count(B.fair_code) as value","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1." and B.visitor_type='TRADE BUYER' and B.fair_code = '".$data['fcode']."' GROUP by label ORDER BY value DESC","");
			} else {
				//$data['visitType'] = $this->vrs_read_model->loadRec2("SELECT if(buyer_type='REGULAR','REGULAR','NEW') as label,count(trade_code) as value FROM v_contact_profile WHERE ".$rstat." ".$vexp." AND deleted='0' and trade_code LIKE '".$data['fcode']."%' GROUP by label ORDER BY value DESC");

				$data['visitType']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","if(B.visitor_status='REGULAR','REGULAR','NEW') as label,count(B.fair_code) as value","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1."  and B.fair_code = '".$data['fcode']."' GROUP by label ORDER BY value DESC","");
			}
			//=================== END DONUT CHART =========================================

			//=================== DONUT CHART TYPE (foreign/local) =================
			if($data['sector']=="02"|| $data['sector']=="01") {
				//$data['buyerType2'] = $this->vrs_read_model->loadRec2("SELECT if(UCASE(country)<>'PHILIPPINES','FOREIGN','LOCAL') as BUYERTYPE, SUM(IF(UCASE(buyer_type)='NEW',1,0 )) AS NEW, SUM(IF(UCASE(buyer_type)='REGULAR',1,0 )) AS REGULAR, count(buyer_type) as total1 FROM v_contact_profile WHERE ".$rstat." ".$vexp." AND deleted='0' and visitor_type='TRADE BUYER' and trade_code LIKE '".$data['fcode']."%' GROUP by BUYERTYPE");

				$data['buyerType2']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","if(UCASE(country)<>'PHILIPPINES','Foreign','Local') as BUYERTYPE, SUM(IF(UCASE(B.visitor_status)='NEW',1,0 )) AS NEW, SUM(IF(UCASE(B.visitor_status)='REGULAR',1,0 )) AS REGULAR, count(B.visitor_status) as total1","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1." and B.visitor_type='TRADE BUYER' and B.fair_code = '".$data['fcode']."' GROUP by BUYERTYPE","");

			} else {

				//$data['buyerType2'] = $this->vrs_read_model->loadRec2("SELECT if(UCASE(country)<>'PHILIPPINES','FOREIGN','LOCAL') as BUYERTYPE,  count(trade_code) as total1 FROM v_contact_profile WHERE ".$rstat." ".$vexp." AND deleted='0' and trade_code LIKE '".$data['fcode']."%' GROUP by BUYERTYPE");

				$data['buyerType2']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","if(UCASE(country)<>'PHILIPPINES','Foreign','Local') as BUYERTYPE, count(B.fair_code) as total1","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1."  and B.fair_code = '".$data['fcode']."' GROUP by BUYERTYPE","");
			}
			$data['foreign'] = 0;
			$data['local'] = 0;
			if($data['buyerType2']<>"")
			 {
			   foreach($data['buyerType2'] as $btype2)
			   {
			 	switch ($btype2['BUYERTYPE'])
			 	 {
			 	   case "Foreign":
			 	   	$data['foreign'] = $btype2['total1'];
			 	   break;
			 	   case "Local":
			 	    $data['local'] = $btype2['total1'];
			 	   break;
			 	 }
			   }
			 }
			//=================== END DONUT CHART =========================================


			 //$rstatNew = ($data['vrs_view_preg_dashboard']=="" ? "b.reg_status='T'" : "d.pre_reg='P'");
			 //$rstat    = ($data['vrs_view_preg_dashboard']=="" ? "A.reg_status='T'" : "B.pre_reg='P'");

			//=================== DONUT CHART STATUS (JOB FUNCTION) =================
			if($data['fcode']<>"TNK2017") {
				//$data['jobfunc'] = $this->vrs_model->loadRec2("SELECT remarks as label,count(fair_code) as value FROM v_job_function WHERE fair_code LIKE '".$data['fcode']."%' GROUP by label ORDER BY value DESC");

				// $data['jobfunc'] = $this->vrs_model->loadRec2("SELECT a.remarks as label, count(a.fair_code) as value FROM v_job_function as a inner join v_contact_profile as b ON a.rep_code = b.rep_code WHERE b.deleted='0' and a.fair_code LIKE '".$data['fcode']."%' and b.trade_code LIKE '".$data['fcode']."%' and reg_status='T' ".$vexp." GROUP by label ORDER BY value DESC");

				$data['jobfunc'] = $this->vrs_read_model->loadRec2("SELECT D.item_code as xcode, count(D.fair_code) as value, C.c_profile as label FROM ".$data['eventDB'].".v_job_function AS D
					 INNER JOIN ".MASTER_DB.".v_reference AS C ON C.c_code = D.item_code
					 INNER JOIN ".$data['eventDB'].".v_contact_profile AS A ON A.rep_code = D.rep_code
					 INNER JOIN ".$data['eventDB'].".v_attendance AS B ON B.rep_code = A.rep_code
					 WHERE A.deleted='0' and D.fair_code LIKE '".$data['fcode']."%' and B.fair_code LIKE '".$data['fcode']."%' and ".$rstat." and C.exclude=0
					 AND C.sector LIKE '%".$data['sector']."%' ".$vexp1." GROUP by label ORDER BY value DESC");

			}

			//=================== DONUT CHART STATUS (NATURE OF BUSINESS) =================
			if($data['fcode']<>"TNK2017") {
				// $data['repre'] = $this->vrs_read_model->loadRec2("SELECT a.item_code as xcode, count(a.fair_code) as value, c.c_profile as label FROM ".$data['eventDB'].".v_representation AS a
				// 	 INNER JOIN citem_masterfile.v_reference AS c ON c.c_code = a.item_code
				// 	 INNER JOIN ".$data['eventDB'].".v_contact_profile AS b ON a.rep_code = b.rep_code
				// 	 WHERE b.deleted='0' and a.fair_code LIKE '".$data['fcode']."%' and b.trade_code LIKE '".$data['fcode']."%' and ".$rstat." and c.exclude=0
				// 	 AND c.sector LIKE '%".$data['sector']."%' ".$vexp1." GROUP by label ORDER BY value DESC");

				$data['repre'] = $this->vrs_read_model->loadRec2("SELECT D.item_code as xcode, count(D.fair_code) as value, C.c_profile as label FROM ".$data['eventDB'].".v_representation AS D
					 INNER JOIN ".MASTER_DB.".v_reference AS C ON C.c_code = D.item_code
					 INNER JOIN ".$data['eventDB'].".v_contact_profile AS A ON A.rep_code = D.rep_code
					 INNER JOIN ".$data['eventDB'].".v_attendance AS B ON B.rep_code = A.rep_code
					 WHERE A.deleted='0' and D.fair_code LIKE '".$data['fcode']."%' and B.fair_code LIKE '".$data['fcode']."%' and ".$rstat." and C.exclude=0
					 AND C.sector LIKE '%".$data['sector']."%' ".$vexp1." GROUP by label ORDER BY value DESC");

			}

			//=================== DONUT CHART STATUS (GENDER) =================
			if($data['fcode']<>"DTCP2020") {

				//$data['gender']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","A.gender as label,count(B.fair_code) as value","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1." and B.fair_code = '".$data['fcode']."' GROUP by label ORDER BY value DESC","");

				$data['gender']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","if(gender<>'',if(gender<>'Male','Female','Male'),'N/A') as label,count(B.fair_code) as value","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1." and B.fair_code = '".$data['fcode']."' GROUP by label ORDER BY value ASC","");

				//$data['buyerType2']= $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","if(UCASE(country)<>'PHILIPPINES','FOREIGN','LOCAL') as BUYERTYPE, count(B.fair_code) as total1","A.rep_code = B.rep_code","A.deleted= '0' and ".$rstat." ".$vexp1."  and B.fair_code = '".$data['fcode']."' GROUP by BUYERTYPE","");

			}
			//=================== DONUT CHART STATUS (Market Segments) =================
			if($data['fcode']<>"TNK2017") {
				// $data['msegment'] = $this->vrs_model->loadRec2("SELECT a.remarks as label, count(a.fair_code) as value FROM v_mn_marketsegment as a inner join v_contact_profile as b ON a.rep_code = b.rep_code WHERE b.deleted='0' and a.fair_code LIKE '".$data['fcode']."%' and b.trade_code LIKE '".$data['fcode']."%' and reg_status='T' ".$vexp." GROUP by label ORDER BY value DESC");

				$data['msegment'] = $this->vrs_read_model->loadRec2("SELECT D.item_code as xcode, count(D.fair_code) as value, C.c_profile as label FROM ".$data['eventDB'].".v_mn_marketsegment AS D
					 INNER JOIN ".MASTER_DB.".v_reference AS C ON C.c_code = D.item_code
					 INNER JOIN ".$data['eventDB'].".v_contact_profile AS A ON A.rep_code = D.rep_code
					 INNER JOIN ".$data['eventDB'].".v_attendance AS B ON B.rep_code = A.rep_code
					 WHERE A.deleted='0' and D.fair_code LIKE '".$data['fcode']."%' and B.fair_code LIKE '".$data['fcode']."%' and ".$rstat." and C.exclude=0
					 AND C.sector LIKE '%".$data['sector']."%' ".$vexp1." GROUP by label ORDER BY value DESC");
			}
			//=================== DONUT CHART STATUS (Annual Sales Volume) =================
			if($data['fcode']<>"TNK2017") {

				$data['annualsales'] = $this->vrs_read_model->loadRec2("SELECT D.item_code as xcode, count(D.fair_code) as value, C.c_profile as label FROM ".$data['eventDB'].".v_mn_annualsales AS D
					 INNER JOIN ".MASTER_DB.".v_reference AS C ON C.c_code = D.item_code
					 INNER JOIN ".$data['eventDB'].".v_contact_profile AS A ON A.rep_code = D.rep_code
					 INNER JOIN ".$data['eventDB'].".v_attendance AS B ON B.rep_code = A.rep_code
					 WHERE A.deleted='0' and D.fair_code LIKE '".$data['fcode']."%' and B.fair_code LIKE '".$data['fcode']."%' and ".$rstat." and C.exclude=0
					 AND C.sector LIKE '%".$data['sector']."%' ".$vexp1." GROUP by label ORDER BY value DESC");
			}
			//=================== DONUT CHART STATUS (Organization) =================
			if($data['fcode']<>"TNK2017") {

				$data['organization'] = $this->vrs_read_model->loadRec2("SELECT D.item_code as xcode, count(D.fair_code) as value, C.c_profile as label FROM ".$data['eventDB'].".v_organization AS D
					 INNER JOIN ".MASTER_DB.".v_reference AS C ON C.c_code = D.item_code
					 INNER JOIN ".$data['eventDB'].".v_contact_profile AS A ON A.rep_code = D.rep_code
					 INNER JOIN ".$data['eventDB'].".v_attendance AS B ON B.rep_code = A.rep_code
					 WHERE A.deleted='0' and D.fair_code LIKE '".$data['fcode']."%' and B.fair_code LIKE '".$data['fcode']."%' and ".$rstat." and C.exclude=0
					 AND C.sector LIKE '%".$data['sector']."%' ".$vexp1." GROUP by label ORDER BY value DESC");

			}
			//=================== DONUT CHART STATUS (Learn about the Event) =================
			if($data['fcode']<>"TNK2017") {

				$data['informthru'] = $this->vrs_read_model->loadRec2("SELECT D.item_code as xcode, count(D.fair_code) as value, C.c_profile as label FROM ".$data['eventDB'].".v_informthru AS D
					 INNER JOIN ".MASTER_DB.".v_reference AS C ON C.c_code = D.item_code
					 INNER JOIN ".$data['eventDB'].".v_contact_profile AS A ON A.rep_code = D.rep_code
					 INNER JOIN ".$data['eventDB'].".v_attendance AS B ON B.rep_code = A.rep_code
					 WHERE A.deleted='0' and D.fair_code LIKE '".$data['fcode']."%' and B.fair_code LIKE '".$data['fcode']."%' and ".$rstat." and C.exclude=0
					 AND C.sector LIKE '%".$data['sector']."%' ".$vexp1." GROUP by label ORDER BY value DESC");

			}
			//=================== DONUT CHART STATUS (Reason to Visit) =================
			if($data['fcode']<>"TNK2017") {

				$data['showreason'] = $this->vrs_read_model->loadRec2("SELECT D.item_code as xcode, count(D.fair_code) as value, C.c_profile as label FROM ".$data['eventDB'].".v_showreason AS D
					 INNER JOIN ".MASTER_DB.".v_reference AS C ON C.c_code = D.item_code
					 INNER JOIN ".$data['eventDB'].".v_contact_profile AS A ON A.rep_code = D.rep_code
					 INNER JOIN ".$data['eventDB'].".v_attendance AS B ON B.rep_code = A.rep_code
					 WHERE A.deleted='0' and D.fair_code LIKE '".$data['fcode']."%' and B.fair_code LIKE '".$data['fcode']."%' and ".$rstat." and C.exclude=0
					 AND C.sector LIKE '%".$data['sector']."%' ".$vexp1." GROUP by label ORDER BY value DESC");

			}
			//=================== DONUT CHART STATUS (Product Of Interest) =================
			if($data['fcode']<>"TNK2017") {
			 	//$data['prodcatX'] = $this->vrs_read_model->loadRec2("SELECT a.remarks_major as label, count(a.fair_code) as value FROM v_genproducts as a inner join v_contact_profile as b ON a.rep_code = b.rep_code WHERE b.deleted='0' and a.fair_code LIKE '".$data['fcode']."%' and b.trade_code LIKE '".$data['fcode']."%' and reg_status='T'  GROUP by label ORDER BY value DESC");

				$data['prodcat']  = $this->vrs_read_model->loadRec2("SELECT D.rep_code as rcode,C.c_profile as pmajor FROM v_genproducts as D
					INNER JOIN ".MASTER_DB.".v_reference AS C ON C.c_code = D.prod_cat
					INNER JOIN v_contact_profile as A ON D.rep_code = A.rep_code
					INNER JOIN v_attendance as B ON A.rep_code = B.rep_code
					WHERE A.deleted='0' and C.exclude='0' and D.fair_code LIKE '".$data['fcode']."%' and B.fair_code LIKE '".$data['fcode']."%' and ".$rstat." ".$vexp1." ORDER BY D.rep_code,C.c_profile");



			}
			// echo "<pre>";
			// print_r($data['prodcatX']);
			// echo "</pre>";


			$data['pcategory'] = "";

			if($data['prodcat']<>"")
			{
				$ctr=0;
				$aaa=0;
				$p1 =''; $p2 = '';
				foreach ($data['prodcat'] as $r2) {

					if($p1 == $r2['rcode'] && $p2 == $r2['pmajor']) { $aaa=1;} else { $aaa=0;}

					if($aaa==0)
						{
							$ArrayProd[$ctr] = array("label" => $r2['pmajor'],"value" => "1");
							$p1 = $r2['rcode'];
							$p2 = $r2['pmajor'];
							//$aaa=0;
							$ctr ++;
						}
					//$sessionArray = array_merge($sessionArray,$sessionArrayT1[$ctr]);
				}

				$totals = array();
				$product2="";
				foreach ($ArrayProd as $row) {
	  				$product2 = $row['label'];
	  				$quantity = $row['value'];

	  				@$totals[$product2] += $quantity;   // @ symbol to suppress the notice error
				}

				$data['pcategory'] = array();
				foreach ($totals as $product => $quantity) {
	  				$data['pcategory'][] = array('label' => $product, 'value' => $quantity);
				}

				// echo "<br><br>";
				// echo "<pre>";
				// print_r($ArrayProd);
				// echo "</pre>";

				// echo "<pre>";
				// print_r($data['pcategory']) ;
				// echo "</pre>";
				// die();

			} // if($data['prodcat']<>"")

		  }
	   else
	      {
	      	//die("wazzzxx");
			redirect('vrs/index');
		  }
	   $this->load->view('vrs_dashboard',$data);
	 }

	 public function dashboardSetting()
     {

       $this->load->model('event_model');

	   //=== set as active menu in MENU.php ========================
	   $data['userBar'] = "class='dropdown active'";
	   //===========================================================
       $set_data = $this->session->all_userdata();

	   if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {
		      $data['read_set_value'] = $set_data['sessionData']['realname'];
			  $data['sessionRights'] = $set_data['sessionData']['urights'];
			  $data['username'] = $set_data['sessionData']['user'];
			  $data['loginuser'] = $set_data['sessionData']['loginuser'];

			  $data['sysName'] = $set_data['sessionData']['sysName'];
			  $data['systitle'] = $set_data['sessionData']['systitle'];
		  	  $data['eventDB'] = $set_data['sessionData']['eventDB'];

			  $data['fcode'] = $set_data['sessionData']['fcode'];
		 	  $data['sector'] = $set_data['sessionData']['sector'];
			  $data['fairdesc'] = $set_data['sessionData']['fdesc'];
			  $data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

			  $data['myVenue'] = $set_data['sessionData']['venue'];
			  $data['myVenueNum'] = $set_data['sessionData']['venueNum'];
			  //chk if admin =====================
			  if ($data['sessionRights']<>'1') {redirect('vrs/main');}
			  //==================================
			  //get the posted values

			  $data['userId'] = $this->input->post("userId");

			  if ($this->input->post("btn_userUpdate") == "Delete")
		 		{
		   			$this->event_model->deleteUserTable("v_datachart",$data['userId']);
		   			redirect('vrs/dashboardsetting');
		 		}

		 	  $data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			  $data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			  $data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");


			  // ============== get General public ===============
			  $data['gpub1'] = 0; $data['gpub2'] = 0; $data['gpub3'] = 0; $data['totGenpub'] = 0;
			  $data['genpubNew']= $this->event_model->loadRec("v_datachart"," where switch='genpub' and sector LIKE '%".$data['sector']."%' order by sortfield");

			  if ($data['genpubNew']<>"")
			  {
			  foreach ($data['genpubNew'] as $rs1) {
			  	switch($rs1['day'])
			   	 {
			   	  case "1":
			   	   $data['gpub1'] = $rs1['wtc_hall'];
			   	  break;
			   	  case "2":
			   	   $data['gpub2'] = $rs1['wtc_hall'];
			   	  break;
			   	  case "3":
			   	   $data['gpub3'] = $rs1['wtc_hall'];
			   	  break;
			   	  case "4":
			   	   $data['totGenpub'] = $rs1['wtc_hall'];
			   	  break;
			   	 }
			  	}
			  }
			  if($this->input->post("btn_userUpdate")=="Save")
			  	{
			  		$totgpub = 0;
			  		for ($x=1; $x<=3; $x++) {

			  			$chkDay = $this->event_model->loadRec("v_datachart","where day='".$x."' and switch='genpub' and sector LIKE '%".$data['sector']."%'");

			  			if($chkDay=="")
			  			{
			  				$recID = $this->event_model->insertRecord("v_datachart");
			  				$this->event_model->updateAllRecord("v_datachart","wtc_hall",$this->input->post("gpub".$x),"id=".$recID);
			  				$this->event_model->updateAllRecord("v_datachart","switch","genpub","id=".$recID);
			  				$this->event_model->updateAllRecord("v_datachart","sector",$data['sector'],"id=".$recID);
			  				$this->event_model->updateAllRecord("v_datachart","day",$x,"id=".$recID);
			  				$this->event_model->updateAllRecord("v_datachart","sortfield",$x,"id=".$recID);

			  				/*$chkCUMULATIVE = $this->event_model->loadRec("v_datachart","period='CUMULATIVE' and switch='genpub' and sector LIKE '%".$data['sector']."%'");
			  				if($chkCUMULATIVE=="")
			  				{
			  					$recID2 = insertRecord("v_datachart");
			  					$this->event_model->updateAllRecord("v_datachart","period","CUMULATIVE","id=".$recID2);
			  					$this->event_model->updateAllRecord("v_datachart","switch","genpub","id=".$recID2);
			  					$this->event_model->updateAllRecord("v_datachart","sector",$data['sector'],"id=".$recID);
			  				}*/
			  			}
			  			else
			  			{
			  	  			$this->event_model->updateAllRecord("v_datachart","wtc_hall",$this->input->post("gpub".$x),"day='".$x."' and switch='genpub' and sector LIKE '%".$data['sector']."%'");
			  	  		}

			  	  		$totgpub = $totgpub + $this->input->post("gpub".$x);
			  	  	}

			  	  	$chkCUMULATIVE = $this->event_model->loadRec("v_datachart","where period='CUMULATIVE' and switch='genpub' and sector LIKE '%".$data['sector']."%'");
			  		if($chkCUMULATIVE=="")
			  			{
			  				$recID2 = $this->event_model->insertRecord("v_datachart");
			  				$this->event_model->updateAllRecord("v_datachart","period","CUMULATIVE","id=".$recID2);
			  				$this->event_model->updateAllRecord("v_datachart","day",$x,"id=".$recID2);
			  				$this->event_model->updateAllRecord("v_datachart","switch","genpub","id=".$recID2);
			  				$this->event_model->updateAllRecord("v_datachart","sector",$data['sector'],"id=".$recID2);
			  				$this->event_model->updateAllRecord("v_datachart","wtc_hall",$totgpub,"id=".$recID2);
			  				$this->event_model->updateAllRecord("v_datachart","sortfield",$x,"id=".$recID2);
			  			}
			  		else
			  			{
			  	  			$updateTot = $this->event_model->updateAllRecord("v_datachart","wtc_hall",$totgpub,"period='CUMULATIVE' and switch='genpub' and sector LIKE '%".$data['sector']."%'");
			  	  		}
			  	  	//die("aaa =".$updateTot);

			  	  redirect('vrs/dashboardsetting');
			  	}

			  // ==============END  get General public ===============



			  // ============== get foot trafrfic ===============
			  $data['wtc1']	= 0; $data['wtc2']	= 0; $data['wtc3']	= 0;
			  $data['tent1'] = 0; $data['tent2'] = 0; $data['tent3'] = 0;
			  $data['pttc1'] = 0; $data['pttc2'] = 0; $data['pttc3'] = 0;
			  $data['hall11'] = 0; $data['hall12'] = 0; $data['hall13'] = 0;

			  $data['footTrafic']= $this->event_model->loadRec("v_datachart"," where switch='trapik' and sector LIKE '%".$data['sector']."%' order by sortfield");

			  if($data['footTrafic']<>"")
			  {
			  foreach ($data['footTrafic'] as $rs1) {
			  	switch($rs1['day'])
			   	 {
			   	  case "1":
			   	   $data['wtc1'] = $rs1['wtc_hall']; $data['tent1'] = $rs1['wtc_tent']; $data['pttc1'] = $rs1['pttc']; $data['hall11'] = $rs1['hall1'];
			   	  break;
			   	  case "2":
			   	   $data['wtc2'] = $rs1['wtc_hall']; $data['tent2'] = $rs1['wtc_tent']; $data['pttc2'] = $rs1['pttc']; $data['hall12'] = $rs1['hall1'];
			   	  break;
			   	  case "3":
			   	   $data['wtc3'] = $rs1['wtc_hall']; $data['tent3'] = $rs1['wtc_tent']; $data['pttc3'] = $rs1['pttc']; $data['hall13'] = $rs1['hall1'];
			   	  break;
			   	 }
			  	}
			  }
			  if($this->input->post("btn_userUpdate")=="Submit")
			  	{
			  		for ($x=1; $x<=3; $x++) {
			  	  		$this->event_model->updateAllRecord("v_datachart","wtc_hall",$this->input->post("wtc".$x),"day='".$x."' and switch='trapik' and sector LIKE '%".$data['sector']."%'");
			  	  		$this->event_model->updateAllRecord("v_datachart","wtc_tent",$this->input->post("tent".$x),"day='".$x."' and switch='trapik' and sector LIKE '%".$data['sector']."%'");
			  	  		$this->event_model->updateAllRecord("v_datachart","pttc",$this->input->post("pttc".$x),"day='".$x."' and switch='trapik' and sector LIKE '%".$data['sector']."%'");
			  	  		$this->event_model->updateAllRecord("v_datachart","hall1",$this->input->post("hall1".$x),"day='".$x."' and switch='trapik' and sector LIKE '%".$data['sector']."%'");
			  	  	}
			  	  redirect('vrs/dashboardsetting');
			  	}
			  	// $data['wtcTotal'] = $data['wtc1']+$data['wtc2']+$data['wtc3'];
			  	// $data['tentTotal'] = $data['tent1']+$data['tent2']+$data['tent3'];
			  	// $data['pttcTotal'] = $data['pttc1']+$data['pttc2']+$data['pttc3'];
			  	// $data['hall1Total'] = $data['hall11']+$data['hall12']+$data['hall13'];

			  // ==============END  get foot trafrfic ===============

			  $data['vReference']= $this->event_model->loadRec("v_datachart"," where switch='area' and sector LIKE '%".$data['sector']."%' order by year(period) DESC,month(period) DESC,day ASC");  // cut-off
			  $data['RefProfile']= $this->event_model->loadRec("v_datachart","where id ='".$data['userId']."'");

			  $data['refTitle']="History Arrival Settings";
			  $data['userId']= "";
			  $data['vtradebuyers']= "";
	          $data['vvisitors']= "";
			  $data['vgenpub']= "";
			  $data['vmedia']= "";
			  $data['vday']= "";
			  $data['vperiod']= "";
			  $data['vsortfield']= "";
			  $data['vexclude']= "";


			  $btn_press= $this->input->post("btn_userUpdate");
			  if ($btn_press != "")
			    {$data['updateButon'] = "Update";}
			  else {$data['updateButon'] = "Add";}
			  //$this->initUser();

			  if(isset($data['RefProfile']) && $data['RefProfile'] != "")
			  {
			   foreach($data['RefProfile'] as $r1)
			   {
			     $data['userId'] = $r1['id'];
			     $data['vtradebuyers'] = $r1['tradebuyers'];
				 $data['vvisitors'] = $r1['visitors'];
				 $data['vgenpub'] = $r1['genpub'];
				 $data['vmedia'] = $r1['media'];
				 $data['vday']= $r1['day'];
				 $data['vperiod'] = $r1['period'];
				 $data['vsortfield'] = $r1['sortfield'];
				 $data['vexclude'] = $r1['exclude'];
			   }
			  }

			  if($this->input->post("btn_userUpdate")=="Add" || $this->input->post("btn_userUpdate")=="Update")
			  	{
			  		$this->form_validation->set_error_delimiters('<div>', '</div>');
			  		$this->form_validation->set_rules('tradebuyers', 'tradebuyers', 'trim|required');
	   		  		$this->form_validation->set_rules('visitors', 'visitors', 'trim|required');
	   		  		$this->form_validation->set_rules('genpub', 'genpub', 'trim|required');
					$this->form_validation->set_rules('media', 'media', 'trim|required');
					$this->form_validation->set_rules('day', 'Day', 'trim|required');
					$this->form_validation->set_rules('period', 'preriod', 'trim|required');
	   		  		$this->form_validation->set_rules('sortfield', 'Sort field', 'trim|required');
	   		  		$this->form_validation->set_rules('exclude', 'Exclude field', 'trim|required');
	   		  	}
	   		  if ($this->form_validation->run() == FALSE)
            	{
               		//validation fails
			   		if($this->input->post("btn_userUpdate")=="Add")
			   			{$data['updateButon'] = "Add";}

			   		$this->load->view('vrs_dashboard_setting',$data);
            	}
              else
                {
                	$switch = "area";
                	if ($this->input->post("btn_userUpdate") == "Add")
			    		{
			      			$data['userId'] = $this->event_model->insertRecord("v_datachart");
						}

							$this->event_model->updateAllRecord("v_datachart","tradebuyers",$this->input->post("tradebuyers"),"id=".$data['userId']);
			      			$this->event_model->updateAllRecord("v_datachart","visitors",$this->input->post("visitors"),"id=".$data['userId']);
			      			$this->event_model->updateAllRecord("v_datachart","genpub",$this->input->post("genpub"),"id=".$data['userId']);
			      			$this->event_model->updateAllRecord("v_datachart","media",$this->input->post("media"),"id=".$data['userId']);
			      			$this->event_model->updateAllRecord("v_datachart","day",$this->input->post("day"),"id=".$data['userId']);
			      			$this->event_model->updateAllRecord("v_datachart","period",$this->input->post("period"),"id=".$data['userId']);
			      			$this->event_model->updateAllRecord("v_datachart","sortfield",$this->input->post("sortfield"),"id=".$data['userId']);
			      			$this->event_model->updateAllRecord("v_datachart","exclude",$this->input->post("exclude"),"id=".$data['userId']);
			      			$this->event_model->updateAllRecord("v_datachart","switch","area","id=".$data['userId']);
			      			$this->event_model->updateAllRecord("v_datachart","sector",$data["sector"],"id=".$data['userId']);

					redirect('vrs/dashboardsetting');
                }

			}
	   else
	        {
		  //$data['read_set_value'] = 'Please Set Session Value First !';
			  redirect('vrs/index');
		    }

	   //$this->load->view('vrs_reference',$data);
	 }


	 function search($search_terms = '', $start = 0)
	 {
	   //$this->load->model('vrs_read_model');

       $set_data = $this->session->all_userdata();

	 if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {
			$data['read_set_value'] = $set_data['sessionData']['realname'];
			$data['sessionRights'] = $set_data['sessionData']['urights'];
			$data['username'] = $set_data['sessionData']['user'];
			$data['loginuser'] = $set_data['sessionData']['loginuser'];

	      //$data['userCtry'] = $set_data['sessionData']['ucountry'];
			$data['fcode'] = $set_data['sessionData']['fcode'];
			$data['sector'] = $set_data['sessionData']['sector'];
			$data['fairdesc'] = $set_data['sessionData']['fdesc'];
			$data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

			$data['myVenue'] = $set_data['sessionData']['venue'];
			$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			$data['sysName'] = $set_data['sessionData']['sysName'];
			$data['systitle'] = $set_data['sessionData']['systitle'];
			$data['eventDB'] = $set_data['sessionData']['eventDB'];

			$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

			$data['RegStatus']=$this->master_model->loadRec("v_reference","where switch='MN6' order by c_profile");
			$data['RegType']=$this->master_model->loadRec("v_reference","where switch='BSTAT' and sector like '%".$data['sector']."%' order by sortfield");
			//$data["exportForm"] = "";

			//echo CI_VERSION; die();

            //===========================================
            //======== get RIGHTS procedure =============
            //===========================================
            foreach ($data['userAccessRef'] as $r1)
            {

              $founds=0;
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

               $xxx = $set_data['sessionData'][$x."r"];
               																					// Using ${} is a way to create dynamic variables, ex.====> ${'vrs_'.$r1['c_code']} = "";
               if ($r1['c_code']==$xxx) { $data['vrs_'.$r1['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r1['c_code']] = ""; }
              //echo $data['vrs_'.$r1['c_code']]."<br>";

            }
            foreach ($data['userAccessMod'] as $r2)
            {

              $founds=0;
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {

               $xxx = $set_data['sessionData'][$x."r"];
               // Using ${} is a way to create dynamic variables,
               if ($r2['c_code']==$xxx) { $data['vrs_'.$r2['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r2['c_code']] = ""; }
              //echo $data['vrs_'.$r2['c_code']]."<br>";

            }
            //===========================================
            // ================================ DASHBOARD FOR PRE-REG ==========================================================

			//die("zzz=".$_POST['searchrec']);
			//if(isset($_POST['searchrec']) && $_POST['searchrec']=="1") {}

            //$viewPreregDashboard = ($data['vrs_view_preg_dashboard']<>"" ? "prereg" : "");    // ===== if vrs_view_preg_dashboard has value then SHOW PREREG IN DASHBOARD ONLY
            //$viewPreregDashboard = (isset($_POST['searchrec']) && $_POST['searchrec']=="1" ? "none" : "prereg");    // ===== if vrs_view_preg_dashboard has value then SHOW PREREG IN DASHBOARD ONLY

            $viewPreregDashboard = "";

            //die("aaa=". print_r($_POST)."<br><br>bbb=".$viewPreregDashboard."<br><br>ccc=".$_POST['searchrec']);
            //die("aaa=". $viewPreregDashboard."<br>ccc=".$_POST['searchrec']);

            $fields = $this->db->field_data('v_contact_profile');



		// If the form has been submitted, rewrite the URL so that the search
		// terms can be passed as a parameter to the action. Note that there
		// are some issues with certain characters here.
		if ($this->input->post('q') && strlen($this->input->post('q'))>2)
		{
		    $data['chkthis'] = str_replace("&","%26",$this->input->post('q'));
			$data['chkthis'] = str_replace("'","%27",$data['chkthis']);
			$data['chkthis'] = str_replace("(","%28",$data['chkthis']);
			$data['chkthis'] = str_replace(")","%29",$data['chkthis']);
			$data['chkthis'] = str_replace("#","%23",$data['chkthis']);
			$data['chkthis'] = str_replace("$","%24",$data['chkthis']);
			$data['chkthis'] = str_replace("/","%2F",$data['chkthis']);
			$data['chkthis'] = str_replace(",","%E2%80%9A",$data['chkthis']);
	        redirect('/vrs/search/' . $data['chkthis']);
		  //redirect('/vrs/search/' . $this->input->post('q','refresh'));
	      //redirect('/vrs/search/' . urlencode($this->input->post('q')));
		}


		if ($search_terms)
		{
			// Determine the number of results to display per page
			$results_per_page = $this->config->item('results_per_page');

			// Mark the start of search
			//$this->benchmark->mark('search_start');

			// Load the model, perform the search and establish the total
			// number of results
			//$this->load->model('page_model');
			$match  = str_replace("%20"," ",$search_terms); //space
			$match0 = str_replace("%27","'",$match);        //aphostrophy
			$match1 = str_replace("%26","&",$match0);       //ampersand &
			$match12= str_replace("%28","(",$match1);       //
			$match13= str_replace("%28","(",$match12);       //
			$match14= str_replace("%29",")",$match13);       //
			$match15= str_replace("%23","#",$match14);       //
			$match16= str_replace("%24","$",$match15);       //
			$match17= str_replace("%24","$",$match16);       //

			$match2 = str_replace("%E2%80%9A",",",$match17); //comma &

			//$match2 = $search_terms;

			$vtype   = str_replace("%20"," ",$this->uri->segment(4));


			//die("a=".$match2."<br>b=".$start."<br>c=".$results_per_page."<br>d=".$viewPreregDashboard."<br>e=".$data['fcode']."<br>f=".$vtype);

			$results = $this->vrs_read_model->search($match2, $start, $results_per_page, $viewPreregDashboard, $data['fcode'], $vtype);
			$total_results = $this->vrs_read_model->count_search_results($search_terms);

			// Mark the end of search
			//$this->benchmark->mark('search_end');

			// Call a method to setup pagination
			$this->_setup_pagination('/vrs/search/' . $match2 . '/', $total_results, $results_per_page);
			//$this->_setup_pagination('/pages/search/' . $search_terms . '/', $total_results, $results_per_page);

			// Work out which results are being displayed
			$first_result = $start + 1;
			$last_result = min($start + $results_per_page, $total_results);
		}

	 // Render the view, passing it the necessary data
     // $this->load->view('vrs_search_results', array(

		if(!isset($vtype) || $vtype=="" || $vtype=="norecord") { $loadView = "vrs_main"; } else { $loadView = "vrs_view";}

			$this->load->view($loadView, array(
				'search_terms' => $search_terms,
				'first_result' => @$first_result,
				'last_result' => @$last_result,
				'total_results' => @$total_results,
				'results' => @$results,
				'read_set_value' => $data['read_set_value'],
				'sessionRights' => $data['sessionRights'],
				'loginuser' => $data['loginuser'],
			    'myVenue' => $data['myVenue'],
			    'myVenueNum'=> $data['myVenueNum'],
				'diyTerminal' => $data['diyTerminal'],
				'fcode' => $data['fcode'],
				'fairdesc' =>$data['fairdesc'],
				'sector' =>$data['sector'],
				'userAccessRef' => $data['userAccessRef'],
				'userAccessMod' => $data['userAccessMod'],
				'emailTemplate' => $data['emailTemplate'],
				'RegStatus' =>$data['RegStatus'],
				'RegType'=>$data['RegType'],
				'sysName' => $data['sysName'],
				'systitle' => $data['systitle'],
				'viewPreregDashboard' => $viewPreregDashboard,
				'fields' => $fields,
				'controller_CI' => $set_data['sessionData']['controller_CI'],
				'mmenu' => ''
			));


	 }
	 else //(isset($set_data['sessionData'])
	 {
	    redirect('vrs/index');
     }

		// Enable the profiler
		//$this->output->enable_profiler(TRUE);
	 }



	 function searchforpaying($search_terms = '', $start = 0)
	 {
       $set_data = $this->session->all_userdata();

	   if (isset($set_data['sessionData']) && $set_data['sessionData']['realname'] != NULL)
		  {
			$data['read_set_value'] = $set_data['sessionData']['realname'];
			$data['sessionRights'] = $set_data['sessionData']['urights'];
			$data['username'] = $set_data['sessionData']['user'];
			$data['loginuser'] = $set_data['sessionData']['loginuser'];

	      //$data['userCtry'] = $set_data['sessionData']['ucountry'];
			$data['fcode'] = $set_data['sessionData']['fcode'];
			$data['sector'] = $set_data['sessionData']['sector'];
			$data['fairdesc'] = $set_data['sessionData']['fdesc'];
			$data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

			$data['myVenue'] = $set_data['sessionData']['venue'];
			$data['myVenueNum'] = $set_data['sessionData']['venueNum'];

			$data['sysName'] = $set_data['sessionData']['sysName'];
			$data['systitle'] = $set_data['sessionData']['systitle'];
			$data['eventDB'] = $set_data['sessionData']['eventDB'];

			$data['controller_CI'] = $set_data['sessionData']['controller_CI'];

			$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
			$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

			$data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

			$data['RegStatus']=$this->master_model->loadRec("v_reference","where switch='MN6' order by c_profile");
			$data['RegType']=$this->master_model->loadRec("v_reference","where switch='BSTAT' and sector like '%".$data['sector']."%' order by sortfield");
			//$data["exportForm"] = "";

			//$fields = $this->db->field_data('v_contact_profile');
			$viewPreregDashboard = "";

		if ($search_terms)
		{
			// Determine the number of results to display per page
			$results_per_page = $this->config->item('results_per_page');

			$match  = str_replace("%20"," ",$search_terms); //space
			$match0 = str_replace("%27","'",$match);        //aphostrophy
			$match1 = str_replace("%26","&",$match0);       //ampersand &
			$match12= str_replace("%28","(",$match1);       //
			$match13= str_replace("%28","(",$match12);       //
			$match14= str_replace("%29",")",$match13);       //
			$match15= str_replace("%23","#",$match14);       //
			$match16= str_replace("%24","$",$match15);       //

			$match2 = str_replace("%E2%80%9A",",",$match16); //comma &

			$vtype   = str_replace("%20"," ",$this->uri->segment(4));

			//die("aaa=".$vtype);
			//$match2 = $search_terms;
			$results = $this->vrs_read_model->search($match2, $start, $results_per_page, $viewPreregDashboard, $data['fcode'], $vtype);
			//$results = $this->vrs_read_model->search($match2, $start, $results_per_page);
			$total_results = $this->vrs_read_model->count_search_results($search_terms);

			// Mark the end of search
			//$this->benchmark->mark('search_end');

			// Call a method to setup pagination
			$this->_setup_pagination('/vrs/searchforpaying/' . $match2 . '/', $total_results, $results_per_page);
			//$this->_setup_pagination('/pages/search/' . $search_terms . '/', $total_results, $results_per_page);

			// Work out which results are being displayed
			$first_result = $start + 1;
			$last_result = min($start + $results_per_page, $total_results);
		}

	 // Render the view, passing it the necessary data
     // $this->load->view('vrs_search_results', array(
		$this->load->view('vrs_main', array(
			'search_terms' => $search_terms,
			'first_result' => @$first_result,
			'last_result' => @$last_result,
			'total_results' => @$total_results,
			'results' => @$results,
			'read_set_value' => $data['read_set_value'],
			'sessionRights' => $data['sessionRights'],
			'loginuser' => $data['loginuser'],
		    'myVenue' => $data['myVenue'],
		    'myVenueNum'=> $data['myVenueNum'],
			'diyTerminal' => $data['diyTerminal'],
			'fcode' => $data['fcode'],
			'fairdesc' =>$data['fairdesc'],
			'sector' =>$data['sector'],
			'userAccessRef' => $data['userAccessRef'],
			'userAccessMod' => $data['userAccessMod'],
			'emailTemplate' => $data['emailTemplate'],
			'RegStatus' =>$data['RegStatus'],
			'RegType'=>$data['RegType'],
			'sysName' => $data['sysName'],
			'systitle' => $data['systitle'],
			'mmenu' => '',
			'viewPreregDashboard' => $viewPreregDashboard,
			'controller_CI' => $data['controller_CI'],
			'messageX' => '<h4>The visitor is a pre-registered <u>Paying Guest</u>.<br><br> Would you like to continue printing the ID Sticker?</h4><br><br> Thanks... dude'
		));

	 }
	 else //(isset($set_data['sessionData'])
	 {
	    redirect('vrs/index');
     }

		// Enable the profiler
		//$this->output->enable_profiler(TRUE);
	 }

	/**
	 * Setup the pagination library.
	 *
	 * @param string $url The base url to use.
	 * @param string $total_results The total number of results.
	 * @param string $results_per_page The number of results per page.
	 * @return void
	 */
	 function _setup_pagination($url, $total_results, $results_per_page)
	 {
		// Ensure the pagination library is loaded
		//$this->load->library('pagination');

		// This is messy. I'm not sure why the pagination class can't work
		// this out itself...
		$uri_segment = count(explode('/', $url));

		//pagination settings
        $config['base_url'] = site_url($url);
        $config['total_rows'] = $total_results;
        $config['per_page'] = $results_per_page;
        $config["uri_segment"] = 4;

		//config for bootstrap pagination class integration
        $config['full_tag_open'] = '<ul class="pagination">';
        $config['full_tag_close'] = '</ul>';
        $config['first_link'] = false;
        $config['last_link'] = false;
        $config['first_tag_open'] = '<li>';
        $config['first_tag_close'] = '</li>';
        $config['prev_link'] = '&laquo';
        $config['prev_tag_open'] = '<li class="prev">';
        $config['prev_tag_close'] = '</li>';
        $config['next_link'] = '&raquo';
        $config['next_tag_open'] = '<li>';
        $config['next_tag_close'] = '</li>';
        $config['last_tag_open'] = '<li>';
        $config['last_tag_close'] = '</li>';
        $config['cur_tag_open'] = '<li class="active"><a href="#">';
        $config['cur_tag_close'] = '</a></li>';
        $config['num_tag_open'] = '<li>';
        $config['num_tag_close'] = '</li>';

		// Initialise the pagination class, passing in some minimum parameters
		$this->pagination->initialize($config);
//		$this->pagination->initialize(array(
//			'base_url' => site_url($url),
//			'uri_segment' => 4,
//			'total_rows' => $total_results,
//			'per_page' => $results_per_page
//		));
	 }


     function ajax_call() {
        //Checking so that people cannot go to the page directly.
        if (isset($_POST) && isset($_POST['vrisk_types'])) {
            $vcateg = $_POST['vrisk_types'];
			$vsub = $_POST['valsub'];
			$forms = $_POST['vforms'];
            $arrCateg = $this->login_model->get_subcat($vcateg);

			if ($arrCateg<>"")
			{
              foreach ($arrCateg as $subCateg) {
                $arrFinal[$subCateg->item_code] = $subCateg->item_code;
              }
			}
            else
			{$arrFinal=array();}

            //Using the form_dropdown helper function to get the new dropdown.
			$addln = 'class="form-control selectpicker show-tick"';
			if($forms=="sending_receving")
			 {print form_dropdown('description',$arrFinal,$vsub,$addln);}
			else
			 {print form_dropdown('adverse_description',$arrFinal,$vsub,$addln);}
			//echo '<select id="risk_types" name="risk_types" class="form-control selectpicker show-tick">';
			//echo '<option value="small">Small Shirt</option><option value="med">Medium Shirt</option>';
			//echo '</select>';

        } else {
            redirect('home'); //Else redire to the site home page.
        }
    }



     function updateRegistration($rId,$bcode,$vpid,$vmenu) {
		$set_data = $this->session->all_userdata();
		$vfcode = $set_data['sessionData']['fcode'];

		if($this->input->post('visitor_type')=="MEDIA") {$vsector = "22";}
		else {$vsector = $set_data['sessionData']['sector'];}

        $this->vrs_model->updateRecord("v_contact_profile","barcode",$bcode,"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","pid",$vpid,"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","trade_code",$vfcode,"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","sector",$vsector,"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","co_name",$this->input->post('co_name'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","cont_per_fn",$this->input->post('cont_per_fn'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","cont_per_ln",$this->input->post('cont_per_ln'),"rep_code",$rId);
        $this->vrs_model->updateRecord("v_contact_profile","mi",$this->input->post('mi'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","salutation",$this->input->post('salutation'),"rep_code",$rId);
		switch($this->input->post('salutation'))
		 {
		   case "Mr." :
             $this->vrs_model->updateRecord("v_contact_profile","gender","Male","rep_code",$rId);
		   break;
		   default:
             $this->vrs_model->updateRecord("v_contact_profile","gender","Female","rep_code",$rId);
		 }
		$this->vrs_model->updateRecord("v_contact_profile","title",$this->input->post('title'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","add_st",$this->input->post('add_st'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","add_city",$this->input->post('add_city'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","zipcode",$this->input->post('zipcode'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","country",$this->input->post('country'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","continent",$this->master_model->getContinent($this->input->post('country')),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","tel_off",$this->input->post('tel_off'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","fax",$this->input->post('fax'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","mobile",$this->input->post('mobile'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","email",$this->input->post('email'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","webpage",$this->input->post('webpage'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","buyer_type",$this->input->post('buyer_type'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","visitor_type",$this->input->post('visitor_type'),"rep_code",$rId);
		$this->vrs_model->updateRecord("v_contact_profile","buyerclass",$this->input->post('buyerclass'),"rep_code",$rId);

		$this->vrs_model->updateRecTable("v_attendance","barcode",$bcode,$vfcode,$vsector,$rId);
		$this->vrs_model->updateRecTable("v_attendance","visitor_status",$this->input->post('buyer_type'),$vfcode,$vsector,$rId);
		$this->vrs_model->updateRecTable("v_attendance","visitor_type",$this->input->post('visitor_type'),$vfcode,$vsector,$rId);
		$this->vrs_model->updateRecTable("v_attendance","buyerclass",$this->input->post('buyerclass'),$vfcode,$vsector,$rId);
		//$this->vrs_model->updateRecTable("v_attendance","fair_code",$vfcode,$vfcode,$vsector,$rId);
		//$this->vrs_model->updateRecTable("v_attendance","sector",$vsector,$vfcode,$vsector,$rId);

		// $this->vrs_model->updateRecord("v_attendance","barcode",$bcode,"rep_code",$rId);
		// $this->vrs_model->updateRecord("v_attendance","fair_code",$vfcode,"rep_code",$rId);
		// $this->vrs_model->updateRecord("v_attendance","sector",$vsector,"rep_code",$rId);
		// $this->vrs_model->updateRecord("v_attendance","visitor_status",$this->input->post('buyer_type'),"rep_code",$rId);
		// $this->vrs_model->updateRecord("v_attendance","visitor_type",$this->input->post('visitor_type'),"rep_code",$rId);
		// $this->vrs_model->updateRecord("v_attendance","buyerclass",$this->input->post('buyerclass'),"rep_code",$rId);

	 }

   function updateSurvey($vtable,$rcode,$vbarcode,$vfcode,$vsec,$vfield,$vremarks) {

       //die("aaa=".$this->input->post('other_NotAttendingEvent2'));
       //die("aaa=".$this->input->post($vremarks."3"));
       // ===== delete old record ===============
        if($vremarks=="") {$delete1 = $this->master_model->deleteRecTable($vtable,$rcode,$vfcode,$vsec);}
		else {$delete1 = $this->vrs_model->deleteRecTable($vtable,$rcode,$vfcode,$vsec);}
       // =======================================
        $arrlength=count($this->input->post($vfield)); //die("aaa=".$arrlength);
        $rarray = $this->input->post($vfield);
        $rarray1 = $this->input->post($vremarks);
        for($x=0;$x<$arrlength;$x++)
		 {
		  //$remarkValue = element($x,$rarray1);
		  $tempvalue = element($x,$rarray);	   // helper(array('array')) required
		  $itemcode  = explode('#',$tempvalue); //die("aaa=".$tempvalue);
		  //=== IMPT use "!==" instead  "!=" =====================
		  //if (strpos($itemcode[1],"Other") !== false) {$remarks=$this->input->post($vremarks.$x);} //chk Others option contain value
		  //if ($itemcode[2] === "textbox") {$remarks=$this->input->post($vremarks);} //chk Others option contain value
		    if ($itemcode[2] === "textbox") {$remarks=$this->input->post($vremarks.$itemcode[3]);} //chk Others option contain value
		    elseif ($itemcode[2] === "textarea") {$remarks=$this->input->post($vremarks.$itemcode[3]);} //chk Others option contain value
		    else {$remarks=$itemcode[1];}
		     // ===== insert new record ===============
		  if($vremarks=="") {$insert = $this->master_model->insertRecTable($vtable,$rcode,$vbarcode,$itemcode[0],$remarks,$vfcode,$vsec);}
		  else {$insert = $this->vrs_model->insertRecTable($vtable,$rcode,$vbarcode,$itemcode[0],$remarks,$vfcode,$vsec);}
		 }
    }

 ###### FOR BARCODE GENERATION  #####
//USED LIBRARIES /libraries/ZEND FOLDER  & /libraries/ZEND.PHP
  function barcode($bcode,$fcode)
   {
    //$visitor_id=$this->uri->segment(3);
    //$faircode=$this->uri->segment(4);
    $this->load->library('zend');
    $this->zend->load('Zend/Barcode');

      //Zend_Barcode::render('code39', 'image', array('text' => $visitor_id), array());
      $barcode=Zend_Barcode::draw('code39', 'image', array('text' => $bcode,'barHeight'=>30, 'factor'=>2, 'fontSize'=>8,'drawText'=>FALSE), array());

      /*$config = new Zend_Config(array(
      'barcode'        => 'code39',
        'barcodeParams'  => array('text' => $visitor_id, 'barHeight'=>30, 'factor'=>2),
        'renderer'       => 'image',
        'rendererParams' => array('imageType' => 'gif'),
    	));
    	$barcode = Zend_Barcode::factory($config);
      */
    imagegif($barcode, 'assets/images/barcode/barcode'.$fcode.'_'.$bcode.'.gif');
    //echo $barcode."bong".$faircode.'_'.$visitor_id;
    //file_put_contents('assets/images', $barcode);
   } //End of Barcode generation function

  function createcsv($email1,$email2,$barcode)
   {
        //$tempDir = FCPATH."pointwest/inbox/";
   		$tempDir = "D:/jde/Dropbox/POINTWEST_APP/inbox/";
        $csv = "email1,email2 \n";//Column headers
        $csv.=$email1.','.$email2; //row data
        //$filename="forupload\\".$barcode.'.csv';
        $filename = $tempDir.$barcode.'.csv';
        $csv_handler = fopen ($filename,'w');
        fwrite ($csv_handler,$csv);
        fclose ($csv_handler);

    //createcsv ("<EMAIL>","<EMAIL>","2000002");
   }


  function createQR_rcode($rcode,$fcode)
  //$qrcods = $this->createQR($data['fn'],$data['ln'],$data['email'],$data['mobile'],$data['comp'],$data['repcode'],$data['fcode']);
   {
    $this->load->library('infiQr');

    $tempDir = FCPATH."idbadge/";

      // we building raw data
      $codeContents  = $rcode;       // "|J"

      // generating
      //QRcode::png($codeContents, $tempDir.'025.png', QR_ECLEVEL_L, 3);
      QRcode::png($codeContents, $tempDir.$fcode."-".$rcode.'.png', QR_ECLEVEL_L, 3);

      $qvalue = $tempDir.$fcode."-".$rcode.'.png';

      // displaying
      //echo '<img src="'.EXAMPLE_TMP_URLRELPATH.'025.png" />';

      //unset($this->infiqr);

    return $qvalue;
    }

  function createQR($visitorType,$position,$fn,$ln,$email,$mobile,$comp,$ctry,$rcode,$fcode,$XincludeAllProfile,$vrsPrereg)
  //$qrcods = $this->createQR($data['fn'],$data['ln'],$data['email'],$data['mobile'],$data['comp'],$data['repcode'],$data['fcode']);
   {
	  $this->load->library('infiQr');

	  $tempDir = FCPATH."idbadge/";

	    // we building raw data
	    //$codeContents  = 'BEGIN:EESY_PROFILE'."\n";				// "|J"
	    $codeContents  = 'BEGIN:VCARD'."\n";						// "|J"
	    $codeContents .= 'FN:'.$fn." ".$ln."\n";							//	.$ln."\n";
	    $codeContents .= 'N:'.$ln." ".$fn."\n";
	    $codeContents .= 'EMAIL:'.$email."\n";

	    if($XincludeAllProfile=="yes") {
	    	$codeContents .= 'TEL:'.$mobile."\n";
			$codeContents .= 'TITLE:'.$position."\n";
			$codeContents .= 'ADR:'.$ctry."\n";						//COUNTRY
	    	//$codeContents .= 'SALUTATION:'.$salutation."\n";
		}

	    $codeContents .= 'ORG:'.$comp."\n";						// ORG
	    $codeContents .= 'VRS:'.$vrsPrereg."\n";					// ===== indicates VRS created the QRcode ====

	    if(isset($visitorType) ) {
	    	$codeContents .= 'VTYPE:'.$visitorType."\n";
	    }

	    //$codeContents .= 'END:EESY_PROFILE';
	    $codeContents .= 'END:VCARD';

	    // generating
	    //QRcode::png($codeContents, $tempDir.'025.png', QR_ECLEVEL_L, 3);
	    QRcode::png($codeContents, $tempDir.$fcode."-".$rcode.'.png', QR_ECLEVEL_L, 2);

	    $qvalue = $tempDir.$fcode."-".$rcode.'.png';

	    // displaying
	    //echo '<img src="'.EXAMPLE_TMP_URLRELPATH.'025.png" />';

	    //unset($this->infiqr);

	  return $qvalue;

   }

  function printid()								// format -----> vrs/printid/8961/barcodeSearch/TB
   { //die("zzzz");
    //$this->load->library('encrypt2');

    //print_r($this->session->all_userdata()); die();

   	$set_data = $this->session->all_userdata();
	if (!isset($set_data['sessionData'])) {redirect('vrs/index');}

	   $data['read_set_value'] = $set_data['sessionData']['realname'];
	   $data['sessionRights'] = $set_data['sessionData']['urights'];
	   $data['username'] = $set_data['sessionData']['user'];
	   $data['loginuser'] = $set_data['sessionData']['loginuser'];

	   $data['sysName'] = $set_data['sessionData']['sysName'];
	   $data['systitle'] = $set_data['sessionData']['systitle'];
	   $data['eventDB'] = $set_data['sessionData']['eventDB'];

	   $data['fcode'] = $set_data['sessionData']['fcode'];
	   $data['sector'] = $set_data['sessionData']['sector'];
	   $data['fairdesc'] = $set_data['sessionData']['fdesc'];
	   $data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

	   $data['myVenue'] = $set_data['sessionData']['venue'];
	   $data['myVenueNum'] = $set_data['sessionData']['venueNum'];

	   $data['controller_CI'] = $set_data['sessionData']['controller_CI'];

	   $data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector='".$data['sector']."' and exclude=0 order by sortfield");

	   //$data['userIDsize']= $this->master_model->loadRec("v_reference","where switch ='VRSID' and exclude=0 order by sortfield");

	   $data['autoprint']= $this->input->get('autoprint'); // $_GET['autoprint'];

	   //die("xxx=".$data['autoprint']);

	   // ====== default =======================
	   $data['disable_qrCode'] = 'NO';
	   $data['disable_barCode'] = 'NO';
	   $data['disable_gpPay'] = 'NO';
	   $vwidth = "3.3";		//3.5
	   $vheight ="1.3";		//1.4
	   $mwidth = "";
	   $mheight = "";
	   $visitor_idSize2 = array(83.82,33.02); 	//3.3x1.3
	   $tb_idSize2 = array(88.9,35.56);
	   $media_idSize2 = array(88.9,35.56);
	   $data['visitor_format'] = "";
	   $data['tb_format'] = "";
	   $data['gp_format'] = "3.5x2.5";
	   $data['media_format'] = "";			//"3.5x1.4";

	   //=======================================
	   for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {
			if ($set_data['sessionData'][$x."r"] == "disable_qr") { $data['disable_qrCode'] = 'YES'; }
			if ($set_data['sessionData'][$x."r"] == "disable_bc") { $data['disable_barCode'] = 'YES'; }
			if ($set_data['sessionData'][$x."r"] == "disable_gp") { $data['disable_gpPay'] = 'YES'; }

			if ($set_data['sessionData'][$x."r"] == "visitor_3x1")
				{ $vwidth = "3.5"; $vheight ="1.4"; $visitor_idSize2 = array(88.9,35.56); $data['visitor_format'] = "3.5x1.4";}
			if ($set_data['sessionData'][$x."r"] == "visitor_3x2")
				{ $vwidth = "3.5"; $vheight ="2.5"; $visitor_idSize2 = array(88.9,63.5);  $data['visitor_format'] = "3.5x2.5";}
			if ($set_data['sessionData'][$x."r"] == "visitor_3x1.3")
				{ $vwidth = "3.3"; $vheight ="1.3"; $data['visitor_format'] = "3.3x1.3";}

			if ($set_data['sessionData'][$x."r"] == "tb_3x1")
				{ $twidth = "3.5"; $theight ="1.4"; $tb_idSize2 = array(88.9,35.56); $data['tb_format'] = "3.5x1.4";}
			if ($set_data['sessionData'][$x."r"] == "tb_3x2")
				{ $twidth = "3.5"; $theight ="2.5"; $tb_idSize2 = array(88.9,63.5);  $data['tb_format'] = "3.5x2.5";}
			if ($set_data['sessionData'][$x."r"] == "tb_3x1.3")
				{ $twidth = "3.3"; $theight ="1.3"; $data['tb_format'] = "3.3x1.3";}

			if ($set_data['sessionData'][$x."r"] == "media_3x1")
				{ $mwidth = "3.5"; $mheight ="1.4"; $media_idSize2 = array(88.9,35.56); $data['media_format'] = "3.5x1.4";}
			if ($set_data['sessionData'][$x."r"] == "media_3x2")
				{ $mwidth = "3.5"; $mheight ="2.5"; $media_idSize2 = array(88.9,63.5);  $data['media_format'] = "3.5x2.5";}
			if ($set_data['sessionData'][$x."r"] == "media_3x1.3")
				{ $mwidth = "3.3"; $mheight ="1.3"; $data['media_format'] = "3.3x1.3";}

	   }

	//print_r($this->session->all_userdata());
	//die("<br><br>aaa= ".$data['visitor_format']);

   	//this data will be passed on to the view
	//$data['the_content']='mPDF and CodeIgniter are cool!';

	//load the view, pass the variable and do not show it but "save" the output into $html variable
	//$html=$this->load->view('pdf_output', $data, true);


	// $xxxlocation='vrs/printid?repcode='.$repcode.'&mmenu=print&vtype='.$visitorType.'&autoprint='.$autoprint.'&e_fcode='.$fcode.'&e_desc='.$projDesc.'&e_base='.$base.'&e_pg='.$main_page.'&station='.$station;

	// redirect("vrs/printid/".$data["repcode"]."/".$data['mmenu']."/".$data['vtype']);

	$tmprcode = $this->input->get('repcode');
    $data['repcode'] = ($tmprcode<>"" ? $tmprcode : $this->uri->segment(3));

    //================= GET DAY for printing of GEN PUBLIC ====================================================================================================
    $chkday = $this->master_model->getRec("v_reference","where switch='cutof' and sector LIKE '%".$data['sector']."%' and c_code= ? limit 1",date('Y-m-d'),"");
    $data['getDay'] = (isset($chkday[0]["c_profile"]) ? $chkday[0]["c_profile"] : ""); //die("aaa= ".$data['getDay']);
    //=========================================================================================================================================================

    //$data['rcode'] = $this->encrypt2->encode2($data['repcode']); //die($data['rcode']);
    $pid = $this->encryption->encrypt($data['repcode']);
    $data['rcode'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $pid);

    $data['mmenu'] = ($this->input->get('mmenu')<>"" ? $this->input->get('mmenu') : $this->uri->segment(4));
    $data['vtype'] = ($this->input->get('vtype')<>"" ? $this->input->get('vtype') : $this->uri->segment(5));  //die($data['vtype']);

    $data['chkIF_Paying'] = ($this->uri->segment(6)=="" ? "" : $this->uri->segment(6));

    //$data['printGPsticker'] = ($this->uri->segment(7)=="" ? "" : $this->uri->segment(7)); 		// PrintSticker
    //die("xxx = ".$data['printGPsticker']);

    $data['vedit'] = (isset($_GET['vedit']) ? $_GET['vedit'] : "");
    //die("aaa= ".$data['vedit']);

    if($data['vtype']  == "GP" || $data['vtype']  == "GENERAL PUBLIC")
    	{
    		//$peperSize = "(223,180)";
    		$pagesized = array(88.9,63.5);	// in mm  3.5x2.5 in array(88.9,63.5);
    	}
    //if($data['vtype']  == "VISITOR")
    if($data['vtype']  == "GUEST")
    	{
    		if($data['visitor_format']=="3.5x2.5") {
    			$pagesized = array(88.9,63.5);
    		} elseif ($data['visitor_format']=="3.5x1.4") {
    			$pagesized = array(88.9,35.56);	// in mm
    		} else {
    			$pagesized = array(83.82,33.02);		//3.3x1.3
    		}
    	}
    if ($data['vtype']  == "TB" || $data['vtype']  == "TRADE BUYER")
    	{
    		if($data['tb_format']=="3.5x2.5") {
    			$pagesized = array(88.9,63.5);
    		} elseif ($data['tb_format']=="3.5x1.4") {
    			$pagesized = array(88.9,35.56);
    		} else {
    			$pagesized = array(83.82,33.02);		//3.3x1.3 	// in mm
    		}
    	}
    if($data['vtype']  == "MEDIA")
    	{
    		if($data['media_format']=="3.5x2.5") {
    			$pagesized = array(88.9,63.5);
    		} elseif ($data['media_format']=="3.5x1.4") {
    			$pagesized = array(88.9,35.56);
    		} else {
    			$pagesized = array(83.82,33.02);		//3.3x1.3	// in mm
    		}
    	}


    $vReference = $this->master_model->loadRec("v_reference"," where switch='url' and sector LIKE '%".$data['sector']."%'");

    $data['urlQRcode'] = "";
    if($vReference<>"")
    {
    	foreach ($vReference as $rs1)
       	{
       	  $data['urlQRcode'] = $rs1['c_code'];
       	}
    }   //die('aaa='.$data['urlQRcode']);
   // $data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
	$data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where rep_code=".$data['repcode']." limit 1");

	$getCoupon = $this->vrs_read_model->getRec("v_voucher_code","where rep_code = ? limit 1",$data['repcode'],"");
	$data['chkCoupon'] = (isset($getCoupon[0]['item_code']) ? $getCoupon[0]['item_code'] : "");
	//die("xxx=".$chkCoupon);

	if($data['results']!=FALSE){
	  foreach ($data['results'] as $rs1)
       {
	     $data['fn'] = $rs1['cont_per_fn'];
		 $data['ln'] = $rs1['cont_per_ln'];
	     $data['salu'] = $rs1['salutation'];
		 $data['position'] = $rs1['position'];
		 $data['comp'] = strtoupper($rs1['co_name']);
		 $data['ctry'] = $rs1['country'];
		 $data['bcode'] = $rs1['barcode'];
		 //$data['pid'] = $this->encrypt2->encode2($rs1['pid']); //$rs1['pid'];
		 $encVal = $this->encryption->encrypt($rs1['pid']);
         $data['pid'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);

		 $data['email'] = trim($rs1['email']);
		 $data['mobile'] = trim($rs1['mobile']);
		 $data['seatnum'] = trim($rs1['rfid']);   //============ USE RFID field TEMP for SEAT NUM ================
		 $data['prereg'] = $rs1['pre_reg'];
		 $data['bclass'] = trim($rs1['buyerclass']);
	   }

	   //========= create QRCODE ===========================================

	   $qrcods = $this->createQR($data['vtype'],$data['position'],$data['fn'],$data['ln'],$data['email'],$data['mobile'],$data['comp'],$data['ctry'],$data['repcode'],$data['fcode'],"yes","onsite");

	   //die(base_url("idbadge/".$data['fcode']."-".$data['rcode'].".png"));


	   //=== chk if barcodeSearch and if VIB  to send ALERT in vrs_main ===
	   //===================================================================

	   $data['vibalert']="";
	   if($data['vtype']=="TB"  && $data['bclass']<>"") {$data['vibalert']="Y";}    // $data['mmenu']=="barcodeSearch" && $data['prereg']=="P"

	   //==================================================================

	   //$data['ats_QRCODE'] = $this->encrypt2->encode2($data['fn']."^".$data['ln']."^".$data['email']."^".$data['comp']."^".$data['ctry']);
	   $encVal = $this->encryption->encrypt($data['fn']."^".$data['ln']."^".$data['email']."^".$data['comp']."^".$data['ctry']);
       $data['ats_QRCODE'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $encVal);


	   //if($data['vtype']=="TB" || $data['vtype']  == "TRADE BUYER" || $data['vtype']  == "GP" || $data['vtype']  == "GENERAL PUBLIC" || $data['vtype']  == "MEDIA" || $data['vtype']  == "VISITOR")
	   if($data['vtype']=="TB" || $data['vtype']  == "TRADE BUYER" || $data['vtype']  == "GP" || $data['vtype']  == "GENERAL PUBLIC" || $data['vtype']  == "MEDIA" || $data['vtype']  == "GUEST")
	   {

		   $data['printStub'] = "";
		   if(trim($data['autoprint']) == "")
		    {
		    	if($data['vtype'] == "GP")
		    	{
		    		$data['printStub'] = "PAYING-";
		    	}

		    	$dspHTML=$this->load->view('vrs_createid', $data, true);

		    	//die("hmmm");

				//this the the PDF filename that user will get to download
		  		$filename = $data['printStub'].$data['fcode']."-".$data['repcode'];
	      		$pdfFilePath = FCPATH."idbadge/$filename.pdf";

		  		//load mPDF library
		  		//$this->load->library('m_pdf');
		  		//$pdf = $this->m_pdf->load();

		  		//$this->load->library('pdf');
		  		//$pdf = $this->pdf->load();

		  		if($data['vtype'] == "GP" || $data['vtype'] == "GENERAL PUBLIC")
		  		{
		  			$pagesized = array(78.74,90);	// in mm  3.5x2.5 in
		  		}

		  		//print_r($pagesized); die();

		  		//============================= PROBLEM ( Array and string offset access syntax with curly braces is deprecated) ==========
		  		//=== SOLUTION http://hashencrypted.com/how-to-work-mpdf-workable-in-php-7/ ===========

		  		//require_once APPPATH .'\third_party\mpdf\vendor\autoload.php';
		  		require_once APPPATH .'/third_party/mpdf/vendor/autoload.php';

		        $pdf = new \Mpdf\Mpdf();
				//die("xxx");

		        $pdf->addPage("P","","","","","0","0","0","0","","","","","","","","","","","",$pagesized);   //92,33.02
		        //generate the PDF!
		        $pdf->WriteHTML($dspHTML,2);
		        // $pdf->AddPage();
		        // $pdf->WriteHTML($page2,2);

		        //$pdf->WriteHTML($page2,2);
		        //offer it to user via browser download! (The PDF won't be saved on your server HDD)
		        //$pdf->Output($pdfFilePath, "D");
		        $pdf->Output($pdfFilePath, "F");



				if($data['printStub'] == "PAYING-")
				{
					$data['printStub'] = "";

					$dspHTML=$this->load->view('vrs_createid', $data, true);

		  			$filename1 = $data['fcode']."-".$data['repcode'];
	      			$pdfFilePath = FCPATH."idbadge/$filename1.pdf";
		  			//load mPDF library
		  			//$this->load->library('m_pdf');
		  			//actually, you can pass mPDF parameter on this load() function
		  			//$pdf = $this->m_pdf->load();

		  			if($data['vtype'] == "GP" || $data['vtype'] == "GENERAL PUBLIC")
		  			{
		  				$pagesized = array(78.74,90);		//array(88.9,63.5);	  3.14x 4
		  			}

		  			//require_once APPPATH .'\third_party\mpdf\vendor\autoload.php';
		  			require_once APPPATH .'/third_party/mpdf/vendor/autoload.php';
		        	$pdf = new \Mpdf\Mpdf();

		  			//print_r($pagesized); die();

		  			$pdf->addPage("P","","","","","0","0","0","0","","","","","","","","","","","",$pagesized);   //92,33.02
					$pdf->WriteHTML($dspHTML);  //WriteHTML($html);
					$pdf->Output($pdfFilePath, "F");

				}

				//print_r($pagesized); die("<br>".$data['vtype']);
		    }

		} // END ($data['vtype']=="TB" || $data['vtype']  == "TRADE BUYER" || $data['vtype']  == "GP" || $data['vtype']  == "GENERAL PUBLIC" || $data['vtype']  == "MEDIA")

	    //===== create LUX TICKET ==========
	   $data['pdfTicket'] = "";
	   if($data['vtype'] == "TB" && $data['ctry'] <> "Philippines")
	    {
	    	$dspHTML2=$this->load->view('vrs_createluxeticket', $data, true);

	  		//this the the PDF filename that user will get to download
	  		$filename2 = "LUXE-".$data['fcode']."-".$data['repcode']; //die("aaa = ".$filename2);
      		$pdfFilePath2 = FCPATH."idbadge/$filename2.pdf";
	  		//load mPDF library
	  		//$this->load->library('m_pdf');
	  		//actually, you can pass mPDF parameter on this load() function
	  		//$pdf = $this->m_pdf->load();

		    //require_once APPPATH .'\third_party\mpdf\vendor\autoload.php';
		    require_once APPPATH .'/third_party/mpdf/vendor/autoload.php';
		    $pdf = new \Mpdf\Mpdf();

      		$pdf->addPage("P","","","","","1","1","0","1","","","","","","","","","","","",array(79,115));  //92,33.02    79,60 mm
	  		//generate the PDF!
	  		// ====== direct PRINT =======
	  		// $pdf->SetJS('this.print();');
	  		// ============================
	  		$pdf->WriteHTML($dspHTML2);  //WriteHTML($html);
	  		//offer it to user via browser download! (The PDF won't be saved on your server HDD)
	  		//$pdf->Output($pdfFilePath2, "D");
	  		$pdf->Output($pdfFilePath2, "F");

	  		//$data['pdfName'] = $_GET['e_base']."idbadge/".$data['fcode']."-".$data['repcode'].".pdf";
	  		$data['pdfTicket'] = base_url("idbadge/".$filename2.".pdf");
	  	 }
	    //==================================


    }
	else
	{
	 $this->selectedRec();
	} // if repcode invalid go back to menu

    $allowLuxeButt = (strtoupper($data['ctry'])=="PHILIPPINES" ? "disabled='disabled'" : ($data['vtype']=="TB" ? "" : "disabled='disabled'") );

	if($data['autoprint']<>"no")
	 {
    	/*
    	$pdf->addPage("P","","","","","1","1","1","1","","","","","","","","","","","",$pagesized);   //92,33.02
		//generate the PDF!
		// ====== direct PRINT =======
		// $pdf->SetJS('this.print();');
		// ============================
		$pdf->WriteHTML($dspHTML);  //WriteHTML($html);
		//offer it to user via browser download! (The PDF won't be saved on your server HDD)
		//$pdf->Output($pdfFilePath, "D");
		$pdf->Output($pdfFilePath, "F");
		*/
	 }

	//redirect("vrs/printid/".$data["repcode"]."/".$data['fcode']."/".$data['mmenu']."/".$data['vtype']);


	if ($data['mmenu']=='DIYprint')      // ====== value from formsuccess.php ===========
		{
			if($data['autoprint']<>"no") // ====== print label sticker if $data['autoprint']<>"no"
				{
					if($data['vtype']  == "GP")
					{

						if($data['disable_gpPay'] == 'NO')
						{
							$data['labelWidth'] = '3.11';
							$data['labelHeight'] = '4.53';
							$data['vPrinter'] = 'EPSON TM-T88V';
							$prefix = "PAYING-";
						} else {

							$data['labelWidth'] = '3.5';
							$data['labelHeight'] = '2.5';
							$data['vPrinter'] = '';
							$prefix = "PAYING-";
						}

						//$vlocation="http://".$_SERVER['SERVER_ADDR'].":8080/silent_print_pdf_from_browser/silent_print_pdf_stub.jsp?DOC_LIST=".$_GET['e_base']."idbadge/".$data['fcode']."-".$data['repcode'].".pdf&STATUS_UPDATE_ENABLED=false&PRINTER_NAME=EPSONTM-T88V&PRINTER_NAME_SUBSTRING_MATCH=false&PAGE_SCALING=FIT_TO_PRINTABLE_AREA&AUTO_ROTATE_AND_CENTER=true&PAPER=".$peperSize."&e_fcode=".$data['fcode']."&stepMsg=&e_base=".$_GET['e_base']."&e_pg=&e_desc=".$data['fairdesc']."&e_station=".$data['diyTerminal']."&e_bcode=".$data['bcode']."&e_salu=".$data['salu']."&e_fn=".$data['ln']."&myip=".$_SERVER['SERVER_ADDR']."&allowLuxeButt=".$allowLuxeButt;
					}
					elseif ($data['vtype']  == "MEDIA")
					{
							$data['labelWidth'] = $mwidth;		//'3.5';
							$data['labelHeight'] =  $mheight;	//'1.4';
							$data['vPrinter'] = '';
							$prefix = "";
					}
					else
					{
						$data['labelWidth'] = ($data['vtype']=='TB' ? $twidth : $vwidth); //'3.5';
						$data['labelHeight'] = ($data['vtype']=='TB' ? $theight : $vheight); //'1.4';
						$data['vPrinter'] = '';
						$prefix = "";

						//$vlocation="http://".$_SERVER['SERVER_ADDR'].":8080/silent_print_pdf_from_browser/silent_print_pdf.jsp?DOC_LIST=".$_GET['e_base']."idbadge/".$data['fcode']."-".$data['repcode'].".pdf&STATUS_UPDATE_ENABLED=false&PRINTER_NAME=&PRINTER_NAME_SUBSTRING_MATCH=false&PAGE_SCALING=FIT_TO_PRINTABLE_AREA&AUTO_ROTATE_AND_CENTER=true&PAPER=".$peperSize."&e_fcode=".$data['fcode']."&stepMsg=&e_base=".$_GET['e_base']."&e_pg=&e_desc=".$data['fairdesc']."&pid=".$data['pid']."&e_station=".$data['diyTerminal']."&repcode=".$data['repcode']."&e_salu=".$data['salu']."&e_fn=".$data['ln']."&myip=".$_SERVER['SERVER_ADDR']."&allowLuxeButt=".$allowLuxeButt;

					}

					//if($data['fcode']=="TNK2017" || $data['fcode']=="PCC2017" || $data['fcode']=="CPHIL2017" || $data['fcode']=="MFIA2018" || $data['fcode']=="IFEX2017")
					//{

						$data['pdfName'] = $_GET['e_base']."idbadge/".$prefix.$data['fcode']."-".$data['repcode'].".pdf";   //die($data['pdfName']);

						$this->load->view('vrs_welcome',$data);
					//}
					//else
					//{
					//	die("printId func... no fcode");//redirect($vlocation);
					//}
				}
			else
			    {
					$this->load->view('vrs_welcome',$data);
				}
		}
	else  // ====== from DIY ========
		{
			$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
	   		$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

	   		//====== DIY use ========================================
			if($data['vtype']=='TB') {$data['vtype']='TRADE BUYER';}
			if($data['vtype']=='GP') {$data['vtype']='GENERAL PUBLIC';}
			//=======================================================

			//print_r($pagesized); die("<br>".$data['vtype']);

			//die("bbb");
			$this->load->view('vrs_main',$data);
		}

   }


  function printLuxe()
   {

   	$set_data = $this->session->all_userdata();
	if (!isset($set_data['sessionData'])) {redirect('vrs/index');}

	   $data['read_set_value'] = $set_data['sessionData']['realname'];
	   $data['sessionRights'] = $set_data['sessionData']['urights'];
	   $data['username'] = $set_data['sessionData']['user'];
	   $data['loginuser'] = $set_data['sessionData']['loginuser'];

	   $data['sysName'] = $set_data['sessionData']['sysName'];
	   $data['systitle'] = $set_data['sessionData']['systitle'];
	   $data['eventDB'] = $set_data['sessionData']['eventDB'];

	   $data['fcode'] = $set_data['sessionData']['fcode'];
	   $data['sector'] = $set_data['sessionData']['sector'];
	   $data['fairdesc'] = $set_data['sessionData']['fdesc'];
	   $data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

	   $data['myVenue'] = $set_data['sessionData']['venue'];
	   $data['myVenueNum'] = $set_data['sessionData']['venueNum'];


	   $autoprint= $this->input->get('autoprint'); // $_GET['autoprint'];

       //redirect("vrs/printLuxe/".$data["repcode"]."/".$data['fname']."/".$data['lname']."/".$data['coname']."/".$data['country']."/".$data['diyTerminal']."/".$data['barcode']."/".$data['mmenu']."/".$data['fairdesc']);

	   //$tmprcode = $this->input->get('repcode');
       $data['repcode'] = $this->uri->segment(3);
       $data['mmenu'] = $this->uri->segment(4);


   // $data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
      $result = $this->vrs_read_model->loadRec("v_contact_profile","where rep_code=".$data['repcode']);
      if ($result<>"")
      {
      	foreach ($result as $r1)
      	{
      		$data['salutation'] = $r1['salutation'];
 	   		$data['fname'] = $r1['cont_per_fn'];
       		$data['lname'] = $r1['cont_per_ln'];
       		$data['coname'] = $r1['co_name'];
       		$data['country'] = $r1['country'];
       		$data['barcode'] = $r1['barcode'];
       		$data['bcode'] = $r1['barcode'];    	// use in $mmenu=='barcodeTicketSearch'
       		$data['pid'] = $r1['pid'];    			// use in $mmenu=='barcodeTicketSearch'
       		$data['vtype'] = $r1['visitor_type']; 	// use in $mmenu=='barcodeTicketSearch'
        }

	  $dspHTML=$this->load->view('vrs_createluxeticket', $data, true);
	  //this the the PDF filename that user will get to download
	  $filename = "LUXE-".$data['fcode']."-".$data['repcode']; //die("aaa = ".$filename);
      $pdfFilePath = FCPATH."idbadge/$filename.pdf";
	  //load mPDF library
	  //$this->load->library('m_pdf');
	  //actually, you can pass mPDF parameter on this load() function
	  //$pdf = $this->m_pdf->load();

	  require_once APPPATH .'\third_party\mpdf\vendor\autoload.php';
	  $pdf = new \Mpdf\Mpdf();

      //$pdf->addPage("P","","","","","1","1","0","1","","","","","","","","","","","",array(74,60));  //92,33.02    74,40 mm
      $pdf->addPage("P","","","","","1","1","0","1","","","","","","","","","","","",array(79,115));  //92,33.02    79,60 mm

	  //generate the PDF!
	  // ====== direct PRINT =======
	  // $pdf->SetJS('this.print();');
	  // ============================
	  $pdf->WriteHTML($dspHTML);  //WriteHTML($html);
	  //offer it to user via browser download! (The PDF won't be saved on your server HDD)
	  //$pdf->Output($pdfFilePath, "D");
	  $pdf->Output($pdfFilePath, "F");

      }

      if($data['mmenu']=='fromSilentPrintPdf')
       {
		//$ebase= base_url();
     	//$peperSize = "(250,185)";
	 	//redirect("http://".$_SERVER['SERVER_ADDR'].":8080/silent_print_pdf_from_browser/silent_print_pdf_ticket.jsp?DOC_LIST=".$ebase."idbadge/LUXE-".$data['fcode']."-".$data['repcode'].".pdf&STATUS_UPDATE_ENABLED=false&PRINTER_NAME=EPSONTM-T88V&PRINTER_NAME_SUBSTRING_MATCH=false&PAGE_SCALING=FIT_TO_PRINTABLE_AREA&AUTO_ROTATE_AND_CENTER=true&PAPER=".$peperSize."&e_fcode=".$data['fcode']."&stepMsg=&e_salu=".$data['salutation']."&e_fn=".$data['lname']."&e_base=".$ebase."&e_pg=&e_desc=".$data['fairdesc']."&e_station=".$data['diyTerminal']."&e_rcode=".$data['repcode']);

	    //die($printme);
       }
      else
       { //die("aaa");
   		$ebase= base_url();
   		$data['pdfName'] = $ebase."idbadge/".$filename.".pdf";  //die($data['pdfName']);

		$data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
	   	$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");
		$this->load->view('vrs_main',$data);
	   }

   }


  public function messageReferer($fairdesc,$link,$vfrom,$bannerpic,$othinfo,$xsector,$refCode) {

      switch($xsector)
      {
      	case "01":
          $fbook = "ifexphilippines";
          $tweet = "IFEXPhilippines";
          $insta = "ifexphilippines";
          break;
        case "02":

          $fbook = "ManilaFAMEofficial";
          $tweet = "TheManilaFAME";
          $insta = "manilafame";
          break;
        case "20":
          $fbook = "createphilippines";
          $tweet = "createphils";
          $insta = "createphilippines";
          break;
        case "25":
          $fbook = "SSXPhilippines";
          $tweet = "SSXPhilippines";
          $insta = "ssx.philippines";
          break;
        default:
          $fbook = "";
          $tweet = "";
          $insta = "";
      }

      $info = explode("~",$othinfo);

      $showRef = ($refCode=="" ? "" : "<br><b>Ref # ".$refCode."</b><br>");

		      $mess = "<!DOCTYPE html><html lang='en'>
				<head>
		  		<title>".$fairdesc."</title>
				  <meta charset='utf-8'>
				  <meta name='viewport' content='width=device-width, initial-scale=1'>
				  <link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css'>
				  <script src='https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js'></script>
				  <script src='https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js'></script>
				</head>


		           <style>
		            .page-wrap {
		              width: 80%;
		              margin: 0 auto;
		            }
		            .autosize{				/* responsive */
				        max-width: 100%;
				        max-height: 100%;
				        display: block; /* remove extra space below image */
				    }

		            td {
		             height: 101px;
		             vertical-align: middle;
		            }
		            .round1 {
		              border: 1px solid gray;
		              border-radius: 7px;
		              width: 80%;
		              padding: 10px;
		            }
		            div {
		              margin: 0px 50px 0px 50px;
		            }
		            a:link
		            {
		              color:#000000
		            }

		            .center {
		                display: block;
		                margin-left: auto;
		                margin-right: auto;
		                width: 50%;
		            }

		           </style>

		           </head><body class='page-wrap'>

		           <div class='round1'>
		           <table cellpadding='0' cellspacing='0' border='0' width='100%'>"

		           .$link.

		           "</table>
		           <div align='right' style='vertical-align: top; margin: 0px 0px 0px 0px; height:20px; font-size:9px; font-family:Verdana, Arial, Helvetica, sans-serif;'>This email was send by ".$fairdesc." thru CITEM's Visitor Registration System.".$showRef."</div><br>

		          </div>

		          </body></html>";

      return $mess;
 }

 public function messageActivity($fairdesc,$link,$vfrom,$bannerpic,$othinfo,$vproject) {

  	  $info = explode("~",$othinfo);

      $mess = "<html><head><title>".$vproject."</title>
           <style>

           	.butn {
			  background: #f09218;
			  background-image: -webkit-linear-gradient(top, #f09218, #b85b2c);
			  background-image: -moz-linear-gradient(top, #f09218, #b85b2c);
			  background-image: -ms-linear-gradient(top, #f09218, #b85b2c);
			  background-image: -o-linear-gradient(top, #f09218, #b85b2c);
			  background-image: linear-gradient(to bottom, #f09218, #b85b2c);
			  -webkit-border-radius: 8;
			  -moz-border-radius: 8;
			  border-radius: 8px;
			  -webkit-box-shadow: 0px 1px 3px #666666;
			  -moz-box-shadow: 0px 1px 3px #666666;
			  box-shadow: 0px 1px 3px #666666;
			  font-family: Arial;
			  color: #ffffff;
			  font-size: 20px;
			  padding: 10px 20px 10px 20px;
			  text-decoration: none;
			}

			.butn:hover {
			  background: #fcb93c;
			  background-image: -webkit-linear-gradient(top, #fcb93c, #d9a534);
			  background-image: -moz-linear-gradient(top, #fcb93c, #d9a534);
			  background-image: -ms-linear-gradient(top, #fcb93c, #d9a534);
			  background-image: -o-linear-gradient(top, #fcb93c, #d9a534);
			  background-image: linear-gradient(to bottom, #fcb93c, #d9a534);
			  text-decoration: none;
			}

            .page-wrap {
     		width: 800px;
     		margin: 0 auto;
			}
            td {
             height: 101px;
             vertical-align: middle;
            }
            .round1 {
    		 border: 1px solid gray;
    		 border-radius: 10px;
    		 width: 700px;
    		 padding: 10px;
    	 	}
    	 	div {
    	 	 margin: 0px 50px 0px 50px;
    	 	}

    	 	/* set image max width to 100% */
			img {
			 max-width: 100%;
			 height: auto;
			 width: auto\9; /* ie8 */
			}

			a:link
			{
			 color:#000000
			}

           </style>
      	   </head><body class='page-wrap'>

      	   <div class='round1xx'>

           <table class='round1' cellpadding='0' cellspacing='0' border='0' width='700'>
           <tr>
              <td align='right' valign='top' ><img src='".$bannerpic."' alt='".$fairdesc."'></td>
           </tr>
           <tr>
            <td valign='top'><br><font size='2' face='Verdana, Arial, Helvetica, sans-serif'><div><br>".$link."<br><br></div></td>
		   </tr>

		   <tr>
            <td bgcolor='#ebecee' style='background-color:#ebecee; height:20px;'>
              <table border='0' cellpadding='0' cellspacing='0' align='left' style='width:50%; height:20px;'>
                <tr>
                  <td align='left' bgcolor='#ebecee' style='background-color:#ebecee; height:20px;'>
                    <a href='http://www.dti.gov.ph/' target='_blank'><img src='http://www.citem.com.ph/citemprojects/pix/foremail/dti.jpg' width='78' height='96' alt='DTI' border='0'></a>
                    <a href='http://www.citem.gov.ph/' target='_blank'><img src='http://www.citem.com.ph/citemprojects/pix/foremail/citem.jpg' width='70' height='96' alt='CITEM' border='0'></a>
                  </td>
                </tr>
              </table>
              <table border='0' cellpadding='0' cellspacing='3' align='right' style='width:50%;'>
                <tr>
                  <td align='right' bgcolor='#ebecee' style='background-color:#ebecee;'>
                    <a href='https://www.facebook.com/ManilaFAMEofficial' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/facebook_new.png' border='0' alt='Facebook' /></a>
                    <a href='https://twitter.com/TheManilaFAME' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/twitter_new.png' border='0' alt='Twitter' /></a>
                    <a href='https://www.instagram.com/manilafame/' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/instagram_new.png' border='0' alt='Instagram' /></a>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
             <td style='height:20px; font-size:9px; font-family:Verdana, Arial, Helvetica, sans-serif;'>This email was send by ".$info[1]." thru ".$info[0]."'s Visitor Registration System.</td>
           </tr>
          </table>


          </div>
          </body></html>";

 	 	return $mess;
 }


  function synctocloud()
   {

   	$set_data = $this->session->all_userdata();
	if(!isset($set_data['sessionData'])) {redirect('vrs/index');}

	   $data['read_set_value'] = $set_data['sessionData']['realname'];
	   $data['sessionRights'] = $set_data['sessionData']['urights'];
	   $data['username'] = $set_data['sessionData']['user'];
	   $data['loginuser'] = $set_data['sessionData']['loginuser'];

	   $data['sysName'] = $set_data['sessionData']['sysName'];
	   $data['systitle'] = $set_data['sessionData']['systitle'];
	   $data['eventDB'] = $set_data['sessionData']['eventDB'];

	   $data['fcode'] = $set_data['sessionData']['fcode'];
	   $data['sector'] = $set_data['sessionData']['sector'];
	   $data['fairdesc'] = $set_data['sessionData']['fdesc'];
	   $data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

	   $data['myVenue'] = $set_data['sessionData']['venue'];
	   $data['myVenueNum'] = $set_data['sessionData']['venueNum'];

	   $data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
	   $data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

		$data['tmp'] = $this->input->post('submit');
		$data['sync'] = (isset($data['tmp']) && $data['tmp']<>"" ? $data['tmp'] : $this->input->get('submit'));
		$data['baseUrl'] = base_url();
		$data['results'] = "";
		$data['numqry']= 0;
		//$data['syncStart'] = 'nope';
		$data['ctr'] = $this->input->get('ctr') + 1;


		if ($data['sync'] == "start_sync")
		{

			//======= check records to be uploaded  ===============
			$data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where marked='".$data['myVenueNum']."'");   //and reg_status='T'
			$data['numqry']= count($data['results']);

			if($data['results']<>"")
	 		{

				if ($data['sync'] == "start_sync")     // $data['sync']=="start_sync"
	 			{
	  				//$this->uploadrec($data);  // upload data to cloud  ================== DONE BY SCHEDULER
     			}

 			}

 		}

	  $this->load->view('vrs_sync_start',$data);

   }


//function uploadrec($data)
  function uploadrec()
   {
   	//$this->load->model('live_model');

   	//=== load main menu ========================
	  $data['userBar'] = "class='active'";
	//===========================================

	// ******* MUST SECURE THIS MODULE ***************
	//************************************************

	  	//========= from "synctocloud.bat" VALUE is the venue number ================ syntax is \htdocs\ci3\index.php vrs uploadrec/{hostname}/{eventDB}/{upload}/{download}

	  	$sessionArray = array('hname' => $this->uri->segment(3),
	  						  'eventDB' => $this->uri->segment(4),
	  						  'un' => $this->uri->segment(5),
	  						  'pw' => $this->uri->segment(6)
                             );
        $this->session->set_userdata('sessionData',$sessionArray);

        $upload   = $this->uri->segment(7);
	  	$download = $this->uri->segment(8);

	  	$this->load->model('live_model');

	  	// echo "aaa= ".$upload;
	  	// echo "bbb= ".$download; die();

	  	//========= from "synctocloud.bat" VALUE is the venue number ================
	  	//die($venue);

	  	//========== get as of DATE for dashboard ===================================================
		$RSasofDate = $this->master_model->loadRec("v_reference","where switch ='cutof' limit 1");
		$xdate = "";
		if($RSasofDate<>"")
			{
				foreach ($RSasofDate as $rs1) { $xdate = trim($rs1['p_switch']); }
			}

		$asofDate = $xdate;
		$sqlDT ="update ".MASTER_DB.".v_reference set p_switch = (CASE WHEN p_switch <> '".$asofDate."' or p_switch is NULL THEN '".$asofDate."' ELSE p_switch END) where switch ='cutof'";
		//$sqlDT ="update citem_masterfile.v_reference set p_switch = '".$asofDate."' where switch ='cutof'";
		$query = $this->live_model->execQry($sqlDT);
	 	//===========================================================================================


        $data['results'] = $this->vrs_read_model->loadRec("v_contact_profile","where marked='".$upload."'");   //and reg_status='T'

		// === get fieldnames to display ==================================================================
		$fields = $this->db->field_data('v_contact_profile');
		//======================================================================================================


			  if($data['results']<>"")   //from from synctocloud()
			  {
			  	$data['numqry']= count($data['results']);

			    foreach ($data['results'] as $r1)
			     {
            		foreach ($fields as $field) //echo $field->name; echo $field->type; echo $field->max_length; echo $field->primary_key;
            			{
			        		$data[$field->name] = $r1[$field->name];
			        		//echo  $data[$field->name]."<br>";
			        	}

			        //========= update live db =======================
			        	//print_r($results);

			        	$sql = "INSERT INTO v_contact_profile (rep_code,barcode,rfid,co_name,marked,reg_status,date_apply,date_input,description,cont_per_ln,cont_per_fn,mi,position,salutation,
			        						gender,age_group,birthdate,nationality,accompanying_person,country,continent,add_st,add_city,zipcode,tel_off,fax,email,email2,webpage,facebook,
			        						linkedin,blogsite,twitter,instagram,pinterest,weibo,attend_mfi,pre_reg,venue,deleted,mobile,visitor_type,buyer_type,buyerclass,trade_code,pid,cutoff,
			        						remarks,sector,tag,added)
								VALUES (".
								$data['rep_code'].",".
								$data['barcode'].",".
								$this->db->escape($data['rfid']).",".
								$this->db->escape($data['co_name']).",'".$upload."','".
								$data['reg_status']."','".
								$data['date_apply']. "','".
						        $data['date_input']."',".
						        $this->db->escape($data['description']).",".
						        $this->db->escape($data['cont_per_ln']).",".
						        $this->db->escape($data['cont_per_fn']).",".
						        $this->db->escape($data['mi']).",".
						        $this->db->escape($data['position']).",'".
						        $data['salutation']."','".
						        $data['gender']."','".
						        $data['age_group']."','".
						        $data['birthdate']."',".
						        $this->db->escape($data['nationality']).",".
						        $this->db->escape($data['accompanying_person']).",'".
						        $data['country']."','".
						        $data['continent']."',".
						        $this->db->escape($data['add_st']).",".
						        $this->db->escape($data['add_city']).",".
						        $this->db->escape($data['zipcode']).",".
						        $this->db->escape($data['tel_off']).",".
						        $this->db->escape($data['fax']).",".
						        $this->db->escape($data['email']).",".
						        $this->db->escape($data['email2']).",".
						        $this->db->escape($data['webpage']).",".
						        $this->db->escape($data['facebook']).",".
						        $this->db->escape($data['linkedin']).",".
						        $this->db->escape($data['blogsite']).",".
						        $this->db->escape($data['twitter']).",".
						        $this->db->escape($data['instagram']).",".
						        $this->db->escape($data['pinterest']).",".
						        $this->db->escape($data['weibo']).",".
						        $this->db->escape($data['attend_mfi']).",'".
						        $data['pre_reg']."','".
						        $data['venue']."','".
						        $data['deleted']."',".
						        $this->db->escape($data['mobile']).",'".
						        $data['visitor_type']."','".
						        $data['buyer_type']."','".
						        $data['buyerclass']."','".
						        $data['trade_code']."','".
						        $data['pid']."','".
						        $data['cutoff']."',".
						        $this->db->escape($data['remarks']).",'".
						        $data['sector']."',".
						        $data['tag'].",".
						        $data['added'].
						        ")
						ON DUPLICATE KEY UPDATE
						  barcode = CASE WHEN date_input<'".$data['date_input']."' THEN ".$data['barcode']." ELSE barcode END,
						  rfid = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['rfid'])." ELSE rfid END,
						  co_name = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['co_name'])." ELSE co_name END,
						  reg_status = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['reg_status']."' ELSE reg_status END,
						  marked = CASE WHEN date_input<'".$data['date_input']."' THEN '".$upload."' ELSE marked END,
						  description = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['description'])." ELSE description END,
						  cont_per_ln = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['cont_per_ln'])." ELSE cont_per_ln END,
						  cont_per_fn = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['cont_per_fn'])." ELSE cont_per_fn END,
						  mi = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['mi'])." ELSE mi END,
						  position = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['position'])." ELSE position END,
						  salutation = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['salutation']."' ELSE salutation END,
						  gender = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['gender']."' ELSE gender END,
						  age_group = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['age_group']."' ELSE age_group END,
						  birthdate = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['birthdate']."' ELSE birthdate END,
						  nationality = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['nationality'])." ELSE nationality END,
						  accompanying_person = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['accompanying_person'])." ELSE accompanying_person END,
						  country = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['country']."' ELSE country END,
						  continent = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['continent']."' ELSE continent END,
						  add_st = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['add_st'])." ELSE add_st END,
						  add_city = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['add_city'])." ELSE add_city END,
						  zipcode = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['zipcode'])." ELSE zipcode END,
						  tel_off = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['tel_off'])." ELSE tel_off END,
						  fax = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['fax'])." ELSE fax END,
						  email = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['email'])." ELSE email END,
						  email2 = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['email2'])." ELSE email2 END,
						  webpage = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['webpage'])." ELSE webpage END,
						  facebook = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['facebook'])." ELSE facebook END,
						  linkedin = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['linkedin'])." ELSE linkedin END,
						  blogsite = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['blogsite'])." ELSE blogsite END,
						  twitter = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['twitter'])." ELSE twitter END,
						  instagram = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['instagram'])." ELSE instagram END,
						  pinterest = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['pinterest'])." ELSE pinterest END,
						  weibo = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['weibo'])." ELSE weibo END,
						  attend_mfi = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['attend_mfi'])." ELSE attend_mfi END,
						  pre_reg = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['pre_reg']."' ELSE pre_reg END,
						  venue = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['venue']."' ELSE venue END,
						  deleted = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['deleted']."' ELSE deleted END,
						  mobile = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['mobile'])." ELSE mobile END,
						  visitor_type = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['visitor_type']."' ELSE visitor_type END,
						  buyer_type = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['buyer_type']."' ELSE buyer_type END,
						  buyerclass = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['buyerclass']."' ELSE buyerclass END,
						  trade_code = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['trade_code']."' ELSE trade_code END,
						  pid = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['pid']."' ELSE pid END,
						  cutoff = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['cutoff']."' ELSE cutoff END,
						  remarks = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['remarks'])." ELSE remarks END,
						  sector = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['sector']."' ELSE sector END,
						  tag = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['tag']."' ELSE tag END,
						  added = CASE WHEN date_input<'".$data['date_input']."' THEN ".$data['added']." ELSE added END,
						  date_apply = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['date_apply']."' ELSE date_apply END,
						  date_input = '".$data['date_input']."'
						  ";

					  //die($sql);

						$query = $this->live_model->execQry($sql);   //die("zzz= ".$query);

						if($query > 0)
						 {
						   $updateProf = $this->vrs_model->updateAllRecord("v_contact_profile","marked","0","rep_code=".$data['rep_code']);
						 }




			     	//================================================
			     }
			     echo "sucess - ".$data['numqry']." queries processed po";
		  	  }

		  	  else
		  	  {  echo "No queries processed po";} //$data['numqry'] = "none";}


//====================================================================
//================== GET DATA FROM CLOUD =============================
//====================================================================

	if ($download<>"")

	  {
        $data['results'] = $this->live_model->loadRec("v_contact_profile","where marked='".$download."'");   // marked='".$download."'"

        //die("v_contact_profile where marked='2' and venue='".$venue."'");

		// === get fieldnames to display ==================================================================
		$fields = $this->db->field_data('v_contact_profile');
		//======================================================================================================


			  if($data['results']<>"")   //from from synctocloud()
			  {
			  	$data['numqry']= count($data['results']);

			    foreach ($data['results'] as $r1)
			     {
            		foreach ($fields as $field) //echo $field->name; echo $field->type; echo $field->max_length; echo $field->primary_key;
            			{
			        		$data[$field->name] = $r1[$field->name];
			        		//echo  $data[$field->name]."<br>";
			        	}

			        //========= update live db =======================
			        	//print_r($results);

			        	$sql = "INSERT INTO v_contact_profile (rep_code,barcode,rfid,co_name,marked,reg_status,date_apply,date_input,description,cont_per_ln,cont_per_fn,mi,position,salutation,
			        						gender,age_group,birthdate,nationality,accompanying_person,country,continent,add_st,add_city,zipcode,tel_off,fax,email,email2,webpage,facebook,
			        						linkedin,blogsite,twitter,instagram,pinterest,weibo,attend_mfi,pre_reg,venue,deleted,mobile,visitor_type,buyer_type,buyerclass,trade_code,pid,cutoff,
			        						remarks,sector,tag,added)
								VALUES (".
								$data['rep_code'].",".
								$data['barcode'].",".
								$this->db->escape($data['rfid']).",".
								$this->db->escape($data['co_name']).",'0','".
								$data['reg_status']."','".
								$data['date_apply']. "','".
						        $data['date_input']."',".
						        $this->db->escape($data['description']).",".
						        $this->db->escape($data['cont_per_ln']).",".
						        $this->db->escape($data['cont_per_fn']).",".
						        $this->db->escape($data['mi']).",".
						        $this->db->escape($data['position']).",'".
						        $data['salutation']."','".
						        $data['gender']."','".
						        $data['age_group']."','".
						        $data['birthdate']."',".
						        $this->db->escape($data['nationality']).",".
						        $this->db->escape($data['accompanying_person']).",'".
						        $data['country']."','".
						        $data['continent']."',".
						        $this->db->escape($data['add_st']).",".
						        $this->db->escape($data['add_city']).",".
						        $this->db->escape($data['zipcode']).",".
						        $this->db->escape($data['tel_off']).",".
						        $this->db->escape($data['fax']).",".
						        $this->db->escape($data['email']).",".
						        $this->db->escape($data['email2']).",".
						        $this->db->escape($data['webpage']).",".
						        $this->db->escape($data['facebook']).",".
						        $this->db->escape($data['linkedin']).",".
						        $this->db->escape($data['blogsite']).",".
						        $this->db->escape($data['twitter']).",".
						        $this->db->escape($data['instagram']).",".
						        $this->db->escape($data['pinterest']).",".
						        $this->db->escape($data['weibo']).",".
						        $this->db->escape($data['attend_mfi']).",'".
						        $data['pre_reg']."','".
						        $data['venue']."','".
						        $data['deleted']."',".
						        $this->db->escape($data['mobile']).",'".
						        $data['visitor_type']."','".
						        $data['buyer_type']."','".
						        $data['buyerclass']."','".
						        $data['trade_code']."','".
						        $data['pid']."','".
						        $data['cutoff']."',".
						        $this->db->escape($data['remarks']).",'".
						        $data['sector']."',".
						        $data['tag'].",".
						        $data['added'].
						        ")
						ON DUPLICATE KEY UPDATE
						  barcode = CASE WHEN date_input<'".$data['date_input']."' THEN ".$data['barcode']." ELSE barcode END,
						  rfid = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['rfid'])." ELSE rfid END,
						  co_name = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['co_name'])." ELSE co_name END,
						  reg_status = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['reg_status']."' ELSE reg_status END,
						  marked = CASE WHEN date_input<'".$data['date_input']."' THEN '0' ELSE marked END,
						  description = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['description'])." ELSE description END,
						  cont_per_ln = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['cont_per_ln'])." ELSE cont_per_ln END,
						  cont_per_fn = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['cont_per_fn'])." ELSE cont_per_fn END,
						  mi = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['mi'])." ELSE mi END,
						  position = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['position'])." ELSE position END,
						  salutation = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['salutation']."' ELSE salutation END,
						  gender = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['gender']."' ELSE gender END,
						  age_group = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['age_group']."' ELSE age_group END,
						  birthdate = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['birthdate']."' ELSE birthdate END,
						  nationality = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['nationality'])." ELSE nationality END,
						  accompanying_person = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['accompanying_person'])." ELSE accompanying_person END,
						  country = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['country']."' ELSE country END,
						  continent = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['continent']."' ELSE continent END,
						  add_st = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['add_st'])." ELSE add_st END,
						  add_city = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['add_city'])." ELSE add_city END,
						  zipcode = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['zipcode'])." ELSE zipcode END,
						  tel_off = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['tel_off'])." ELSE tel_off END,
						  fax = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['fax'])." ELSE fax END,
						  email = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['email'])." ELSE email END,
						  email2 = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['email2'])." ELSE email2 END,
						  webpage = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['webpage'])." ELSE webpage END,
						  facebook = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['facebook'])." ELSE facebook END,
						  linkedin = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['linkedin'])." ELSE linkedin END,
						  blogsite = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['blogsite'])." ELSE blogsite END,
						  twitter = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['twitter'])." ELSE twitter END,
						  instagram = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['instagram'])." ELSE instagram END,
						  pinterest = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['pinterest'])." ELSE pinterest END,
						  weibo = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['weibo'])." ELSE weibo END,
						  attend_mfi = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['attend_mfi'])." ELSE attend_mfi END,
						  pre_reg = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['pre_reg']."' ELSE pre_reg END,
						  venue = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['venue']."' ELSE venue END,
						  deleted = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['deleted']."' ELSE deleted END,
						  mobile = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['mobile'])." ELSE mobile END,
						  visitor_type = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['visitor_type']."' ELSE visitor_type END,
						  buyer_type = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['buyer_type']."' ELSE buyer_type END,
						  buyerclass = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['buyerclass']."' ELSE buyerclass END,
						  trade_code = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['trade_code']."' ELSE trade_code END,
						  pid = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['pid']."' ELSE pid END,
						  cutoff = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['cutoff']."' ELSE cutoff END,
						  remarks = CASE WHEN date_input<'".$data['date_input']."' THEN ".$this->db->escape($data['remarks'])." ELSE remarks END,
						  sector = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['sector']."' ELSE sector END,
						  tag = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['tag']."' ELSE tag END,
						  added = CASE WHEN date_input<'".$data['date_input']."' THEN ".$data['added']." ELSE added END,
						  date_apply = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['date_apply']."' ELSE date_apply END,
						  date_input = '".$data['date_input']."'
						  ";  //  date_input = CASE WHEN date_input<'".$data['date_input']."' THEN '".$data['date_input']."' ELSE date_input END

					  //die($sql);

						 $query = $this->vrs_read_model->execQry($sql);   //die("zzz= ".$query);

						if($query > 0)
						 {
						   $updateProf = $this->live_model->updateAllRecord("v_contact_profile","marked","0","rep_code=".$data['rep_code']);
						 }



			     	//================================================
			     }
			     echo "sucess - ".$data['numqry']." queries processed po";
		  	  }

		  	  else
		  	  {  echo "No queries processed po";} //$data['numqry'] = "none";}

	  } // end if ($download<>"")

//====================================================================

   }


function array_mesh() {
	// Combine multiple associative arrays and sum the values for any common keys
	// The function can accept any number of arrays as arguments
	// The values must be numeric or the summed value will be 0

	// Get the number of arguments being passed
	$numargs = func_num_args();

	// Save the arguments to an array
	$arg_list = func_get_args();

	// Create an array to hold the combined data
	$out = array();

	// Loop through each of the arguments
	for ($i = 0; $i < $numargs; $i++) {
		$in = $arg_list[$i]; // This will be equal to each array passed as an argument

		// Loop through each of the arrays passed as arguments
		foreach($in as $key => $value) {
			// If the same key exists in the $out array
			if(array_key_exists($key, $out)) {
				// Sum the values of the common key
				$sum = $in[$key] + $out[$key];
				// Add the key => value pair to array $out
				$out[$key] = $sum;
			}else{
				// Add to $out any key => value pairs in the $in array that did not have a match in $out
				$out[$key] = $in[$key];
			}
		}
	}

	return $out;
}


}

?>

<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
class data extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        //$this->load->database();
    }

    public function getReferenceTable() {
        $set_data = $this->session->all_userdata();
        $data = sessionRights($set_data);

        $rtable = MASTER_DB.'.v_reference';

        $aColumns = array('id','c_profile','c_code','switch','exclude','sector','sortfield');

        $searchHeader = $this->input->get_post('searchHeader', true);
        $iDisplayStart = $this->input->get_post('start', true);
        $iDisplayLength = $this->input->get_post('length', true);
        $iSortCol_0 = $this->input->get_post('order[0][column]', true);
        $iSortingCols = $this->input->get_post('order[0][dir]', true);
        $sSearch = $this->input->get_post('search[value]', true);
        $sEcho = $this->input->get_post('draw', true);

        // echo $iDisplayLength;die('test');
        // Paging
        if(isset($iDisplayStart) && $iDisplayLength != '-1')
        {
            $this->db->limit($this->db->escape_str($iDisplayLength), $this->db->escape_str($iDisplayStart));
        }
        // Ordering
        if(isset($iSortCol_0))
        {
            for($i=0; $i<intval($iSortingCols); $i++)
            {
                $iSortCol = $this->input->post('iSortCol_'.$i, true);
                // $bSortable = $this->input->post('bSortable_'.intval($iSortCol), true);
                $sSortDir = $this->input->post('sSortDir_'.$i, true);

                // if($bSortable == 'true')
                // {
                    $this->db->order_by($aColumns[intval($this->db->escape_str($iSortCol))], $this->db->escape_str($sSortDir));
                // }
            }
        }

        if(isset($sSearch) && !empty($sSearch))         // ****** using the SEARCH in data table
        {

            $this->db->or_like('c_profile',$this->db->escape_like_str($sSearch));
            $this->db->or_like('c_code',$this->db->escape_like_str($sSearch));
            $this->db->or_like('switch',$this->db->escape_like_str($sSearch));
            //$this->db->or_like('sector',$this->db->escape_like_str($sSearch));

        }

        // Select Data

        $this->db->select('SQL_CALC_FOUND_ROWS '.str_replace(' , ', ' ', implode(', ', $aColumns)), false);

        //$this->db->select('id,c_profile,c_code,switch,exclude,sector,sortfield');
 
        $this->db->from($rtable);
        $this->db->order_by('switch','ASC');
        $this->db->order_by('c_profile','ASC');

        $rResult = $this->db->get();
        // echo $this->db->last_query();die();
        // diagnostics($this->session->userdata());die();
        // Data set length after filtering
        $this->db->select('FOUND_ROWS() AS found_rows');
        $iFilteredTotal = $this->db->get()->row()->found_rows;
        // Total data set length
        $iTotal = $this->db->count_all($rtable);
        // echo $this->db->last_query();die();
        // Output
        $output = array(
            'draw' => intval($sEcho),
            'recordsTotal' => $iTotal,
            'recordsFiltered' => $iFilteredTotal,
            'data' => array()
        );

        // $output['data'] = $rResult->result_array();
        $ctr=$iDisplayStart;
        $attributes = array("class" => "form-horizontal", "id" => "userEdit", "name" => "userEdit");
        foreach($rResult->result_array() as $aRow)
        {
            $row = array();

            $ctr+=1;
            
            $row = array(
                $ctr,               
                $aRow['c_profile'],
                $aRow['c_code'],
                $aRow['switch'],
                $aRow['exclude'],
                $aRow['sector'],
                $aRow['sortfield'],
                form_open('vrs/reference', $attributes).
                "<input type='hidden' name='userId' value='".$aRow['id']."' />".
                "<input id='btn_userUpdate' name='btn_userUpdate' type='submit' class='btn btn-default btn-sm btn btn-info' value='Edit' />".
                "<input id='ref' name='ref' type='hidden' value='".$aRow['switch']."' />".
                form_close(),
                
                form_open('vrs/reference', $attributes).
                "<input type='hidden' name='userId' value='".$aRow['id']."' />".
                "<input id='btn_userUpdate' name='btn_userUpdate' type='submit' class='btn btn-default btn-sm btn btn-danger' value='Delete' onClick='return confirm(`Are you sure you want to delete?`);' />".
                "<input id='ref' name='ref' type='hidden' value='".$aRow['switch']."' />".
                form_close(),  
            );


            // $row[] = $this->array_insert($row,0,$colValue1);
            $output['data'][] = $row;
        }





        echo json_encode($output);


    }

    public function getTable()
    {
        /* Array of database columns which should be read and sent back to DataTables. Use a space where
         * you want to insert a non-database field (for example a counter or static image)
         */

        $set_data = $this->session->all_userdata();
        $data = sessionRights($set_data);    

        // $aColumns = array('A.rep_code', 'A.co_name');
        $aColumns = array('rep_code','barcode','rfid','reg_status','pre_reg','co_name','cont_per_ln','cont_per_fn','authority_title','salutation','visitor_type','visitor_status','deleted','buyerclass','country','add_st','email','email2','venue','gcclaimed','date_apply','date_input','remarks','add_value','tag','item_code','isPaid','pid','sector','position');

        // DB table to use
        $sTable = 'v_contact_profile';
        //
        $searchHeader = $this->input->get_post('searchHeader', true);
        $iDisplayStart = $this->input->get_post('start', true);
        $iDisplayLength = $this->input->get_post('length', true);
        $iSortCol_0 = $this->input->get_post('order[0][column]', true);
        $iSortingCols = $this->input->get_post('order[0][dir]', true);
        $sSearch = $this->input->get_post('search[value]', true);
        $sEcho = $this->input->get_post('draw', true);

        $fairCode = isset($this->session->userdata('sessionData')['fcode']) ? $this->session->userdata('sessionData')['fcode'] :  "";

        $loadNewRecs = $this->input->get_post('loadme', true); 

        $loadBcodeValue = $this->input->get_post('loadRec', true);     

        // echo $iDisplayLength;die('test');
        // Paging
        if(isset($iDisplayStart) && $iDisplayLength != '-1')
        {
            $this->db->limit($this->db->escape_str($iDisplayLength), $this->db->escape_str($iDisplayStart));
        }
        // Ordering
        if(isset($iSortCol_0))
        {
            for($i=0; $i<intval($iSortingCols); $i++)
            {
                $iSortCol = $this->input->post('iSortCol_'.$i, true);
                // $bSortable = $this->input->post('bSortable_'.intval($iSortCol), true);
                $sSortDir = $this->input->post('sSortDir_'.$i, true);

                // if($bSortable == 'true')
                // {
                    $this->db->order_by($aColumns[intval($this->db->escape_str($iSortCol))], $this->db->escape_str($sSortDir));
                // }
            }
        }

        /*
         * Filtering
         * NOTE this does not match the built-in DataTables filtering which does it
         * word by word on any field. It's possible to do here, but concerned about efficiency
         * on very large tables, and MySQL's regex functionality is very limited
         */
        //main filter

        if(isset($sSearch) && !empty($sSearch))         // ****** using the SEARCH in data table
        {
            // for($i=0; $i<count($aColumns); $i++)
            // {
            //     //$bSearchable = $this->input->post('bSearchable_'.$i, true);
            //     $bSearchable = $this->input->post('columns['.$i.'][searchable]', true);
            //     // Individual column filtering
            //     if(isset($bSearchable) && $bSearchable == 'true')
            //     {
            //         $this->db->or_like($aColumns[$i], $this->db->escape_like_str($sSearch));
            //     }
            // }
            $this->db->or_like('co_name',$this->db->escape_like_str($sSearch));
            $this->db->or_like('cont_per_ln',$this->db->escape_like_str($sSearch));
            $this->db->or_like('cont_per_fn',$this->db->escape_like_str($sSearch));
            $this->db->or_like('country',$this->db->escape_like_str($sSearch));
            $this->db->or_like('rep_code',$this->db->escape_like_str($sSearch));
            $this->db->or_like('barcode',$this->db->escape_like_str($sSearch));
            $this->db->or_like('rfid',$this->db->escape_like_str($sSearch));
            $this->db->or_like('buyerclass',$this->db->escape_like_str($sSearch));
            $this->db->or_like('isPaid',$this->db->escape_like_str($sSearch));
            $this->db->or_like('visitor_type',$this->db->escape_like_str($sSearch));
            $this->db->or_like('fameplus_id',$this->db->escape_like_str($sSearch));
            $this->db->or_like('rep_codeOld',$this->db->escape_like_str($sSearch));
            $this->db->or_like('rfid',$this->db->escape_like_str($sSearch));
            $this->db->or_like('email',$this->db->escape_like_str($sSearch));
            $this->db->or_like('email2',$this->db->escape_like_str($sSearch));
            $this->db->or_like('item_code',$this->db->escape_like_str($sSearch));
            $this->db->or_like('visitor_status',$this->db->escape_like_str($sSearch));
            $this->db->or_like('position',$this->db->escape_like_str($sSearch));

        }

        // Select Data

        $this->db->select('SQL_CALC_FOUND_ROWS '.str_replace(' , ', ' ', implode(', ', $aColumns)), false);

        // $this->db->join('v_attendance B', 'A.rep_code = B.rep_code');

        //search header
        if (isset($searchHeader) && !empty($searchHeader)){
            // for($i=0; $i<count($aColumns); $i++)
            // {
            //     // $bSearchable = $this->input->post('columns['.$i.'][searchable]', true);
            //     // // Individual column filtering
            //     // if(isset($bSearchable) && $bSearchable == 'true')
            //     // {
            //         // $this->db->or_like($aColumns[$i], $this->db->escape_like_str($searchHeader));
            //     $this->db->where($aColumns[$i], $this->db->escape_like_str($searchHeader));
            //     // }
            // }
            // $this->db->where('co_name', $this->db->escape_like_str($searchHeader));
            if(isset($fairCode) && !empty($fairCode)){
                // $criteria = "AND B.fair_code='".$fairCode."' AND ";
                $criteria = "AND";
            }
            $aColumns = array('A.rep_code','A.barcode','rfid','B.reg_status','B.pre_reg','A.co_name','cont_per_ln','cont_per_fn','authority_title','salutation','B.visitor_type','B.visitor_status','A.deleted','B.buyerclass','country','add_st','email','email2','venue','gcclaimed','B.date_apply','B.date_input','A.remarks','add_value','tag','B.item_code','B.remarks AS isPaid','fameplus_id','rep_codeOld','pid','B.sector','A.position');



            $sTable = "(SELECT ".implode(",", $aColumns)." FROM v_contact_profile A ".

                "LEFT JOIN ( SELECT date_apply,date_input,refno,rep_code,visitor_type,fair_code,reg_status,pre_reg,buyerclass,visitor_status,item_code,remarks,assigned_table,health_declaration,antigen_test,sector  FROM v_attendance WHERE fair_code='".$fairCode."' ORDER BY refno ASC) as B ON A.rep_code=B.rep_code WHERE TRUE ".$criteria.

                " (A.co_name like '%".$searchHeader."%' ".
                "OR cont_per_ln like '%".$searchHeader."%' ".
                "OR cont_per_fn LIKE '%".$searchHeader."%' ".
                "OR country LIKE '%".$searchHeader."%' ".
                "OR fameplus_id LIKE '%".$searchHeader."%' ".
                "OR A.rep_code LIKE '%".$searchHeader."%' ".
                "OR A.barcode LIKE '%".$searchHeader."%' ".
                "OR A.rep_codeOld LIKE '%".$searchHeader."%' ".
                "OR B.visitor_type LIKE '%".$searchHeader."%' ".
                "OR rfid LIKE '%".$searchHeader."%' ".
                "OR A.email LIKE '%".$searchHeader."%' ".
                "OR A.email2 LIKE '%".$searchHeader."%' ".
                "OR B.buyerclass LIKE '%".$searchHeader."%' ".
                "OR B.item_code LIKE '%".$searchHeader."%' ".
                "OR B.visitor_status LIKE '%".$searchHeader."%' ".
                "OR B.remarks LIKE '%".$searchHeader."%' ".
                "OR A.position LIKE '%".$searchHeader."%' ".
                ") GROUP BY rep_code) A";

            $this->db->from($sTable);
            $this->db->group_by('A.rep_code');
            $this->db->order_by('A.co_name','ASC');
            $this->db->order_by('A.cont_per_ln','ASC');



        }

        else if(isset($loadNewRecs) && !empty($loadNewRecs)) {

            $aColumns = array('A.rep_code','A.barcode','rfid','B.reg_status','B.pre_reg','A.co_name','cont_per_ln','cont_per_fn','authority_title','salutation','B.visitor_type','B.visitor_status','A.deleted','B.buyerclass','country','add_st','email','email2','venue','gcclaimed','B.date_apply','B.date_input','A.remarks','add_value','tag','B.item_code','B.remarks AS isPaid','fameplus_id','rep_codeOld','pid','B.sector','A.position');

            $sTable = "(SELECT ".implode(",", $aColumns)." FROM v_contact_profile A ".
                "INNER JOIN ( SELECT date_apply,date_input,refno,rep_code,visitor_type,fair_code,reg_status,pre_reg,buyerclass,visitor_status,item_code,remarks,assigned_table,health_declaration,antigen_test,sector  FROM v_attendance WHERE fair_code='".$loadNewRecs."' ORDER BY refno ASC) as B ON A.rep_code=B.rep_code WHERE TRUE GROUP BY rep_code) A";// AND ".$criteria.") A";

            $this->db->from($sTable);
            $this->db->group_by('A.rep_code');
            $this->db->order_by('A.co_name','ASC');
            $this->db->order_by('A.cont_per_ln','ASC'); 

        }

        else if(isset($loadBcodeValue) && !empty($loadBcodeValue)) {

            $aColumns = array('A.rep_code','A.barcode','rfid','B.reg_status','B.pre_reg','A.co_name','cont_per_ln','cont_per_fn','authority_title','salutation','B.visitor_type','B.visitor_status','A.deleted','B.buyerclass','country','add_st','email','email2','venue','gcclaimed','B.date_apply','B.date_input','A.remarks','add_value','tag','B.item_code','B.remarks AS isPaid','fameplus_id','rep_codeOld','pid','B.sector','A.position');

            $sTable = "(SELECT ".implode(",", $aColumns)." FROM v_contact_profile A ".
                "LEFT JOIN ( SELECT date_apply,date_input,refno,rep_code,visitor_type,fair_code,reg_status,pre_reg,buyerclass,visitor_status,item_code,remarks,assigned_table,health_declaration,antigen_test,sector  FROM v_attendance WHERE fair_code='".$fairCode."' ORDER BY refno ASC) as B ON A.rep_code=B.rep_code WHERE A.barcode='".$loadBcodeValue."' GROUP BY rep_code) A";// AND ".$criteria.") A";

            $this->db->from($sTable);
            $this->db->group_by('A.rep_code');
            $this->db->order_by('A.co_name','ASC');
            $this->db->order_by('A.cont_per_ln','ASC'); 

        }

        else{

            if(isset($fairCode) && !empty($fairCode)){
                // $criteria = "B.fair_code='".$fairCode."'";
                $criteria = "";
            }
            $aColumns = array('A.rep_code','A.barcode','rfid','B.reg_status','B.pre_reg','A.co_name','cont_per_ln','cont_per_fn','authority_title','salutation','B.visitor_type','B.visitor_status','A.deleted','B.buyerclass','country','add_st','email','email2','venue','gcclaimed','B.date_apply','B.date_input','A.remarks','add_value','tag','B.item_code','B.remarks AS isPaid','fameplus_id','rep_codeOld','pid','B.sector','A.position');

            $sTable = "(SELECT ".implode(",", $aColumns)." FROM v_contact_profile A ".
                "INNER JOIN ( SELECT date_apply,date_input,refno,rep_code,visitor_type,fair_code,reg_status,pre_reg,buyerclass,visitor_status,item_code,remarks,assigned_table,health_declaration,antigen_test,sector  FROM v_attendance WHERE fair_code='".$fairCode."' ORDER BY refno ASC) as B ON A.rep_code=B.rep_code WHERE TRUE GROUP BY rep_code) A";// AND ".$criteria.") A";

            $this->db->from($sTable);
            $this->db->group_by('A.rep_code');
            $this->db->order_by('A.co_name','ASC');
            $this->db->order_by('A.cont_per_ln','ASC');
        }

        $rResult = $this->db->get();
        // echo $this->db->last_query();die();
        // diagnostics($this->session->userdata());die();
        // Data set length after filtering
        $this->db->select('FOUND_ROWS() AS found_rows');
        $iFilteredTotal = $this->db->get()->row()->found_rows;
        // Total data set length
        $iTotal = $this->db->count_all($sTable);
        // echo $this->db->last_query();die();
        // Output
        $output = array(
            'draw' => intval($sEcho),
            'recordsTotal' => $iTotal,
            'recordsFiltered' => $iFilteredTotal,
            'data' => array()
        );
        // $output['data'] = $rResult->result_array();
        $ctr=$iDisplayStart;
        foreach($rResult->result_array() as $aRow)
        {
            $row = array();

            $ctr+=1;
            $dspRegStatus = function ($rowData=null){
                if ($rowData['deleted']==1){
                    return "<div class='text-center'><i style='color:#FF0000;' class='fa fa-trash-o'></i></div>";
                }else{
                    switch ($rowData['reg_status']){
                        case "T":
                            $xLabel='Unmark';
                            $xIcon = "<i style='color:#009933;' class='fa fa-check'></i>";
                            break;
                        default:
                            $xLabel='Mark';
                            $xIcon="<i style='color:#FF0000;' class='fa fa-close'></i>";
                            break;
                    }
                    $wristband='';
                    if (in_array('enable_mark', $this->session->userdata('sessionData'))){
                        $wristband = "";
                        if (in_array('vrs_enable_wristband', $this->session->userdata('sessionData'))) { $wristband = "yes"; }
                        $url = "<div class='text-center'><a href='".site_url('vrs/selectedRec?repcode='.$rowData['rep_code'].'&mmenu=mark&vurl=&wristband='.$wristband)."' onClick = confirmArrive('".$xLabel."')>";

                    }else{
                        $url = "<div class='text-center'><a href= '".site_url("vrs/selectedRec?repcode=".$rowData["rep_code"]."&mmenu=mark&vurl=")."' onClick='return false'>";
                    }
                    return $url.$xIcon."</a></div>";

                }

            };
            // $row = array(
            //     $ctr,
            //     // "<input type='radio'>",
            //     "<input type='radio' id='repcode' name='repcode' value=".$aRow["rep_code"].">",
            //     $aRow['reg_status'] = $dspRegStatus($aRow),
                
            //     $aRow['pre_reg']=='P' ? "<div class='text-center'><i style='color:#009933;' class='fa fa-check'></i></div>" : "<div class='text-center'><i style='color:#FF0000;' class='fa fa-close'></i></div>",
               
            //     $aRow['co_name'],
            //     $aRow['authority_title']=='' ? $aRow['salutation'] : $aRow['authority_title'],
            //     $aRow['cont_per_ln'],
            //     $aRow['cont_per_fn'],
            //     $aRow['country'],
            //     $aRow['visitor_type'],
            //     $aRow['remarks'],//representation
            //     $aRow['item_code'],
            //     $aRow['barcode'],
            //     $aRow['date_apply'],
            //     $aRow['pid'],
            //     $aRow['sector']
            // );

               $arr1 = array($ctr,"<input type='radio' id='repcode' name='repcode' value=".$aRow["rep_code"].">");
               $arr2 = array($aRow['reg_status'] = $dspRegStatus($aRow));   

               $mergeArr1 = array_merge($arr1,$arr2); 

               if(isset($data['vrs_hide_pregcol']) && $data['vrs_hide_pregcol']=="") { 

                    $arr3 = array(
                            $aRow['pre_reg']=='P' ? "<div class='text-center'><i style='color:#009933;' class='fa fa-check'></i></div>" : "<div class='text-center'><i style='color:#FF0000;' class='fa fa-close'></i></div>"
                          );
                    $mergeArr1 = array_merge($arr1,$arr2,$arr3); 
               } 

               $arr4 = array(
                            $aRow['co_name'],
                            $aRow['authority_title']=='' ? $aRow['salutation'] : $aRow['authority_title'],
                            $aRow['cont_per_ln'],
                            $aRow['cont_per_fn']);

               $mergeArr2 = array_merge($mergeArr1,$arr4);             

               if(isset($data['vrs_hide_designation']) && $data['vrs_hide_designation']=="") { 
                    $arr5 = array($aRow['position']);
                    $mergeArr2 = array_merge($mergeArr1,$arr4,$arr5); 
               }            
                            
                
               $mergeArr3 = array_merge($mergeArr2);                

               if(isset($data['vrs_hide_email']) && $data['vrs_hide_email']=="") { 
                    $arr6 = array($aRow['email']);
                    $mergeArr3 = array_merge($mergeArr2,$arr6); 
               }  

               $arr7 = array($aRow['country']); 

               $mergeArr4 = array_merge($mergeArr3,$arr7);

               if(isset($data['vrs_hide_represent']) && $data['vrs_hide_represent']=="") { 
                    $arr8 = array($aRow['remarks']);
                    $mergeArr4 = array_merge($mergeArr3,$arr7,$arr8);
               } 

               $mergeArr5 = array_merge($mergeArr4);

               if(isset($data['vrs_hide_promocode']) && $data['vrs_hide_promocode']=="") { 
                    $arr8 = array($aRow['item_code']);
                    $mergeArr5 = array_merge($mergeArr4,$arr8);
               } 

               $arr9 = array($aRow['barcode']);
               $arr10= array(
                            $aRow['date_input'],
                            $aRow['visitor_type'],
                            $aRow['sector']
                            
                          ); 

               $mergeArr6 = array_merge($mergeArr5,$arr9,$arr10);            

            //die("ccc=".$data['vrs_enable_dashboard']);     

            // $row[] = $this->array_insert($row,0,$colValue1);
            $output['data'][] = $mergeArr6;
        }


        echo json_encode($output);
    }

/**
 * @param array      $array

 */





    /**
 * @param array      $array
 * @param int|string $position
 * @param mixed      $insert
 */
    function array_insert(&$array, $position, $insert)
    {
        if (is_int($position)) {
            array_splice($array, $position, 0, $insert);
        } else {
            $pos   = array_search($position, array_keys($array));
            $array = array_merge(
                array_slice($array, 0, $pos),
                $insert,
                array_slice($array, $pos)
            );
        }
    }
}
?>
<?php

class Rsvp extends CI_controller{

  public function __construct(){
        parent::__construct();

        ini_set('memory_limit','256M');

        $this->load->library(array('form_validation','session','encryption'));
        $this->load->helper(array('form', 'url', 'array', 'html', 'captcha'));
        $this->load->model('Rsvp_model');                // load site_model.php from models folder

   }


  public function index() {
    $repCode = $this->input->get('rcode') ?? '';
    if ($repCode!='')
    $data = $this->Rsvp_model->updateRsvp($repCode);
    if (count($data)>0){
        $sendResult = $this->sendEmail($data);
    }
    $this->load->view('view_rsvp_landing');
  }

  function sendEmail($data)  {
        // print_r($data);
        // Load PHPMailer library
        $this->load->library('phpmailer_lib');

        // PHPMailer object
        $mail = $this->phpmailer_lib->load();

        // SMTP configuration
        $mail->isSMTP();
        $mail->Host     = SMTP_HOST;
        $mail->SMTPAuth = SMTP_AUTH;
        $mail->SMTPAutoTLS = SMTP_AutoTLS;
        $mail->Username = SMTP_USER;
        $mail->Password = SMTP_PASS;
        $mail->SMTPSecure = SMTP_ENCRYPT;     // 'ssl'    // tsl
        $mail->Port     = SMTP_PORT;

        $mail->setFrom('<EMAIL>');                  //  ('<EMAIL>', 'CodexWorld');
        // $mail->addReplyTo($vReplyto);            //  ('<EMAIL>', 'CodexWorld');

        // Add a recipient
        $mail->addAddress('<EMAIL>');
        // $mail->addAddress('<EMAIL>');
        // Add cc or bcc
        // $mail->addCC($vCC);                       //('<EMAIL>');
        // $mail->addBCC($vBCC);                     //('<EMAIL>');

        // Email subject
        $mail->Subject = 'Confirmed Attendance to the Manila FAME Networking Reception';

        // Set email format to HTML
        $mail->isHTML(true);

        // Email body content
        $mailContent =$this->load->view('view_rsvp_email',$data,TRUE);
        $mail->Body = $mailContent;

        // Send email
        if(!$mail->send()){
           // echo 'Message could not be sent.';
           // echo 'Mailer Error: ' . $mail->ErrorInfo;
           $result = $mail->ErrorInfo;
        }else{
            //echo 'Message has been sent';
           $result = "";
        }

    return $result;
  }


}




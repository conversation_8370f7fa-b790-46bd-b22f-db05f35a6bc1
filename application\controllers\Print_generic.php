<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
 
class print_generic extends CI_Controller
{

   public function __construct(){
      parent::__construct();
      ini_set('memory_limit','256M');
      $this->load->helper('array');  // used by element() in function updateSurvey
      $this->load->model('vrs_model');
      $this->load->model('vrs_read_model');
      $this->load->model('master_model');
      $this->load->model('print_model');
     }

   public function c_generate_id_test(){
      $set_data = $this->session->all_userdata(); //E5
      $data = sessionRights($set_data);
      $this->load->view('v_generic_id',$data);
   }
   public function c_generate_id(){
      $data['homeBar'] = "class='active'";
       //===========================================
      
       $set_data = $this->session->all_userdata();

       //print_r($set_data['sessionData']); die();
                   
      if (isset($set_data['sessionData']) && isset($set_data['sessionData']['realname'])  && $set_data['sessionData']['realname'] != NULL) {
         $data['read_set_value'] = $set_data['sessionData']['realname'];
         $data['sessionRights'] = $set_data['sessionData']['urights'];
         $data['loginuser'] = $set_data['sessionData']['loginuser'];       
         
         $data['sysName'] = $set_data['sessionData']['sysName'];        
         $data['systitle'] = $set_data['sessionData']['systitle'];
         $data['eventDB'] = $set_data['sessionData']['eventDB'];

         $data['fcode'] = $set_data['sessionData']['fcode'];
         $data['sector'] = $set_data['sessionData']['sector'];
         $data['fairdesc'] = $set_data['sessionData']['fdesc'];         
         $data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

         $data['myVenue'] = $set_data['sessionData']['venue'];
         $data['myVenueNum'] = $set_data['sessionData']['venueNum'];

         $data['controller_CI'] = $set_data['sessionData']['controller_CI'];        
         

         $data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
         $data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");

         $data['emailTemplate']= $this->master_model->loadRec("v_reference","where c_code ='MFmessage' and sector like '%".$data['sector']."%' and exclude=0 order by sortfield");

         $data['RegType']=$this->master_model->loadRec("v_reference","where switch='BSTAT' and sector like '%".$data['sector']."%' order by sortfield");
         if(isset($data['vrs_diy_visitor_print']) && $data['vrs_diy_visitor_print']=="") {$data['disableVisitPrint']="";} else {$data['disableVisitPrint']="&autoprint=no";}
         //===========================================
            //======== get RIGHTS procedure =============
            //===========================================
            foreach ($data['userAccessRef'] as $r1)
            {
              
              $founds=0;                
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {
               
               $xxx = $set_data['sessionData'][$x."r"];               
                                                                              // Using ${} is a way to create dynamic variables, ex.====> ${'vrs_'.$r1['c_code']} = "";
               if ($r1['c_code']==$xxx) { $data['vrs_'.$r1['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r1['c_code']] = ""; }
              //echo $data['vrs_'.$r1['c_code']]."<br>";

            }
            foreach ($data['userAccessMod'] as $r2)
            {
              
              $founds=0;                
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {
               
               $xxx = $set_data['sessionData'][$x."r"];               
               // Using ${} is a way to create dynamic variables,
               if ($r2['c_code']==$xxx) { $data['vrs_'.$r2['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r2['c_code']] = ""; }
              //echo $data['vrs_'.$r2['c_code']]."<br>";

            }
            //===========================================
            //===========================================


         // $data['vrs_diy_general_public_print'] = $set_data['sessionData']['vrs_diy_general_public_print'];
                           
         $data['mmenu']='';
         $this->load->view('v_generic_id',$data);
      }
  }
  public function getLastNumber(){
    $idType = $this->input->post('idType');
    // $idType='VISITOR';
    $lastNumber = $this->print_model->getLastNumber($idType);
    echo json_encode($lastNumber);
  }
  public function generateID(){
    $idType = $this->input->post('idtype');
    $lastNumber = $this->input->post('lastPrinted');
    $toPrint = $this->input->post('noIds');
    $addText = $this->input->post('addText');
    $requester = $this->input->post('requester');

    $fontSize="12px";
    $filename = FAIR_CODE."_".$lastNumber.".pdf";
    $pdfFilePath = FCPATH."idbadge/$filename.pdf";
    $data['filename'] = $filename;
    $data['idType'] = $idType;
    $data['addText'] = $addText;
    $data['myDay'] = $this->getDay();
    //$data['bannerpic'] = 'https://ifexphilippines.com/event/images/IFEX2024.png';
    

    require_once APPPATH .'/third_party/mpdf/vendor/autoload.php';
    $pagesized = [88.9,63.5];
    $pdf = new \Mpdf\Mpdf();
    $pdf->addPage("P","","","","","0","0","0","0","","","","","","","","","","","",$pagesized);   //92,33.02
    //generate the PDF!
    for ($i=1;$i<=$toPrint;$i++){
      $data['counter'] = $lastNumber+$i;
      $dspHTML=$this->load->view('createGeneric', $data, true);   
      $pdf->WriteHTML($dspHTML,2);
    }
    $pdf->Output($pdfFilePath, 'F');// save to file because we can
    $pdf->Output($pdfFilePath, 'I');//open to browser
    $this->print_model->insertRecord($idType,$toPrint,$addText,$requester);
  }

  public function getDay(){
      $chkDay = $this->master_model->getRec("v_reference","where switch='dayofuse' and exclude='0' and sector LIKE '%".$vsector."%' and c_code= ? limit 1",date('Y-m-d'),""); 
      if (count($chkDay[0])){
        return $chkDay[0]['c_profile'];
      }else{
        return 'DAY 0';
      }
    }
}
<?php

defined('BASEPATH') OR exit('No direct script access allowed');



date_default_timezone_set('Asia/Manila');



/*

|--------------------------------------------------------------------------

| Display Debug backtrace

|--------------------------------------------------------------------------

|

| If set to TRUE, a backtrace will be displayed along with php errors. If

| error_reporting is disabled, the backtrace will not display, regardless

| of this setting

|

*/

defined('SHOW_DEBUG_BACKTRACE') OR define('SHOW_DEBUG_BACKTRACE', TRUE);



/*

|--------------------------------------------------------------------------

| File and Directory Modes

|--------------------------------------------------------------------------

|

| These prefs are used when checking and setting modes when working

| with the file system.  The defaults are fine on servers with proper

| security, but you may wish (or even need) to change the values in

| certain environments (Apache running a separate process for each

| user, PHP under CGI with Apache suEXEC, etc.).  Octal values should

| always be used to set the mode correctly.

|

*/

defined('FILE_READ_MODE')  OR define('FILE_READ_MODE', 0644);

defined('FILE_WRITE_MODE') OR define('FILE_WRITE_MODE', 0666);

defined('DIR_READ_MODE')   OR define('DIR_READ_MODE', 0755);

defined('DIR_WRITE_MODE')  OR define('DIR_WRITE_MODE', 0755);



/*

|--------------------------------------------------------------------------

| File Stream Modes

|--------------------------------------------------------------------------

|

| These modes are used when working with fopen()/popen()

|

*/

defined('FOPEN_READ')                           OR define('FOPEN_READ', 'rb');

defined('FOPEN_READ_WRITE')                     OR define('FOPEN_READ_WRITE', 'r+b');

defined('FOPEN_WRITE_CREATE_DESTRUCTIVE')       OR define('FOPEN_WRITE_CREATE_DESTRUCTIVE', 'wb'); // truncates existing file data, use with care

defined('FOPEN_READ_WRITE_CREATE_DESTRUCTIVE')  OR define('FOPEN_READ_WRITE_CREATE_DESTRUCTIVE', 'w+b'); // truncates existing file data, use with care

defined('FOPEN_WRITE_CREATE')                   OR define('FOPEN_WRITE_CREATE', 'ab');

defined('FOPEN_READ_WRITE_CREATE')              OR define('FOPEN_READ_WRITE_CREATE', 'a+b');

defined('FOPEN_WRITE_CREATE_STRICT')            OR define('FOPEN_WRITE_CREATE_STRICT', 'xb');

defined('FOPEN_READ_WRITE_CREATE_STRICT')       OR define('FOPEN_READ_WRITE_CREATE_STRICT', 'x+b');



/*

|--------------------------------------------------------------------------

| Exit Status Codes

|--------------------------------------------------------------------------

|

| Used to indicate the conditions under which the script is exit()ing.

| While there is no universal standard for error codes, there are some

| broad conventions.  Three such conventions are mentioned below, for

| those who wish to make use of them.  The CodeIgniter defaults were

| chosen for the least overlap with these conventions, while still

| leaving room for others to be defined in future versions and user

| applications.

|

| The three main conventions used for determining exit status codes

| are as follows:

|

|    Standard C/C++ Library (stdlibc):

|       http://www.gnu.org/software/libc/manual/html_node/Exit-Status.html

|       (This link also contains other GNU-specific conventions)

|    BSD sysexits.h:

|       http://www.gsp.com/cgi-bin/man.cgi?section=3&topic=sysexits

|    Bash scripting:

|       http://tldp.org/LDP/abs/html/exitcodes.html

|

*/

defined('EXIT_SUCCESS')        OR define('EXIT_SUCCESS', 0); // no errors
defined('EXIT_ERROR')          OR define('EXIT_ERROR', 1); // generic error
defined('EXIT_CONFIG')         OR define('EXIT_CONFIG', 3); // configuration error
defined('EXIT_UNKNOWN_FILE')   OR define('EXIT_UNKNOWN_FILE', 4); // file not found
defined('EXIT_UNKNOWN_CLASS')  OR define('EXIT_UNKNOWN_CLASS', 5); // unknown class
defined('EXIT_UNKNOWN_METHOD') OR define('EXIT_UNKNOWN_METHOD', 6); // unknown class member
defined('EXIT_USER_INPUT')     OR define('EXIT_USER_INPUT', 7); // invalid user input
defined('EXIT_DATABASE')       OR define('EXIT_DATABASE', 8); // database error
defined('EXIT__AUTO_MIN')      OR define('EXIT__AUTO_MIN', 9); // lowest automatically-assigned error code
defined('EXIT__AUTO_MAX')      OR define('EXIT__AUTO_MAX', 125); // highest automatically-assigned error code

define('SHOW_LIST_DB',TRUE);			// TRUE or FALSE  // 	SHOW dropdown of sectors DB in login page

define('SYS_STATE', 'TEST'); 			// "TEST" -> test stage | "" -> production stage
define('SHOW_ERROR',false);				// TRUE or FALSE   **** CHANGE VALUE in index.php {development/production}
define('FAIR_CODE', 'MFIO2024');				// use in registration.php
define('SEND_EMAIL', 'YES'); 			// send email: value YES or NO
define('UPLOAD_MAXFILESIZE', '10240');		// in KB, use in upload_files.php


define('MASTERCLASS_CDATE', strtotime(date("Y-m-d")));
define('MASTERCLASS_XDATE', strtotime("2018-08-09"));


define('REFERENCE_CODE', 'CPH2020');			// use in function createPaymentOrder
//define('REFERENCE_CODE', 'TEST2018');

define('SHOW_BUNDLE', 'no');
//define('SHOW_PAYMENT', 'yes');
//define('SHOW_PNB_OTC', 'yes');


define('AMOUNT_PROFESSIONAL', '0');
define('AMOUNT_STUDENT', '0');


// define('SMTP_HOST', 'smtp.ems-email.com');
// define('SMTP_PORT', '587');		//465 ==== 2525
// define('SMTP_USER', '');		//citem.email
// define('SMTP_PASS', '');		//0s0Em@1Lsa0uf
// define('SMTP_ENCRYPT', '');
// define('SMTP_AUTH', false);
// define('SMTP_AutoTLS', false);


define('SMTP_HOST', 'smtp2.agmstech.com');		
define('SMTP_PORT', '587');		//2525
define('SMTP_USER', '<EMAIL>');		//citem.email
define('SMTP_PASS', 'o#7p6&mZ5%QEhrtNMObE#LQkDyFmPXv@$pW');		//0s0Em@1Lsa0uf
define('SMTP_ENCRYPT', '');
define('SMTP_AUTH', true);
define('SMTP_AutoTLS', true);



if(SYS_STATE == 'TEST')

	{
		define('EVENT_DB', 'citem_stakeholders');	// *** USED in vrs.php ***
		define('MASTER_DB', 'citem_masterfile');
		define('HOSTNAME','mysql');
		define('UNAME','root');
		define('PWORD','root');

		//======= DBP TEST========================================

		define('TERMINAL_ID', '83');
		define('TRANSACTON_KEY', '04f08c7c8b27fdb52ffdf99a0445dc34a2d7c066');
		define('DBP_URL', 'https://testipg.apollo.com.ph:8443/transaction/verify?');

		//======= Dragon Pay TEST=================================

		define('MERCHANT_ID', 'CITEM');
		define('MERCHANT_PASSWORD', 'nswfJJvnhTTyPoH');
		define('ENV_TEST', 1);

		//====================================================

	}

else

	{

		define('EVENT_DB', 'citem_stakeholders');				// *** USED in vrs.php ***
		define('MASTER_DB', 'citem_masterfile');
		define('HOSTNAME','localhost');
		define('UNAME','citem_user12');
		define('PWORD','W@KHtFfTi6FH');

		//======= DBP ============================================

		define('TERMINAL_ID', '705');
		define('TRANSACTON_KEY', '3feabf2648e3b194e45d79c2b61f0c89ccaf37db');
		define('DBP_URL', 'https://ipg.devbnkphl.com:8443/transaction/verify?');

		//======= Dragon Pay =================================
		define('MERCHANT_ID', 'CITEM');
		define('MERCHANT_PASSWORD', '2FgJij25bfdDoA3');
		define('ENV_TEST', 0);

	}






<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

use Ngekoding\CodeIgniterDataTables\DataTables;
require_once FCPATH.'vendor/autoload.php';
class Membersdash extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
			ini_set('memory_limit','256M');
			$this->load->library('session');
			$this->load->helper('form');
			$this->load->helper('url');
			$this->load->helper('html');
			$this->load->helper('array');  // used by element() in function updateSurvey
			$this->load->database();
			$this->load->library('encryption');
			$this->load->model('master_model');
			$this->load->model('vdetails_model');
			$this->load->library('phpmailer_lib');
			$this->dashboard = $this->load->model('dashboard_model');


	}

	public function index(){
		$set_data = $this->session->all_userdata(); //E5
     	$data = sessionRights($set_data);
     	$decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $this->input->get('d'));
       	$get = $this->encryption->decrypt($decryptX);
       	$data['tmp'] = explode('#',$get);

		if($data['tmp'][3] == 'Incomplete Registration -Pre-Registered'){
			$viewHeader = 'Incomplete Registration';
		}elseif($data['tmp'][3] == 'Incomplete Registration -Cumulative'){
			$viewHeader = 'Incomplete Registration';
		}
		else{
			$viewHeader = $data['tmp'][3];
		}

       	$data['ViewHeader'] = $viewHeader;
       	$data['asofDate'] = date('F d, Y h:i A');
       	// diagnostics($data['tmp']);
		$this->load->view('vrs_memberdetails',$data);
	}

//====================================================

	public function ajax_datatable(){
		$decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $this->input->get('d'));
       	$get = $this->encryption->decrypt($decryptX);
		list($visitorType, $queryFilter, $visitorSector, $visitorView) = explode('#', $get);

       	$data['fcode'] = $_SESSION["sessionData"]['fcode'];
       	$qryAdd = $this->_generate_qryAdd($get);

		$query = $this->vdetails_model->getDtcpProfile($data['fcode'],$visitorView);

		$datatables = new DataTables($query, '3');
		if($visitorType=="TRADE BUYER"){
			$datatables->addColumn('action', function($row){
				return '<div class="btn-group">
							<button type="button" class="btn btn-success btn-sm resend-email-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="TB" title="Resend Email Confirmation">
								<i class="fa fa-envelope"></i>
							</button>
						</div>';
			});
		}
		$datatables->addColumn('remarks', function($row){
			return $this->_getRemarks($row->validation_status);
		});
		$datatables->addSequenceNumber('row_number')
			->asObject()
			->generate();
	}

//====================================================

	private function _getRemarks($validation_status){
		if($validation_status == 'APPROVED TRADE'){
			return 'APPROVED as Trade Buyer';
		}
		elseif($validation_status == 'PENDING TRADE'){
			return 'PENDING as Trade Buyer';
		}
		elseif($validation_status == 'INCOMPLETE TRADE'){
			return 'INCOMPLETE Registration';
		}
		elseif($validation_status == 'DEACTIVATED TRADE'){
			return 'DEACTIVATED Account';
		}
		elseif($validation_status == 'DISAPPROVED TRADE'){
			return 'DISAPPROVED as Trade Buyer';
		}
	}

//====================================================
	private function _generate_qryAdd($get){
		list($visitorType, $queryFilter, $visitorSector, $dashboardView) = explode('#',$get);
		if(strtolower($dashboardView)=='cumulative'){
			if(strtolower($visitorType)=='trade buyer'){
				return "B.visitor_type LIKE '%TRADE BUYER%' and B.reg_status='T' and ";
			}
			if(strtolower($visitorType)=='guest'){
				return "B.visitor_type LIKE '%GUEST%' and B.reg_status='T' and ";
			}
			if(strtolower($visitorType)=='general public'){
				return "B.visitor_type LIKE '%GENERAL PUBLIC%' and B.reg_status='T' and ";
			}
			if(strtolower($visitorType)=='media'){
				return "B.visitor_type LIKE '%MEDIA%' and B.reg_status='T' and ";
			}

		}
		if(strtolower($dashboardView)=='pre-registered'){
			if(strtolower($visitorType)=='trade buyer'){
				return "B.visitor_type LIKE '%TRADE BUYER%' and B.pre_reg='P' and ";
			}
			if(strtolower($visitorType)=='guest'){
				return "B.visitor_type LIKE '%GUEST%' and B.pre_reg='P' and ";
			}
			if(strtolower($visitorType)=='general public'){
				return "B.visitor_type LIKE '%GENERAL PUBLIC%' and B.pre_reg='P' and ";
			}
			if(strtolower($visitorType)=='media'){
				return "B.visitor_type LIKE '%MEDIA%' and B.pre_reg='P' and ";
			}
		}

		//cummulative
		//media = B.visitor_type LIKE '%MEDIA%' and B.reg_status='T' and
		//paying visitor = B.visitor_type LIKE '%GENERAL PUBLIC%' and B.reg_status='T' and
		//non-trade = B.visitor_type LIKE '%GUEST%' and B.reg_status='T' and
		// TB = B.visitor_type LIKE '%TRADE BUYER%' and B.reg_status='T' and
		//
		// pre reg
		//media = B.visitor_type LIKE '%MEDIA%' and B.pre_reg='P' and
		//paying visitor = B.visitor_type LIKE '%GENERAL PUBLIC%' and B.pre_reg='P' and
		//non-trade = B.visitor_type LIKE '%GUEST%' and B.pre_reg='P' and
		// TB = B.visitor_type LIKE '%TRADE BUYER%' and B.pre_reg='P' and
		//
	}


//====================================================

	private function sanitizeInput($value, $default = '', $required = false)
	{
		// Check if value is null, empty string, or only whitespace
		if ($value === null || $value === '' || (is_string($value) && trim($value) === '')) {
			return $required ? $default : '';
		}
		
		// For non-empty values, trim whitespace and return
		return is_string($value) ? trim($value) : $value;
	}






}

<?php
/*

  vrs=1 -> less required fields    
  vrs=2 -> required fields      
  vrs=3 -> standalone
  vedit=1 -> update                
  vedit=2 -> add person selected in VRS
  allow=1 -> allow edit even if prereg is closed
  allow=2 -> allow edit even if prereg is closed but promocode is required
  showall=1 -> less page tabs
  app=1 -> mobile app, disable edit if validated in attendance table, showall=1
  app=2 -> mobile app, enable edit if validated in attendance table, showall=1
  app=3 -> will encrypt email, fair_code then redirect to registration/step2?pid={encrypt value} *** used for edit option in invite
  app=4 -> will get previous data in table and mark prereg *** used in prereg to bypass edit option
  app=5 -> enable auto submition of page. use to display agad form_error()
  rel=1 -> bypass auto submition of page. 

 {domain}/ci/index.php/registration/step1?fcode=MFIM2015&vt={TB,VISITOR,MEDIA,GUEST}
 {domain}/ci/index.php/registration/step2?fcode=MFIM2015&pid={PID}
 {domain}/ci/index.php/registration/step2?fcode=MFIM2015&pid={PID}&vt={TB,VISITOR,MEDIA}
 {domain}/ci/index.php/registration/step2?fcode=MFIM2015&pid={PID}&vt={TB,VISITOR,MEDIA}&allow=1    ---> bypass disable prereg for Operations
 {domain}/ci/index.php/registration/step2?fcode=MFIM2015&pid={PID}&vt={TB,VISITOR,MEDIA}&showall=1  ---> view all survey
 {domain}/ci/index.php/registration/willnotattend?fcode=mfio2015&pid={PID}                          ---> used for NOT ATTEND
 {domain}/ci/index.php/registration/reminder?fcode=mfio2015&pid={PID}                               ---> used for REMINDER 

 http://*************/event/registration/step2?app=1&vt=TB&fcode=MFIA2019&pid=<EMAIL>    ---> mobile app use during PREREG ver1

 ---> mobile app use during PREREG ver2 trade buyer
 http://*************/event/registration/step2?app=1&fcode=MFIO2019&pid=<EMAIL>&&pre={prefix}&fn={fname}&ln={lname}&num={cell}&co={coname}&cntry={country}&bus={business}

 ---> mobile app use during PREREG ver2 visitor
 http://*************/event/registration/step2?app=1&fcode=MFIO2019-VISITOR&pid=<EMAIL>&&pre={prefix}&fn={fname}&ln={lname}&num={cell}&co={coname}&cntry={country}&bus={business}

 ---> use in eblast or to skip steps in prereg
 http://localhost/event/registration/step2?pid={encrypt value of email only}   --> will proceed to http://localhost/event/registration/confirmreg?pid={}
 
 http://localhost/event/registration/step2?app=3&fcode=MFIO2020&pid=<EMAIL>

 will redirect to
 http://*************/event/registration/step2?pid={encrypt value}

 ******* IF used in VRS (include &vrs=1(less required fields) or $vrs=2(required fields) to indicate used by VRS)***************
 {domain}/ci/index.php/registration/step2?autoprint={yes,no}&fcode=MFIA2016&station={STAION NO}&vt={TB,VISITOR,MEDIA}&pid={PID}&vrs=1

 ************* if standalone ==================================================================================
 http://*************/ci/registration/step2?fcode=MFIA2019&station=1&vt=TB&venue=WTC&bcard=N&pid=&vrs=3
 or 
 http://*************/ci/registration/step2?fcode=MFIA2019    *** note view full screen in busmatch table must be enabled
 ==============================================================================================================
 
*/

/*
******* IMPORTANT mark '0' the EXCLUDE field of reference for profiles
*/
class registration extends CI_controller
{

  public function __construct()
  {
    parent::__construct();

    ini_set('memory_limit', '256M');

    $this->load->library(array('form_validation', 'session', 'encryption'));

    $this->load->helper(array('form', 'url', 'array', 'html', 'captcha'));
    $this->load->model('site_model');                // load site_model.php from models folder   
    $this->load->model('site_read_model');
    $this->load->model('master_model');

    $this->display_session();
  }

  public function display_session()
  {
      // Retrieve all session data
      $session_data = $this->session->all_userdata();

      // Convert session data to JSON format
      $session_json = json_encode($session_data);

      // Output the session data as a JavaScript console log
      //echo "<script>console.log('Session Data: ', $session_json);</script>";
  }

  function index()
  {

    //====== USE project name since fcode is encrpyted ===========
    //====== USE project name since fcode is encrpyted =========== 

    //vrs=2&fcode=MFIA2018&station=0&vt=TB&venue=WTC&bcard=N&pid= 

    // ======== to create encrpytp redirection values ==================================

    //$TMP1 = $this->encryption->encrypt('2#MFIO2024#0#GUEST#WTC#N#yes#2');    // $TMP1 = $this->encryption->encrypt('2#MFIO2024-GUEST#0#VISITOR#WTC#N#yes#2');
    //$TMP1 = $this->encryption->encrypt('2#MFIO2024#0#TB#WTC#N#yes#2');
    //$TMP1 = $this->encryption->encrypt('2#MFIO2024#0#GP#WTC#N#yes#2');
    //$TMP1 = $this->encryption->encrypt('2#MFIO2024#0#MEDIA#WTC#N#yes#2');
    //$TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);
    //die($TMP2);

    // ======== to create encrpytp redirection values ==================================

    redirect('registration/mfio');
  }

  function visitor()
  {

    redirect('registration/step2?qr=44ff2cbaa9de790e74b211de74be47e4917d099d85b80a7e2ea9fc8e9f22c526bb2d4d5c7fcbd7c153d041b5b7c9198c109291d396a13f5cb2cae2089efca6e36tExEXDiVPzj8tAGrOdqCW8qwFXjlJM5xAHoqU2xooz0d6XcnGJ6rhf1CXlmAlmH');
  }
  function govt()
  {

    redirect('registration/step2?qr=44ff2cbaa9de790e74b211de74be47e4917d099d85b80a7e2ea9fc8e9f22c526bb2d4d5c7fcbd7c153d041b5b7c9198c109291d396a13f5cb2cae2089efca6e36tExEXDiVPzj8tAGrOdqCW8qwFXjlJM5xAHoqU2xooz0d6XcnGJ6rhf1CXlmAlmH&gov=1');
  }
  function tb()
  {

    redirect('registration/step2?qr=160bc9655b795fc0df9641ce77987e83c132b0fa8e3d601923659b9a712733995b22ab64f6f8c41878891c293ac4a6bc7a2dc0c4e4dfa8155a3685cefe7fb9edMQIFndTZaTV4JFtwC0UCAL_sMYfSV3ULHQ1mDCX7P-4_wyFJ14jso4tRDknEoDro');
  }
  function gp()
  {

    redirect('registration/step2?qr=8665805a97b40bf597a27f74fa14409de54f13a00584b8c202d5be880dd2aa725162f14388f2f62cd6be9e02625b9bda259ab6d4a205affd351b536dbd7372868iTQUyVcpNxtDAfS0FRzgK5R0AHlmB24dISpvhgD56LqRDSBhTf5PT7Ddy_F3klQ');
  }
  function media()
  {

    redirect('registration/step2?qr=997dbd89ee72d85205037078aad0d1b6cbee922604ba1d3cc482fc960291bbbc14c2b148d90d46724ef588668bdc8eb4788e9417e82680c0a0a151c22bf0fcb70cn_Mo14z5IgtLJEsAs6KXGya7oto0aWiZ-wGQBbOgyjnWFrmrc-dcKUNgKOpIay');
  }

  // http://localhost/event/registration/mfi?vrs=compli&pcode=91TT3M

  public function sector() 
   {      //ONSITE()    //mfio()

	  //$rsFcode = $this->master_model->loadRec("busmatch_date","where sector='02' and active='1' and preselected_event='1' limit 1");

	    if(!isset($_GET['sec']) || $_GET['sec']=="") { die("no sect found... contact SMDD");}

	    $rsFcode = $this->master_model->loadRec("busmatch_date","where sector='".$_GET['sec']."' and active='1' and preselected_event='1' limit 1");  
	    if($rsFcode=="") { die("No active Project.. pls contact SMDD. tnx.");}

	    $pregClose =$this->check_status1($rsFcode[0]['disable_prereg'],0); // chek value of explode (0 - array[0] , 1 - array[1])

	    if($pregClose=='1') {

	    //$getCPage = $this->master_model->loadRec("v_reference","where switch ='PCLOSE' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
	      $getCPage = $this->master_model->loadRec("v_reference","where switch ='PCLOSE' and exclude=0 AND sector like '%".$_GET['sec']."%' order by sortfield LIMIT 1");
	      if($getCPage=="") {die("contact SMDD... project not defined2");}

	      $data['PageMessage'] = $getCPage[0]['header'].$getCPage[0]['content_message'].$getCPage[0]['footer'];     //$getCPage[0]['content_message'];    

	      $this->load->view('vrs_message', $data); // pre-registration CLOSE   
	      //echo $data['PageMessage'];
	    } else {

	        //$TMP1 = $this->encryption->encrypt($data['fcode']."~".$getpromocodes[$x]['promocode']);         step1
	        //$vGuest = $vtypes."~~~~".$vproject[0]['fair_code']."~".$promoCode."~verifiedEmail";             step2

	        $getCPage = $this->master_model->loadRec("v_reference","where switch ='REDIRECT1' and exclude=0 AND sector like '%".$_GET['sec']."%' order by sortfield LIMIT 1");
	        if($getCPage=="") {die("contact SMDD... reference not defined001");}

	        $Content = explode("#",trim($getCPage[0]['content_message'])); 

	        $getContent = trim($Content[3]);     // get url value 
	        $getvalue  = trim($Content[2]);      // get values 1 or 2 in content_message  (1 = vps url ; 2 = fameplus/ifex url )   

	        //die("xxx=".$getvalue." yyy= ".$getContent);      


	        if (isset($_GET['smd']) && $_GET['smd']=="1" )    // came from VRS(action_email_template) "PCODE2" 
	        {                                                 // http://localhost/event/registration/mfio?fcode={fcode1}&smd=1

	              // $getCPage = $this->master_model->loadRec("v_reference","where switch ='REDIRECT1' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
	              // if($getCPage=="") {die("contact SMDD... reference not defined001");}

	              // $Content = explode("#",trim($getCPage[0]['content_message'])); 
	              // $getContent = trim($Content[2]);

	              //https://fameplus.com/registration/visitor-registration?vtype=1&code=9CTBC35     

	              //http://localhost/event/registration/mfio?vrs=compli&pcode=9CTB79E (invite sticker)


	              $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['fcode']);
	              $getValue = $this->encryption->decrypt($decryptX); 
	              $ContentValue = explode("~",$getValue);                 //die($getValue);

	              $promocode = $ContentValue[1];
	              $faircode = $rsFcode[0]['fair_code'];
	              $vtype = (isset($ContentValue[2]) ? $ContentValue[2] : "GUEST");



	              if($rsFcode[0]['send_confirmation_email'] == "1" || $rsFcode[0]['send_email_verification'] == "1")         //send_email_verification
	              {
	                if($getvalue=="1") {

	                  redirect($getContent."registration/step1?fcode=".$_GET['fcode']);     // ."&vt=".$vtype

	                } else {

	                  redirect($getContent.$promocode);
	                }

	              }
	              else
	              {

	                $TMP1 = $this->encryption->encrypt($vtype."~~~~".$faircode."~".$promocode."~verifiedEmail~allowEmailInput");  // "~allowEmailInput" for onsite entry
	                $TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);                                       // disable readonly in registration_form_new, email field
	                            
	                if($getvalue=="1") {                  

	                  redirect($getContent."registration/step2?pid=".$TMP2);  

	                } else {
	                  redirect($getContent.$promocode);   
	                }  
	              }

	        }  
	        elseif (isset($_GET['pcode']) && $_GET['pcode']<>"") 
	        {

	              // $getCPage = $this->master_model->loadRec("v_reference","where switch ='REDIRECT1' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
	              // if($getCPage=="") {die("contact SMDD... reference not defined001");}

	              // $Content = explode("#",trim($getCPage[0]['content_message'])); 
	              // $getContent = trim($Content[2]);


	              //$TMP1 = $this->encryption->encrypt("2#".$rsFcode[0]["fair_code"]."-GUEST#0#VISITOR#WTC#N#yes#2");
	              $TMP1 = $this->encryption->encrypt("2#".$rsFcode[0]["fair_code"]."#0#GUEST#WTC#N#yes#2");
	              $TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);
	              //die($TMP2);

	              if($getvalue=="1") {        // get values 1 or 2 in content_message  (1 = vps url ; 2 = fameplus/ifex url )             

	                  redirect($getContent."registration/step2?pcode=".$_GET['pcode']."&qr=".$TMP2); 

	              } else {                    // get values 1 or 2 in content_message  (1 = vps url ; 2 = fameplus/ifex url )            
	                  redirect($getContent.$_GET['pcode']);   
	              }
	              

	              //$this->load->view('vrs_message', $data);

	        }  

	        else 

	        {     // ======================= ONSITE ========================================================================================= //


	              // ===== INCLUDE RIDERECTION TO FAMEPLUS =================
	              // ===== INCLUDE RIDERECTION TO FAMEPLUS =================

	              // $getCPage = $this->master_model->loadRec("v_reference","where switch ='REDIRECT1' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
	              // if($getCPage=="") {die("contact SMDD... reference not defined002");}

	              // $Content = explode("#",trim($getCPage[0]['content_message'])); 
	              // $getContent = trim($Content[4]);   
	              //die($getContent); 

	              if($getContent=="") {     //==== proceed to ONSITE landing page

	                $getCPage = $this->master_model->loadRec("v_reference","where switch ='ONSITE' and exclude=0 AND sector like '%".$_GET['sec']."%' order by sortfield LIMIT 1");
	                if($getCPage=="") {die("contact SMDD... reference not defined003");}

	                $content = $getCPage[0]['header'].$getCPage[0]['content_message'].$getCPage[0]['footer'];      // $getCPage[0]['content_message'];  
	                $xvalue = $rsFcode[0]['fair_code']."-GUEST";
	                $mess0 = str_replace("{gfcode}",$xvalue,$content);
	                 
	                $data['PageMessage'] = $mess0;

	                $this->load->view('vrs_message', $data); 

	              }  else {

	                $fcode = $this->encryption->encrypt($rsFcode[0]["fair_code"]);                  
	                $fcode = str_replace(array('+', '/', '='), array('-', '_', '^'), $fcode);

	                //redirect($getContent);
	                redirect($getContent.'registration/step1?fcode='.$fcode);

	              }

	        }

	    }    
   }

public function mfio_CANDELETE()
  {      //ONSITE()

	    $rsFcode = $this->master_model->loadRec("busmatch_date", "where sector='02' and active='1' and preselected_event='1' limit 1");
	    if ($rsFcode == "") {
	      die("No active Project.. pls contact SMDD. tnx.");
	    }

	    $pregClose = $this->check_status1($rsFcode[0]['disable_prereg'], 0); // chek value of explode (0 - array[0] , 1 - array[1])

	    if ($pregClose == '1') {

	      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='PCLOSE' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
	      if ($getCPage == "") {
	        die("contact SMDD... project not defined2");
	      }

	      $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message'];    

	      $this->load->view('vrs_message', $data); // pre-registration CLOSE   
	      //echo $data['PageMessage'];
	    } else {

	      //$TMP1 = $this->encryption->encrypt($data['fcode']."~".$getpromocodes[$x]['promocode']);         step1
	      //$vGuest = $vtypes."~~~~".$vproject[0]['fair_code']."~".$promoCode."~verifiedEmail";             step2

	      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='REDIRECT1' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
	      if ($getCPage == "") {
	        die("contact SMDD... reference not defined001");
	      }

	      $Content = explode("#", trim($getCPage[0]['content_message']));

	      $getContent = trim($Content[3]);     // get url value 
	      $getvalue  = trim($Content[2]);      // get values 1 or 2 in content_message  (1 = vps url ; 2 = fameplus/ifex url )   

	      //die("xxx=".$getvalue);      


	      if (isset($_GET['smd']) && $_GET['smd'] == "1")    // came from VRS(action_email_template) "PCODE2" 
	      {                                                 // http://localhost/event/registration/mfio?fcode={fcode1}&smd=1

	        // $getCPage = $this->master_model->loadRec("v_reference","where switch ='REDIRECT1' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
	        // if($getCPage=="") {die("contact SMDD... reference not defined001");}

	        // $Content = explode("#",trim($getCPage[0]['content_message'])); 
	        // $getContent = trim($Content[2]);

	        //https://fameplus.com/registration/visitor-registration?vtype=1&code=9CTBC35     

	        //http://localhost/event/registration/mfio?vrs=compli&pcode=9CTB79E (invite sticker)


	        $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['fcode']);
	        $getValue = $this->encryption->decrypt($decryptX);
	        $ContentValue = explode("~", $getValue);                 //die($getValue);

	        $promocode = $ContentValue[1];
	        $faircode = $rsFcode[0]['fair_code'];
	        $vtype = (isset($ContentValue[2]) ? $ContentValue[2] : "GUEST");



	        if ($rsFcode[0]['send_confirmation_email'] == "1" || $rsFcode[0]['send_email_verification'] == "1")         //send_email_verification
	        {
	          if ($getvalue == "1") {

	            redirect($getContent . "registration/step1?fcode=" . $_GET['fcode']);     // ."&vt=".$vtype

	          } else {

	            redirect($getContent . $promocode);
	          }
	        } else {

	          $TMP1 = $this->encryption->encrypt($vtype . "~~~~" . $faircode . "~" . $promocode . "~verifiedEmail~allowEmailInput");  // "~allowEmailInput" for onsite entry
	          $TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);                                       // disable readonly in registration_form_new, email field

	          if ($getvalue == "1") {

	            redirect($getContent . "registration/step2?pid=" . $TMP2);
	          } else {
	            redirect($getContent . $promocode);
	          }
	        }
	      } elseif (isset($_GET['pcode']) && $_GET['pcode'] <> "") {

	        // $getCPage = $this->master_model->loadRec("v_reference","where switch ='REDIRECT1' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
	        // if($getCPage=="") {die("contact SMDD... reference not defined001");}

	        // $Content = explode("#",trim($getCPage[0]['content_message'])); 
	        // $getContent = trim($Content[2]);


	        //$TMP1 = $this->encryption->encrypt("2#".$rsFcode[0]["fair_code"]."-GUEST#0#VISITOR#WTC#N#yes#2");
	        $TMP1 = $this->encryption->encrypt("2#" . $rsFcode[0]["fair_code"] . "#0#GUEST#WTC#N#yes#2");
	        $TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);
	        //die($TMP2);

	        if ($getvalue == "1") {

	          redirect($getContent . "registration/step2?pcode=" . $_GET['pcode'] . "&qr=" . $TMP2);
	        } else {
	          redirect($getContent . $_GET['pcode']);
	        }


	        //$this->load->view('vrs_message', $data);

	      } else {     // ======================= ONSITE ========================================================================================= //


	        // ===== INCLUDE RIDERECTION TO FAMEPLUS =================
	        // ===== INCLUDE RIDERECTION TO FAMEPLUS =================

	        // $getCPage = $this->master_model->loadRec("v_reference","where switch ='REDIRECT1' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
	        // if($getCPage=="") {die("contact SMDD... reference not defined002");}

	        // $Content = explode("#",trim($getCPage[0]['content_message'])); 
	        // $getContent = trim($Content[4]);   
	        //die($getContent); 

	        if ($getContent == "") {     //==== proceed to ONSITE landing page

	          $getCPage = $this->master_model->loadRec("v_reference", "where switch ='ONSITE' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
	          if ($getCPage == "") {
	            die("contact SMDD... reference not defined003");
	          }

	          $content = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];      // $getCPage[0]['content_message'];  
	          $xvalue = $rsFcode[0]['fair_code'] . "-GUEST";
	          $mess0 = str_replace("{gfcode}", $xvalue, $content);

	          $data['PageMessage'] = $mess0;

	          $this->load->view('vrs_message', $data);
	        } else {

	          $fcode = $this->encryption->encrypt($rsFcode[0]["fair_code"]);
	          $fcode = str_replace(array('+', '/', '='), array('-', '_', '^'), $fcode);

	          //redirect($getContent);
	          redirect($getContent . 'registration/step1?fcode=' . $fcode);
	        }
	      }
	    }
  }


public function ifex_()
  {      //ONSITE()

	    $rsFcode = $this->master_model->loadRec("busmatch_date", "where sector='01' and active='1' and preselected_event='1' limit 1");
	    if ($rsFcode == "") {
	      die("No active Project.. pls contact SMDD. tnx.");
	    }

	    $pregClose = $this->check_status1($rsFcode[0]['disable_prereg'], 0); // chek value of explode (0 - array[0] , 1 - array[1])

	    if ($pregClose == '1') {

	      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='PCLOSE' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");
	      if ($getCPage == "") {
	        die("contact SMDD... project not defined2");
	      }

	      $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message'];    

	      $this->load->view('vrs_message', $data); // pre-registration CLOSE   
	      //echo $data['PageMessage'];
	    } else {


	      if (isset($_GET['pcode']) && $_GET['pcode'] <> "") {

	        $getCPage = $this->master_model->loadRec("v_reference", "where switch ='REDIRECT1' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");
	        if ($getCPage == "") {
	          die("contact SMDD... reference not defined001");
	        }

	        $Content = explode("#", trim($getCPage[0]['content_message']));

	        $getContent = trim($Content[2]);

	        //die($getContent);
	        //$getContent = $getCPage[0]['content_message'];

	        //print_r($getContent); die();

	        //$fcode = $this->encryption->encrypt($rsFcode[0]["fair_code"]);                  
	        //$fcode = str_replace(array('+', '/', '='), array('-', '_', '^'), $fcode);

	        //redirect('https://fameplus.mediabrandsstudio.com/registration/visitor-registration?vtype=1&code='.$_GET['pcode']);
	        //redirect('https://fameplus.com/registration/visitor-registration?vtype=1&code='.$_GET['pcode']);


	        $TMP1 = $this->encryption->encrypt("2#" . $rsFcode[0]["fair_code"] . "-GUEST#0#VISITOR#WTC#N#yes#2");
	        $TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);
	        //die($TMP2);

	        redirect($getContent . "registration/step2?pcode=" . $_GET['pcode'] . "&qr=" . $TMP2);


	        //redirect('registration/step1?fcode='.$fcode."&pcode=".$_GET['pcode']);
	        //redirect('https://www.ifexphilippines.com/onsite/registration/step1?fcode='.$fcode."&pcode=".$_GET['pcode']);

	        //$this->load->view('vrs_message', $data);

	      } else {     // ======================= ONSITE ========================================================================================= //


	        // ===== INCLUDE RIDERECTION TO FAMEPLUS =================
	        // ===== INCLUDE RIDERECTION TO FAMEPLUS =================

	        $getCPage = $this->master_model->loadRec("v_reference", "where switch ='REDIRECT1' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");
	        if ($getCPage == "") {
	          die("contact SMDD... reference not defined002");
	        }

	        $Content = explode("#", trim($getCPage[0]['content_message']));
	        $getContent = trim($Content[4]);
	        //die($getContent); 

	        if ($getContent == "") {     //==== proceed to ONSITE landing page

	          $getCPage = $this->master_model->loadRec("v_reference", "where switch ='ONSITE' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");
	          if ($getCPage == "") {
	            die("contact SMDD... reference not defined003");
	          }

	          $content = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];      // $getCPage[0]['content_message'];  
	          $xvalue = $rsFcode[0]['fair_code'] . "-GUEST";
	          $mess0 = str_replace("{gfcode}", $xvalue, $content);

	          $data['PageMessage'] = $mess0;

	          $this->load->view('vrs_message', $data);
	        } else {

	          $fcode = $this->encryption->encrypt($rsFcode[0]["fair_code"]);
	          $fcode = str_replace(array('+', '/', '='), array('-', '_', '^'), $fcode);

	          //redirect($getContent);
	          redirect($getContent . 'registration/step1?fcode=' . $fcode);
	        }
	      }
	    }
  }


 public function ifexOnline()        // == ifexONLINE()
  {
	    $rsFcode = $this->master_model->loadRec("busmatch_date", "where sector='01' and active='1' and preselected_event='1' limit 1");
	    if ($rsFcode == "") {
	      die("No active Project.. pls contact SMDD. tnx.");
	    }


	    if (isset($_GET['pcode']) && $_GET['pcode'] <> "") {

	      $pregClose = '0';
	    } else {

	      $pregClose = $this->check_status1($rsFcode[0]['disable_prereg'], 0); // chek value of explode (0 - array[0] , 1 - array[1]) 
	    }

	    //die("aaa=".$pregClose);

	    if ($pregClose == '1') {

	      // ===== insert redirection if onsite registration gagamitin====== 

	      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='PCLOSE' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");
	      if ($getCPage == "") {
	        die("contact SMDD... project not defined2");
	      }

	      $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];
	    } else {

	      $fcode = $this->encryption->encrypt($rsFcode[0]["fair_code"]);
	      $fcode = str_replace(array('+', '/', '='), array('-', '_', '^'), $fcode);

	      if (isset($_GET['pcode']) && $_GET['pcode'] <> "") {
	        //die("aaa");
	        redirect('registration/step1?fcode=' . $fcode . "&pcode=" . $_GET['pcode']);

	        //redirect('https://www.ifexphilippines.com/onsite/registration/step1?fcode='.$fcode."&pcode=".$_GET['pcode']);

	      } else {
	        redirect('registration/step1?fcode=' . $fcode);
	      }
	    }

	    $this->load->view('vrs_message', $data); // pre-registration CLOSE

  }

  public function redirect()
  {

    $getCPage = $this->master_model->loadRec("v_reference", "where switch ='PAGE_REDIRECT1' and exclude=0 AND sector like '%02%' order by sortfield LIMIT 1");
    if ($getCPage == "") {
      die("contact SMDD... reference not defined003");
    }

    //$content = $getCPage[0]['content_message'];  

    //$xvalue = "xxx";
    //$mess0 = str_replace("{message}",$xvalue,$content);

    $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];

    $this->load->view('vrs_message', $data);
  }

  public function ifexONSITE2()
  {

    $rsFcode = $this->master_model->loadRec("busmatch_date", "where sector='01' and active='1' and preselected_event='1' limit 1");
    if ($rsFcode == "") {
      die("No active Project.. pls contact SMDD. tnx.");
    }

    $getCPage = $this->master_model->loadRec("v_reference", "where switch ='ONSITE' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");
    if ($getCPage == "") {
      die("contact SMDD... project not defined2");
    }

    $content = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];       // $getCPage[0]['content_message'];  

    $xvalue = $rsFcode[0]['fair_code'] . "-GUEST";

    $mess0 = str_replace("{gfcode}", $xvalue, $content);


    $data['PageMessage'] = $mess0;

    $this->load->view('vrs_message', $data);
  }

  public function ifex_PROMO()    // =================== check if with promocode and prog close
  {

    $rsFcode = $this->master_model->loadRec("busmatch_date", "where sector='01' and active='1' and preselected_event='1' limit 1");
    if ($rsFcode == "") {
      die("No active Project.. pls contact SMDD. tnx.");
    }


    if (isset($_GET['pcode']) && $_GET['pcode'] <> "") {

      $pregClose = '0';
    } else {

      $pregClose = $this->check_status1($rsFcode[0]['disable_prereg'], 0); // chek value of explode (0 - array[0] , 1 - array[1]) 
    }

    if ($pregClose == '1') {

      // ===== insert redirection if onsite registration gagamitin====== 


      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='PCLOSE' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");
      if ($getCPage == "") {
        die("contact SMDD... project not defined2");
      }

      $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     // $getCPage[0]['content_message'];    


    } else {

      $fcode = $this->encryption->encrypt($rsFcode[0]["fair_code"]);
      $fcode = str_replace(array('+', '/', '='), array('-', '_', '^'), $fcode);

      if (isset($_GET['pcode']) && $_GET['pcode'] <> "") {
        //die("aaa");
        redirect('registration/step1?fcode=' . $fcode . "&pcode=" . $_GET['pcode']);

        //redirect('https://www.ifexphilippines.com/onsite/registration/step1?fcode='.$fcode."&pcode=".$_GET['pcode']);

      } else {
        redirect('registration/step1?fcode=' . $fcode);
      }
    }

    $this->load->view('vrs_message', $data); // pre-registration CLOSE

  }

  //========== API for pointwest Mobile APP =============================================
  public function push()
  {
    //include request library
    include APPPATH . 'third_party/requests.php';
    //load internal classes
    Requests::register_autoloader();
    $url = 'https://us-central1-citem-9e3fa.cloudfunctions.net/api/CreateUser';
    $data = array(
      'email' => $this->input->post('email'),
      'password' => 'c1i5t3m7',
      'firstName' => $this->input->post('fname'),
      'lastName' => $this->input->post('lname'),
      'role' => "Visitor"
    );

    //set content type
    $headers = array('Content-Type' => 'application/json');
    //make a post request
    $response = Requests::post($url, $headers, json_encode($data));
    // if($response->success){
    //   // print_r($response->body);
    //   $temp2 = json_decode($response->body);
    //   // print_r($temp2) ;
    //   echo $temp2->msg;
    //   echo $temp2->email;
    //   // var_dump($response->headers['date']);
    // }
    // else{
    //   // print_r($response->body);
    //   $temp2 = json_decode($response->body);
    //   // print_r($temp2) ;
    //   echo $temp2->msg;
    //   echo $temp2->email;
    // }
  }
  //========== API for pointwest Mobile APP =============================================
  //=====================================================================================

  //=============================================================================================================================  
  // === http://localhost/event/registration/dtcpresult?pid=1022SMDIFEX2023SMDy ==== value will came from ifexconnect approve TB 
  //=============================================================================================================================    
  function dtcpresult()
  {      // IMPORTANT FAIRCODE IS NOT CORRECT IN GEETING CHILDTABLE

    if (SHOW_ERROR == FALSE) {
      //error_reporting(0);
      ini_set('error_reporting', 0);
      //ini_set('display_errors',ERR_DISPLAY); 
    }

    //$pidTMP = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['pid']);
    //$pid = $this->encryption->decrypt($pidTMP);  

    //die("zzz= ".$pid);

    $getPID = explode("SMD", $_GET['pid']);

    $pid = $getPID[0]; //die("aaa =".$pid);
    $data['fcode'] = ((isset($getPID[1])) ? $getPID[1] : "");

    $chkIfApproved = (strtoupper((isset($getPID[2])) ? $getPID[2] : ""));

    //print_r($getPID); die();    

    if (!is_numeric($pid)) {

      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='template' and exclude=0  order by sortfield LIMIT 1");
      if ($getCPage == "") {
        die("contact SMDD... no ref found (j3)");
      }

      $vtemp = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message']; 
      $pMessage = str_replace("{message}", "invalid value, please <NAME_EMAIL> (step0.1)", $vtemp);

      echo $pMessage;
      exit();

      //$this->endOfError("illegal value, please <NAME_EMAIL> (step0.1)");
      //die("illegal value, please <NAME_EMAIL> (step0.1)");
    }

    $data['step']  = "dtcpresult";

    //chk if fcode exists and get project details
    $data['project'] = $this->master_model->getRec("busmatch_date", "where active='1' and fair_code = ?", strtoupper($data['fcode']), "");
    if ($data['project'] == '') {

      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='template' and exclude=0  order by sortfield LIMIT 1");
      if ($getCPage == "") {
        die("contact SMDD... no ref found (j4)");
      }

      $vtemp = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message']; 
      $pMessage = str_replace("{message}", "Problem Encountered(err01-nobusmach).... pls report to SMDD", $vtemp);

      echo $pMessage;
      exit();

      //$this->endOfError("Problem Encountered.... pls report to SMDD");  
      //die("Under Construction.....");

    }

    //================== LOAD values in Profile if to be shown/required =================================
    $data['chkShowFields'] = $this->loadShowValue($data['project']); //die("aaa= ".$data['chkShowFields']['show_company_name']); print_r($zzz); 
    //===================================================================================================

    if ($data['chkShowFields']['disable_prereg'] == "1") {

      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='PCLOSE' and exclude=0 AND sector like '%" . $data['project'][0]['sector'] . "%' order by sortfield LIMIT 1");

      if ($getCPage == "") {
        die("contact SMDD... no ref found (j5)");
      }

      $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message'];    

      //$this->load->view('vrs_message', $data); // pre-registration CLOSE        
      echo $data['PageMessage'];
      exit();
    }

    $data['vsector'] = $data['project'][0]['sector'];

    //die("sss");



    $data['profiles'] = $this->site_read_model->joinTable("v_dtcp_id as A", "v_contact_profile as B", "", "A.rep_code = B.rep_code", "A.fair_code='" . $data['project'][0]['event'] . "' and A.ref_id='" . $pid . "' limit 1", "inner");

    if ($data['profiles'] == "") {

      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='template' and exclude=0  order by sortfield LIMIT 1");
      if ($getCPage == "") {
        die("contact SMDD... no ref found (j6)");
      }

      $vtemp = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message']; 
      $pMessage = str_replace("{message}", "Problem Encountered(err02-profile).... pls report to SMDD", $vtemp);

      echo $pMessage;
      exit();
    }

    if ($chkIfApproved == "Y") {

      $xresult = $this->execSendProc($data['project'], $data['profiles'], "TRADE BUYER", "CMESS1", "CPAGE1", "processEmail", "", "");

      $DTCPfaircode = $data['project'][0]['url_busmatch'];
      $repcode = $data['profiles'][0]['rep_code'];
      $barcodeValue = $data['profiles'][0]['barcode'];

      $getVisitorStatus = $this->site_model->loadRec("v_attendance", "where fair_code like '" . $DTCPfaircode . "%' and rep_code='" . $repcode . "' ORDER BY fair_code DESC limit 1");

      //==== undelete if email is entered again in prereg site ====================================
      $this->site_model->updateRecord('v_contact_profile', 'deleted', "0", "rep_code = " . $repcode);
      //===========================================================================================
      $this->site_model->updateRecord('v_contact_profile', 'visitor_type', "TRADE BUYER", "rep_code = " . $repcode);
      $this->site_model->updateRecord('v_contact_profile', 'buyer_type', $getVisitorStatus[0]['visitor_status'], "rep_code = " . $repcode);
      $this->site_model->updateRecord('v_contact_profile', 'reg_status', "F", "rep_code = " . $repcode);
      $this->site_model->updateRecord('v_contact_profile', 'pre_reg', 'P', "rep_code = " . $repcode);
      $this->site_model->updateRecord('v_contact_profile', 'date_input', date('Y-m-d H:i:s'), "rep_code = " . $repcode);

      $data['chkAttendance'] = $this->site_read_model->getRec("v_attendance", "where fair_code='" . $data['fcode'] . "' and rep_code = ? limit 1", $repcode, "");
      if ($data['chkAttendance'] == "") {
        $this->site_model->insertRecTable('v_attendance', $repcode, $barcodeValue, '', '', strtoupper($data['fcode']), $data['vsector']);
        $this->site_model->updateRecTable('v_attendance', 'date_apply', date('Y-m-d H:i:s'), strtoupper($data['fcode']), $data['vsector'], $repcode);
        $updateAttendance = "1";
      }

      $this->site_model->updateRecTable('v_attendance', 'visitor_type', 'TRADE BUYER', strtoupper($data['fcode']), $data['vsector'], $repcode);
      $this->site_model->updateRecTable('v_attendance', 'visitor_status', $getVisitorStatus[0]['visitor_status'], strtoupper($data['fcode']), $data['vsector'], $repcode);
      $this->site_model->updateRecTable('v_attendance', 'reg_status', 'F', strtoupper($data['fcode']), $data['vsector'], $repcode);
      $this->site_model->updateRecTable('v_attendance', 'pre_reg', 'P', strtoupper($data['fcode']), $data['vsector'], $repcode);
      $this->site_model->updateRecTable('v_attendance', 'date_input', date('Y-m-d H:i:s'), strtoupper($data['fcode']), $data['vsector'], $repcode);
      $this->site_model->updateRecTable('v_attendance', 'user_agreement', 'yes', strtoupper($data['fcode']), $data['vsector'], $repcode);
      $this->site_model->updateRecTable('v_attendance', 'pre_arranged_meeting', $getVisitorStatus[0]['pre_arranged_meeting'], strtoupper($data['fcode']), $data['vsector'], $repcode);
      $this->site_model->updateRecTable('v_attendance', 'interpreter', $getVisitorStatus[0]['interpreter'], strtoupper($data['fcode']), $data['vsector'], $repcode);

      $this->dtcpUpdatechild("v_genproducts", $DTCPfaircode, $data['fcode'], $repcode, $barcodeValue, $data['vsector']);
      $this->dtcpUpdatechild("v_job_function", $DTCPfaircode, $data['fcode'], $repcode, $barcodeValue, $data['vsector']);
      $this->dtcpUpdatechild("v_representation", $DTCPfaircode, $data['fcode'], $repcode, $barcodeValue, $data['vsector']);
      $this->dtcpUpdatechild("v_mn_annualsales", $DTCPfaircode, $data['fcode'], $repcode, $barcodeValue, $data['vsector']);
      $this->dtcpUpdatechild("v_existing_arrangement", $DTCPfaircode, $data['fcode'], $repcode, $barcodeValue, $data['vsector']);
    } else {

      $xresult = $this->execSendProc($data['project'], $data['profiles'], "", "", "CPAGE3", "getVtype", "", "");     //=== Landing Page Disapproved/Deactivated
    }


    echo $xresult;
    exit();
  }

  function dtcpUpdatechild($vtable, $vDTCPfaircode, $vfcode, $rcode, $vbcode, $vsec)
  {      // VFCODE must be the DTCP fcode 


    //$getData = $this->site_read_model->getRec($vtable,"where fair_code like '".$vDTCPfaircode."%'  AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC",$rcode,$vsec);
    $getData = $this->site_read_model->getRec($vtable, "where fair_code like '" . $vDTCPfaircode . "%'  AND rep_code = ? ", $rcode, $vsec);

    //print_r($getData);

    if ($getData <> "") {
      //$numRec = count($getData);
      //die("<br>xxx=".$numRec);
      $delete1 = $this->site_model->deleteRecTable($vtable, $rcode, $vfcode, $vsec);

      foreach ($getData as $rs) {
        if ($vtable == "v_genproducts") {

          $insert = $this->site_model->insertProdTable($vtable, $rcode, $vbcode, $rs['prod_cat'], $rs['prod_code'], $rs['remarks_major'], $rs['remarks_minor'], $vfcode, $vsec);
        } else {

          $insert = $this->site_model->insertRecTable($vtable, $rcode, $vbcode, $rs['item_code'], $rs['remarks'], $vfcode, $vsec);
        }
      }
    }
  }


  // ================= step0 ===========================================================================
  // === http://localhost/event/registration/step0?pid={value} ==== 
  // === ex. value <EMAIL>~~~~IFEX2023
  // === from step1 if "Join as a Visistor" was pressed in Template "Landing Page Deleted/Incomplete" ============
  function step0()
  {

    if (SHOW_ERROR == FALSE) {
      //error_reporting(0);
      ini_set('error_reporting', 0);
      //ini_set('display_errors',ERR_DISPLAY); 
    }

    $pidTMP = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['pid']);
    $pid = $this->encryption->decrypt($pidTMP);

    //die("zzz= ".$pid);

    $getPID = explode("~", $pid);
    $pid = $getPID[0]; //die("aaa =".$pid);

    $data['fcode'] = ((isset($getPID[4])) ? $getPID[4] : "");

    //print_r($getPID); die();

    // ==== check if email value instead PID =============================================================
    // ==== check if email value instead PID =============================================================
    $isemail = "0";
    $vemail = filter_var($pid, FILTER_SANITIZE_EMAIL);        // Remove all illegal characters from email
    if (filter_var($vemail, FILTER_VALIDATE_EMAIL)) {         // Validate e-mail

      $isemail = "1";
    }
    // ==== end check if email value instead PID ========================================================
    // ==== end check if email value instead PID ========================================================

    //if ($isemail=="0") {
    if ($isemail == "0" && !ctype_alnum($pid)) {
      die("illegal value, please <NAME_EMAIL> (step0)");
    }
    //}   


    $data['step']  = "step0";

    //chk if fcode exists and get project details
    $data['project'] = $this->master_model->getRec("busmatch_date", "where active='1' and fair_code = ?", strtoupper($data['fcode']), "");
    if ($data['project'] == '') {

      $this->endOfError("Problem Encountered.... pls report to SMDD");
      die("Under Construction.....");
    }
    $data['vsector'] = $data['project'][0]['sector'];

    //die("sss");
    //================== LOAD values in Profile if to be shown/required =================================
    $data['chkShowFields'] = $this->loadShowValue($data['project']); //die("aaa= ".$data['chkShowFields']['show_company_name']); print_r($zzz); 
    //===================================================================================================


    // load profiles
    if ($isemail == "1") {
      $data['profiles'] = $this->site_read_model->getRec("v_contact_profile", "where email = ?", $pid, "", "GUEST");
      $xresult = $this->execSendProc($data['project'], $vemail, "", "", "CPAGE3", "newRecord", "");              //=== Landing Page Disapproved/Deactivated      

    } else {
      $data['profiles'] = $this->site_read_model->getRec("v_contact_profile", "where pid = ?", $pid, "");
      $xresult = $this->execSendProc($data['project'], $data['profiles'], "", "", "CPAGE3", "getVtype", "", "GUEST");     //=== Landing Page Disapproved/Deactivated
      if ($data['profiles'] == "") {
        die("profile issue, contact SMDD.....");
      }
    }


    if ($data['chkShowFields']['disable_prereg'] == "1") {

      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='PCLOSE' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");

      if ($getCPage == "") {
        die("contact SMDD... no ref found (j2)");
      }

      $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message'];    

      $this->load->view('vrs_message', $data); // pre-registration CLOSE  

    } else {
      echo $xresult;
      exit();
    } // $pregClose     
  }
  // ================= step0 ============================================================================



  // /registration/step1?fcode={encrypted(fcode)}    // function ifex()
  function step1()
  {

    if (SHOW_ERROR == FALSE) {
      //error_reporting(0);
      ini_set('error_reporting', 0);
      //ini_set('display_errors',ERR_DISPLAY); 
    }

    // $this->load->model('site_model');                // load site_model.php from models folder  	
    // $this->load->model('site_read_model');
    // $this->load->model('master_model');

    $data['controller_CI'] = "registration";


    $data['allow'] = (isset($_GET['allow']) ? $_GET['allow'] : $this->input->post('allow'));    // ==== used for opereations marketing officers

    //============= chk if PREREG so date_apply field can be UPDATED =====================================================================
    $data['updateIfPrereg'] = (isset($_GET['preg']) ? $_GET['preg'] : (isset($_POST['updateIfPrereg']) ? $_POST['updateIfPrereg'] : ""));
    //====================================================================================================================================

    //======== for Refer.php ========================se
    $data['pticRefer'] = ""; // for use in Refer.php
    $data['pticReferID'] = "";
    //===============================================

    //======== function dynamic_fields() at buyreg.php =========
    $data['orders_placed_last_visit_data'] = null;
    //=========================================================


    if (!isset($_GET['vt'])) {
      $data['vtype'] = $this->input->post('visitorType');
    }
    if (!isset($data['vtype'])) {
      $data['vtype'] = 'TRADE BUYER';
    }

    // faircode required
    if (isset($_GET['fcode'])) {
      $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['fcode']);

      $decryptX = $this->encryption->decrypt($decryptX);    //die($decryptX." ***= 839");

      //$data['fcode']= $decryptX;

      $getValues = explode("~", $decryptX);

      $data['fcode'] = $getValues[0];

      $_GET['pcode'] = (isset($getValues[1]) ? $getValues[1] : "");    //die($_GET['pcode']);

      $data['vtype'] = (isset($getValues[2]) && $getValues[2] <> "" ? $getValues[2] : "GUEST");

      //========= remove after test ========
      //========= remove after test ========
      //$data['fcode']= $_GET['fcode'];
      //========= remove after test ========
      //========= remove after test ========


    } else {
      $data['fcode'] = $this->input->post('fcode');
    }

    //die($decryptX); 

    $faircode = strtoupper($data['fcode']);
    if (strpos($faircode, '-VISITOR') !== false) {
      $faircode = trim(str_replace("-VISITOR", "", $faircode));
      $data['vtype'] = "VISITOR";
    }
    if (strpos($faircode, '-GUEST') !== false) {
      $faircode = trim(str_replace("-GUEST", "", $faircode));
      $data['vtype'] = "GUEST";
    }
    if (strpos($faircode, '-MEDIA') !== false) {
      $faircode = trim(str_replace("-MEDIA", "", $faircode));
      $data['vtype'] = "MEDIA";
    }
    if (strpos($faircode, '-GP') !== false) {
      $faircode = trim(str_replace("-GP", "", $faircode));
      $data['vtype'] = "GENERAL PUBLIC";
    }
    if (strpos($faircode, '-VIB') !== false) {
      $faircode = trim(str_replace("-VIB", "", $faircode));
      $data['vtype'] = "VIB";
    }


    // chk if PID is declared
    if (isset($_GET['pid'])) {
      $pid = $_GET['pid'];
    } else {
      $pid = "0";
    }

    //====== required in viewing profile if regular registered ======
    $data['showall'] = "";
    //===============================================================


    $data['base'] = $this->config->item('base_url'); // get base_url from config.php
    $data['main_page'] = $this->config->item('index_page'); // get index_page from config.php
    $data['css']  = $this->config->item('css');      // load mystyles.css defined in config.php 

    $data['step']    = "step1";


    $data['project'] = $this->master_model->getRec("busmatch_date", "where active='1' and fair_code = ?", strtoupper($data['fcode']), "");

    if ($data['project'] == '') {

      $this->endOfError("Problem Encounteredz.... pls report to SMDD");
      die("Under Construction.....");
    }


    $pregClose = $this->check_status1($data['project'][0]['disable_prereg'], 0);    // chek value of explode (0 - array[0] , 1 - array[1]) 


    // ==== check if promo code was passed in vrs.php (createPromo func) ================================================
    $data['pcode'] = (isset($_GET['pcode']) ? $_GET['pcode'] : (isset($_POST['pcode']) ? $_POST['pcode'] : ""));
    // ==================================================================================================================

    // ==== check if voucher code was used na ===========================================================================
    // ==================================================================================================================    
    if ($data['pcode'] <> "") {

      $pregClose = '0';

      $rsChkifAvailable = $this->site_read_model->getRec("v_voucher_code", "where item_code = ?", $data['pcode'], "");



      if ($rsChkifAvailable == "") {

        $xresult = $this->execSendProc($data['project'], $data['pcode'], "", "", "template", "pcodeInvalid", "", $data['vtype']);
        echo $xresult;
        exit();
      }


      if ($rsChkifAvailable[0]['rep_code'] <> 0) {

        $xresult = $this->execSendProc($data['project'], $data['pcode'], "", "", "template", "pcodeUsed", "", $data['vtype']);
        echo $xresult;
        exit();
      }
    }
    // ==================================================================================================================

    //die("aaa=".$data['pcode'] );



    if ($pregClose == '1') {

      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='PCLOSE' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");
      if ($getCPage == "") {
        die("contact SMDD... no ref found (j1)");
      }

      $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message'];    

      // proceed to view('vrs_message', $data);
    } else {  //die("bbb=".$data['pcode'] );

      //================== LOAD values in Profile if to be shown/required =================================
      $data['chkShowFields'] = $this->loadShowValue($data['project']); //die("aaa= ".$data['chkShowFields']['show_company_name']); print_r($zzz); 
      //===================================================================================================

      $countMod = 0;
      foreach ($data['project'] as $proj1) {
        //$pregClose =$this->check_status1($proj1['disable_prereg'],0); // chek value of explode (0 - array[0] , 1 - array[1]) 
        $data['vsector'] = $proj1['sector'];
        $data['ccEmail']  = $proj1['emailto'];
        $data['vEvent'] = $proj1['event'];
        $data['eventDTCP'] = $proj1['url_busmatch'];
        $data['bannerpic'] = $proj1['bannerpic'];
        $data['footerpic'] = $proj1['footerpic'];
        $data['event'] = $proj1['event'];
      }


      // if ($data['vtype']=='TRADE BUYER')  
      //   { // chk number of steps for image in buyreg.php
      //      //======= get how many steps should the preregistration should proceed =================
      //      //$data['numModule'] = $this->check_status1($proj1['disable_prereg'],1);   // chek value of explode (0 - array[0] , 1 - array[1])
      //      $data['number_of_page_in_form'] = $this->check_status1($proj1['disable_prereg'],0);

      //      //======================================================================================
      //   }
      // else
      //   {	 //$data['numModule'] = 1;   // use this if VISITOR
      // 	   $data['numModule'] = $this->check_status1($proj1['disable_prereg'],1);   // chek value of explode (0 - array[0] , 1 - array[1])
      //   }


      switch ($data['vtype']) {
        case "TB":
        case "TRADE BUYER":
          $data['numModule'] = $this->check_status1($proj1['number_of_page_in_form'], 0);
          break;
        case "VISITOR":
        case "GUEST":
          $data['numModule'] = $this->check_status1($proj1['number_of_page_in_form'], 1);
          break;
        case "MEDIA":
          $data['numModule'] = $this->check_status1($proj1['number_of_page_in_form'], 2);
          break;
        case "GP":
        case "GENERAL PUBLIC":
          $data['numModule'] = $this->check_status1($proj1['number_of_page_in_form'], 3);
          break;
      }


      // load profiles
      $data['profiles'] = $this->site_read_model->getRec("v_contact_profile", "where pid = ?", $pid, "");

      $rcode = isset($data['profiles'][0]['rep_code']) ? $data['profiles'][0]['rep_code'] : "";


      $data['typeAttendance'] = $this->site_read_model->getRec("v_attendance", "where fair_code='" . $faircode . "' and sector='" . $data['vsector'] . "' and rep_code = ? limit 1", $rcode, '');

      $this->form_validation->set_error_delimiters('<div style="background-color:#FF6666">', '</div>'); //globally change the error delimiters   


      $data['stepMsg'] =  1;
      $data['tempCount'] = 1;
      //$data['stepMsg'] =  1;

      //if ($data['vtype']=='TRADE BUYER')  {$data['stepImg'] = "step1.jpg";}
      //else {$data['stepImg'] = "step1_visitor.jpg";}

      $data['dSalutation'] = $this->master_model->loadRec("v_reference", "where switch ='MN9' and exclude='0' and sector like '%" . $data['vsector'] . "%' order by sortfield,c_profile");


      // ===================== VALIDATION ==================================================================
      $this->form_validation->set_rules('captcha', "Captcha", 'required');
      $this->form_validation->set_rules('email', 'Email', 'required|valid_email');


      //load country
      //$data['ctry']= $this->master_model->loadRec("v_reference","where switch ='R1' order by c_profile");
      $data['vcountry']  = "";

      if ($data['chkShowFields']['show_country'] == "1") {

        //$this->form_validation->set_rules('country', 'Country', 'required');
      }
      // ===================================================================================================

      $data['vPassword'] = "F";

      if ($data['vPassword'] == "T") {
        $rules = array(
          [
            'field' => 'password',
            'label' => 'Password',
            'rules' => 'callback_valid_password',
          ],
          [
            'field' => 'repeat_password',
            'label' => 'Repeat Password',
            'rules' => 'matches[password]',
          ],
        );
        $this->form_validation->set_rules($rules);
      }

      //$this->form_validation->set_rules('password', 'Password', 'required');

      // === for DTCP =====================================================================================
      // if($data['vEvent']=="DTCP") {
      //   //$data['vtype']='VISITOR';
      //   $data['vtype']='GUEST';
      //   $this->form_validation->set_rules('fname', 'First Name', 'required');
      //   $this->form_validation->set_rules('lname', 'Last Name', 'required');
      //   $this->form_validation->set_rules('coname', 'Company/Organization', 'required');
      //   $this->form_validation->set_rules('title1', 'Position/Designation', 'required');
      // }
      // ==================================================================================================

      /* Get the user's entered captcha value from the form */
      $userCaptcha = $this->input->post('captcha');

      // ==== REMOVE if  SEESION is working na ============================================================

      $data['regis'] = $this->input->post('regis');
      if ($data['regis'] <> "") {
        $decryptX = $this->encryption->decrypt($data['regis']);
      } else {
        $decryptX = "";
      }

      // ==================================================================================================


    } // $pregClose == '1')

    if ($pregClose == "1") {

      $this->load->view('vrs_message', $data);   // pre-registration CLOSE
    } else {

      //if ($this->form_validation->run() == FALSE || $isHuman <> 1)  // runs the validation routine $data['msg']=='Mismatch, try again'
      $data['captchaValidationMessage'] = '';

      // ================= ORIG ====================================================================================================================
      //if ($this->form_validation->run() == FALSE || strcmp(strtolower($userCaptcha), strtolower($this->session->userdata('captchaWord'))) <> 0)
      // ==== REMOVE if  SEESION is working na =================================================================================================== 
      if ($this->form_validation->run() == FALSE || strcmp(strtolower($userCaptcha), strtolower($decryptX)) <> 0)
      // ========================================================================================================================================= 
      {

        // ==== REMOVE if  SEESION is working na ===================================================================== 
        if (strcmp(strtolower($userCaptcha), strtolower($decryptX)) <> 0 && $userCaptcha <> "")
        // ===========================================================================================================
        // ============== ORIG ==============================================================================================  
        //if(strcmp(strtolower($userCaptcha), strtolower($this->session->userdata('captchaWord')))  <> 0 && $userCaptcha <> "")
        // ===========================================================================================================  

        {
          $data['captchaValidationMessage'] = 'Entry does not match';
        }

        /** Validation was not successful - Generate a captcha **/
        /* Setup vals to pass into the create_captcha function */

        //$set_data = $this->session->all_userdata();
        //$data['xxx'] = $set_data['captchaWord']['word']; 

        $data['captcha1'] = $this->_generateCaptcha();     // $captcha

        // ==== REMOVE if  SEESION is working na ======================
        $data['regis'] = $this->encryption->encrypt($data['captcha1']['word']);
        //=============================================================

        //print_r($data['captcha1']); //die();
        //echo "<br><br>a= ".$userCaptcha;
        // ============== ORIG ============================================================================================== 
        //echo "<br><br>b= ".$this->session->userdata('captchaWord');
        // =======================================================================================================
        //echo "<br><br>c= ".$decryptX;

        /* Store the captcha value (or 'word') in a session to retrieve later */
        //======================= ORIG ==========================================
        //$this->session->set_userdata('captchaWord', $data['captcha1']['word']);
        //=======================================================================

        $this->load->view('vrs_form_template_content', $data);      // STEP 1       registration_form_new
      } else {

        /** Validation was successful; show the Success view **/
        /* Clear the session variable */
        $this->session->unset_userdata('captchaWord');

        $vsend = '';
        $pid = trim($this->input->post('pid'));

        //======== check if email exists ========================================
        //$data['vemail'] = trim($this->input->post('email2a'));
        $data['vemail'] = trim($this->input->post('email'));

        // =============== FOR IFEX ======================================================================================================================
        // =============== CHECK EMAIL if APPROVED TRADE, INCOMPLETE, DISAPPROVED, DELETED (from "validator_status" in attendance) =======================
        //================================================================================================================================================ 


        if ($data['chkShowFields']['send_confirmation_email'] == "0") {

          //$data['profiles2']= $this->site_read_model->getRec("v_contact_profile","where email<>'' and email = ?",$data['vemail'],"");

          $data['profiles2'] = $this->site_read_model->joinTable("v_contact_profile as A", "v_attendance as B", "", "A.rep_code = B.rep_code", "A.email='" . $data['vemail'] . "' and B.fair_code LIKE '" . $data['eventDTCP'] . "%' limit 1", "inner");
        } else {
          // --------------  change to check status in "validator_status" in attendance --------------------
          // --------------  change to check status in "validator_status" in attendance --------------------

          $data['profiles2'] = $this->site_read_model->joinTable("v_contact_profile as A", "v_attendance as B", "", "A.rep_code = B.rep_code", "A.email='" . $data['vemail'] . "' and B.fair_code LIKE '" . $data['eventDTCP'] . "%' limit 1", "inner");
        }
        //================================================================================================================================================
        //================================================================================================================================================        


        //=== check if "Send confirmation Email" and "Verify Email" in bussmatch table ===
        //=== check if "Send confirmation Email" and "Verify Email" in bussmatch table ===
        if ($data['chkShowFields']['send_email_verification'] == "1") {    //die("zzz");

          $emailTemplate1   = "VMESS1";
          $confirmationPage = "CPAGE0";    //"STEP1";

          //if( $data['pcode'] <> "")  {}

          $xresult = $this->execSendProc($data['project'], $data['profiles2'], $_POST['email'], $emailTemplate1, $confirmationPage, "verifyemail", $data['pcode'], $data['vtype']);
          echo $xresult;
          exit();
        }
        //=======================================================


        if ($data['profiles2'] <> "") {
          //die("xxx");

          if (isset($_POST['pcode']) && $_POST['pcode'] <> "") {

            $vGuest = $_POST['email'] . "~" . $_POST['pcode'] . "~~~" . $_POST['fcode'] . "-GUEST";
            $TMP1 = $this->encryption->encrypt($vGuest);
            $TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);

            //die("aaa11=".$vGuest);


            redirect('registration/step2?pid=' . $TMP2 . "&rel=&app=5");
          }


          foreach ($data['profiles2'] as $prof2) {
            $pid     = $prof2['pid'];
            $repcode = $prof2['rep_code'];
            $barcodeValue = $prof2['barcode'];
            $registeredEmail = $prof2['email'];

            $preregister = ($prof2['pre_reg'] == "P" ? "P" : "INC");

            $cont_per = trim($prof2['cont_per_fn']) . " " . trim($prof2['cont_per_ln']);

            $coname = (trim($prof2['co_name']) == "" ? $this->input->post('coname') : $prof2['co_name']);
            $fname  = (trim($prof2['cont_per_fn']) == "" ? $this->input->post('fname') : $prof2['cont_per_fn']);
            $lname  = (trim($prof2['cont_per_ln']) == "" ? $this->input->post('lname') : $prof2['cont_per_ln']);
            $title1 = (trim($prof2['position']) == "" ? $this->input->post('title1') : $prof2['position']);
            $ctry   = (trim($prof2['country']) == "" ? $this->input->post('country') : $prof2['country']);

            //die("zzz=".$prof2['validation_status']);

            // ===========================================================================

            $sendConfirmationEmail = "0";
            if ($data['chkShowFields']['send_confirmation_email'] == "1") {

              $confirmationPage = "CPAGE1";
              $preregister = "P";                           //die($prof2['validation_status']);

              switch ($prof2['validation_status']) {        // $data['profiles2'][0]['validation_status']

                case "APPROVED TRADE":

                  //die("ssss");
                  $emailTemplate1 = "CMESS1";
                  $sendConfirmationEmail = "1";

                  $xresult = $this->execSendProc($data['project'], $data['profiles2'], $data['vtype'], $emailTemplate1, $confirmationPage, "processEmail", "", "TB");
                  break;
                case "PENDING TRADE":
                case "REVIEWED TRADE":
                case "WAITLISTED TRADE":

                  $emailTemplate1 = "";
                  $confirmationPage = "CPAGE2";
                  $sendConfirmationEmail = "1";

                  $xresult = $this->execSendProc($data['project'], $data['profiles2'], $data['vtype'], $emailTemplate1, $confirmationPage, "doNotProcessEmail", "", "TB");
                  echo $xresult;
                  exit();

                  break;
                case "DISAPPROVED TRADE":
                case "DEACTIVATED TRADE":

                  $emailTemplate1 = "";
                  $confirmationPage = "CPAGE3";

                  //$vComp = $pid."~~~~".$data['fcode']."-GUEST";
                  //$TMP1 = $this->encryption->encrypt($vComp);
                  //$TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);

                  $xresult = $this->execSendProc($data['project'], $data['profiles2'], $data['vtype'], $emailTemplate1, $confirmationPage, "getVtype", "", "GUEST");

                  echo $xresult;
                  exit();

                  break;

                default:                           //if validation_status is  "DELETED TRADE" , "INCOMPLETE TRADE" or no value/invalid value
                  $emailTemplate1 = "";
                  $confirmationPage = "CPAGE4";   //die($xresult);

                  $xresult = $this->execSendProc($data['project'], $data['profiles2'], $data['vtype'], $emailTemplate1, $confirmationPage, "regAsVisitor", "", "GUEST");
                  echo $xresult;
                  exit();

                  //  break; 
                  //default:
                  //  $emailTemplate1 = "";

              } //die($xresult);                    

            }  // === if($data['chkShowFields']['send_confirmation_email'] == "1")

            // ===========================================================================
            //break; // exit foreach loop

          } // === end foreach($data['profiles2'] as $prof2) 

          //die("xx = ".$repcode);


          //===== if CREATEPHILS ==============
          if ($data['vsector'] == "20") {
            $this->site_model->updateField("v_contact_profile", "country", $_POST['country'], "rep_code=" . $repcode);
          }
          //===================================

          //==== undelete if email is entered again in prereg site ====================================
          $this->site_model->updateRecord('v_contact_profile', 'deleted', "0", "rep_code = " . $repcode);
          //===========================================================================================
          $this->site_model->updateRecord('v_contact_profile', 'reg_status', "F", "rep_code = " . $repcode);
          $this->site_model->updateRecord('v_contact_profile', 'pre_reg', $preregister, "rep_code = " . $repcode);
          $this->site_model->updateRecord('v_contact_profile', 'date_input', date('Y-m-d H:i:s'), "rep_code = " . $repcode);

          if ($sendConfirmationEmail == "0") {
            $this->site_model->updateRecord('v_contact_profile', 'visitor_type', $data['vtype'], "rep_code = " . $repcode);
            $this->site_model->updateRecord('v_contact_profile', 'country', $ctry, "rep_code = " . $repcode);
            $this->site_model->updateRecord('v_contact_profile', 'continent', $this->site_read_model->getContinent($ctry), "rep_code = " . $repcode);
            $this->site_model->updateRecord('v_contact_profile', 'cont_per_fn', $fname, "rep_code = " . $repcode);
            $this->site_model->updateRecord('v_contact_profile', 'cont_per_ln', $lname, "rep_code = " . $repcode);
            $this->site_model->updateRecord('v_contact_profile', 'position', $title1, "rep_code = " . $repcode);
            $this->site_model->updateRecord('v_contact_profile', 'co_name', $coname, "rep_code = " . $repcode);
            //$this->site_model->updateRecord('v_contact_profile','date_apply',date('Y-m-d H:i:s'),"rep_code = ".$repcode);            
            $this->site_model->updateRecord('v_contact_profile', 'add_value', $this->input->post('password'), "rep_code = " . $repcode);
          }

          //========= check if event name exist in attandance table ========
          //=================================================================
          $data['chkSectorName'] = $this->site_read_model->getRec("v_attendance", "where fair_code='" . $data['event'] . "' and rep_code = ? limit 1", $repcode, "");
          if ($data['chkSectorName'] == "") {
            $this->site_model->insertRecTable('v_attendance', $repcode, $barcodeValue, '', '', strtoupper($data['event']), $data['vsector']);
            $this->site_model->updateRecTable('v_attendance', 'date_apply', date('Y-m-d H:i:s'), strtoupper($data['fcode']), $data['vsector'], $repcode);
          }
          //=================================================================

          $data['chkAttendance'] = $this->site_read_model->getRec("v_attendance", "where fair_code='" . $faircode . "' and rep_code = ? limit 1", $repcode, "");
          if ($data['chkAttendance'] == "") {
            $this->site_model->insertRecTable('v_attendance', $repcode, $barcodeValue, '', '', strtoupper($data['fcode']), $data['vsector']);
            $this->site_model->updateRecTable('v_attendance', 'date_apply', date('Y-m-d H:i:s'), strtoupper($data['fcode']), $data['vsector'], $repcode);

            $preregister = ($sendConfirmationEmail == "0" ? "INC" : "P");
            $updateAttendance = "1";
          } else {
            //==== chk if validated by coordinator
            foreach ($data['chkAttendance'] as $rsAttendance) {
              $validated = $rsAttendance['validated'];
              $preregister = ($rsAttendance['pre_reg'] == "P" ? "P" : "INC");
            }

            if ($validated == "0") {
              $updateAttendance = "1";
            } else {
              $updateAttendance = "0";
            }
          }
          if ($updateAttendance == "1") {
            $this->site_model->updateRecTable('v_attendance', 'visitor_type', $data['vtype'], strtoupper($data['fcode']), $data['vsector'], $repcode);
            $this->site_model->updateRecTable('v_attendance', 'pre_reg', $preregister, strtoupper($data['fcode']), $data['vsector'], $repcode);
            $this->site_model->updateRecTable('v_attendance', 'date_input', date('Y-m-d H:i:s'), strtoupper($data['fcode']), $data['vsector'], $repcode);
          }

          //===================================================================== 


          if ($sendConfirmationEmail == "1") {

            echo $xresult;
            exit();
          }

          $vsend = '2'; // INCLUDE MESSAGE IF PROBLEM IN SAVING


          //die("aaaa= ".$repcode);
        } else //======= $data['profiles2'] does not exist ==========
        {



          $emailTemplate1 = "";
          $confirmationPage = "CPAGE4";  //=== Landing Page Deleted/Incomplete ===               

          if (isset($_POST['pcode']) && $_POST['pcode'] <> "") {

            $vGuest = $_POST['email'] . "~" . $_POST['pcode'] . "~~~" . $_POST['fcode'] . "-GUEST";
            $TMP1 = $this->encryption->encrypt($vGuest);
            $TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);

            redirect('registration/step2?pid=' . $TMP2 . "&rel=&app=5");
          } else {

            $xresult = $this->execSendProc($data['project'], $data['profiles2'], $data['vtype'], $emailTemplate1, $confirmationPage, $data['vemail'], "");
            echo $xresult;
            exit();
          }
        }
        //===================================================================================================

        // === for DTCP =====================================================================================
        if ($data['vEvent'] == "DTCP") {
          $vsend = '';
          $this->site_model->updateRecTable('v_attendance', 'pre_reg', 'P', strtoupper($data['fcode']), $data['vsector'], $repcode);
        }
        //=================================================================================================== 

        //=== FOR TESTING remove after ==========
        //$vsend='0';
        //=======================================
        //die($repcode);

        if (SEND_EMAIL == "NO") {
          $vsend = '';
        }

        $data['emailErr'] = "";
        if ($vsend == '1' || $vsend == '2') {
          $fcode = $this->input->post('fcode');
          if ($vsend == '1') {
            $sendto = $this->input->post('email');
          }
          if ($vsend == '2') {
            $sendto = $registeredEmail;
          }
          //====== from busmatch_date table
          $data['sendfrom'] = $this->input->post('emailfrom');
          $subj = $this->input->post('projectdesc') . "";
          $banner = $this->input->post('bannerpic');
          //==============================		             	 			

          if ($data['vtype'] == 'TRADE BUYER') {
            $data['vtype'] = 'TB';
          }
          if ($data['vtype'] == 'GENERAL PUBLIC') {
            $data['vtype'] = 'GP';
          }

          //$pid = $this->encrypt2->encode2($pid);    // encrypt PID
          //$TMPpid = $pid;

          $pid = $this->encryption->encrypt($data['vemail']);                   // $pid
          $pid = str_replace(array('+', '/', '='), array('-', '_', '^'), $pid);

          //$decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $pid);
          //$decryptX = $this->encryption->decrypt($decryptX);

          $step2link = $this->input->post('urlPage1') . "&pid=" . $pid . "&vt=" . $data['vtype'] . "&preg=1";

          $this->site_model->updateRecTable('v_attendance', 'url_form', $step2link, strtoupper($data['fcode']), $data['vsector'], $repcode);

          //die($step2link."<br><br>".$TMPpid."<br><br>".$decryptX);

          if ($vsend == '1') {
            $message = $this->messagePrereg($subj, $step2link, $data['sendfrom'] . "^" . $cont_per, $banner, '0', $data['vsector']);
          }  // 0 - email not exist
          else {
            $message = $this->messagePrereg($subj, $step2link, $data['sendfrom'] . "^" . $cont_per, $banner, '1', $data['vsector']);
          }  // 1 - email exist 

          //echo $message;
          //die();

          $naSend = $this->sendEmail($sendto, $data['sendfrom'], "", $data['ccEmail'], "", $subj, $message);
          if ($naSend <> "") {
            //die($naSend);
            $data['errDesc'] = $naSend;
            $data['emailErr'] = "We are very sorry but there was a problem in Sending Email, please try again later... (s1)";
            $this->writeError($data['errDesc'], $subj, $repcode, $barcodeValue, $data['fcode'], $data['vsector'], $sendto, "step1 - s1");
          }
          // ===========================

        }
        //}// $buyerExist=='1'


        $getCPage = $this->master_model->loadRec("v_reference", "where switch ='template' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");
        if ($getCPage == "") {
          die("contact SMDD... no ref found (j7)");
        }

        $vtemp = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message']; 
        $data['PageMessage']  = str_replace("{message}", "STEP000.10", $vtemp);

        $this->load->view('vrs_message', $data);
      } // validataion
    }     // $pregClose		  
  }     //step1


  /**
   * Validate the password
   *
   * @param string $password
   *
   * @return bool
   */
  public function valid_password($password = '')
  {
    $password = trim($password);

    $regex_anycase = '/[a-zA-Z]/';
    $regex_lowercase = '/[a-z]/';
    $regex_uppercase = '/[A-Z]/';
    $regex_number = '/[0-9]/';
    $regex_special = '/[!@#$%^&*()\-_=+{};:,<.>§~]/';

    if (empty($password)) {
      $this->form_validation->set_message('valid_password', 'The {field} field is required.');

      return FALSE;
    }
    if (preg_match_all($regex_anycase, $password) < 1) {
      $this->form_validation->set_message('valid_password', 'The {field} field must be at least one letter.');

      return FALSE;
    }

    // if (preg_match_all($regex_lowercase, $password) < 1)
    // {
    //   $this->form_validation->set_message('valid_password', 'The {field} field must be at least one lowercase letter.');

    //   return FALSE;
    // }

    // if (preg_match_all($regex_uppercase, $password) < 1)
    // {
    //   $this->form_validation->set_message('valid_password', 'The {field} field must be at least one uppercase letter.');

    //   return FALSE;
    // }

    if (preg_match_all($regex_number, $password) < 1) {
      $this->form_validation->set_message('valid_password', 'The {field} field must have at least one number.');

      return FALSE;
    }

    // if (preg_match_all($regex_special, $password) < 1)
    // {
    //   $this->form_validation->set_message('valid_password', 'The {field} field must have at least one special character.' . ' ' . htmlentities('!@#$%^&*()\-_=+{};:,<.>§~'));

    //   return FALSE;
    // }

    if (strlen($password) < 5) {
      $this->form_validation->set_message('valid_password', 'The {field} field must be at least 5 characters in length.');

      return FALSE;
    }

    if (strlen($password) > 20) {
      $this->form_validation->set_message('valid_password', 'The {field} field cannot exceed 20 characters in length.');

      return FALSE;
    }

    return TRUE;
  }

  function execSendProc($vproject, $vprofiles, $vtypes, $emailTemplate, $vconfirmationPage, $processEmail, $promoCode, $visitorType)
  {

    //$emailTemplate1   = "VMESS1";
    //$confirmationPage = "CPAGE1";

    $naSend = "";
    // =============== confirmation page message for message.php ==========================
    $getCPage = $this->master_model->loadRec("v_reference", "where switch ='" . $vconfirmationPage . "' and exclude=0 AND fair_code='" . $vproject[0]['fair_code'] . "' and sector like '%" . $vproject[0]['sector'] . "%' order by sortfield LIMIT 1");
    if ($getCPage == "") {
      die("contact SMDD... template not defined33");
    }
    $PageMess = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];    //$getCPage[0]['content_message'];


    if ($processEmail == "getVtype" || $processEmail == "newRecord") {

      if ($processEmail == "newRecord") {
        $vGuest = $vprofiles . "~~~~" . $vproject[0]['fair_code'] . "-GUEST";
        $vGP = $vprofiles . "~~~~" . $vproject[0]['fair_code'] . "-GP";
        $vMedia = $vprofiles . "~~~~" . $vproject[0]['fair_code'] . "-MEDIA";
        $newRec = "&n=1";
      } else {
        $vGuest = $vprofiles[0]['pid'] . "~~~~" . $vproject[0]['fair_code'] . "-GUEST";
        $vGP = $vprofiles[0]['pid'] . "~~~~" . $vproject[0]['fair_code'] . "-GP";
        $vMedia = $vprofiles[0]['pid'] . "~~~~" . $vproject[0]['fair_code'] . "-MEDIA";
        $newRec = "";
      }

      $TMP1 = $this->encryption->encrypt($vGuest);
      $TMP2 = $this->encryption->encrypt($vGP);
      $TMP3 = $this->encryption->encrypt($vMedia);

      $vt1 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);
      $vt2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP2);
      $vt3 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP3);

      // 'registration/step2?pid='.$TMP2."&rel=&app=5"
      $mess1 = str_replace("{pidGov}", $vt1 . $newRec, $PageMess);
      $mess2 = str_replace("{pidGP}", $vt2 . $newRec, $mess1);
      $mess3 = str_replace("{pidMedia}", $vt3 . $newRec, $mess2);

      $PageMess = $mess3;
    } else if ($processEmail == "pcodeUsed") {

      $messTxt = "Sorry, the invite code, <strong>" . $vprofiles . "</strong>, was already been used.";
      $mess0 = str_replace("{message}", $messTxt, $PageMess);
      $PageMess = $mess0;
    } else if ($processEmail == "pcodeInvalid") {

      $messTxt = "Sorry, the promo code, <strong>" . $vprofiles . "</strong>, is invalid";
      $mess0 = str_replace("{message}", $messTxt, $PageMess);
      $PageMess = $mess0;
    } else if ($processEmail == "regAsVisitor") {

      $vGuest = $vprofiles[0]['pid'] . "~~~~" . $vproject[0]['fair_code'];
      $TMP1 = $this->encryption->encrypt($vGuest);
      $vt1 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);
      $mess1 = str_replace("{pid}", $vt1, $PageMess);
      $PageMess = $mess1;
    } else if ($processEmail == "verifyemail") {

      $getContent = $this->master_model->loadRec("v_reference", "where switch ='" . $emailTemplate . "' and exclude=0 and fair_code='" . $vproject[0]['fair_code'] . "' AND sector like '%" . $vproject[0]['sector'] . "%' order by sortfield LIMIT 1");
      if ($getContent == "") {
        die("contact SMDD... project not defined1");
      }
      $messContent = $getContent[0]['header'] . $getContent[0]['content_message'] . $getContent[0]['footer'];     //$getContent[0]['content_message'];
      $messTitle = $getContent[0]['c_profile'];

      // == value of $vtypes = email if dumaan dito
      $vGuest = $vtypes . "~~~~" . $vproject[0]['fair_code'] . "~" . $promoCode . "~verifiedEmail" . "~~" . $visitorType;   //die("aaa=".$vGuest); 
      // == value of $vtypes = email if dumaan dito
      $TMP1 = $this->encryption->encrypt($vGuest);
      $vt1 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);

      $fname = (isset($vprofiles[0]['cont_per_fn']) ? "&nbsp" . $vprofiles[0]['cont_per_fn'] : "");
      $lname = (isset($vprofiles[0]['cont_per_ln']) ? "&nbsp" . $vprofiles[0]['cont_per_ln'] : "");
      $coname = (isset($vprofiles[0]['co_name']) ? $vprofiles[0]['co_name'] : "");

      $mess1 = str_replace("{PID}", $vt1, $messContent);
      $mess2 = str_replace("{fname}", $fname, $mess1);
      $mess3 = str_replace("{lname}", $lname, $mess2);
      $mess4 = str_replace("{coname}", $coname, $mess3);

      //$PageMess = $messContent;

      $message = $mess4;
      //=======================================================

      $sendto = $vtypes;
      $vfrom = $vproject[0]['emailto_registration'];
      $vcc   = $vproject[0]['emailto'];

      // ==== REMOVE comment BELOW ============================================================================

      $naSend = $this->sendEmail($sendto, $vfrom, "", $vcc, "", $vproject[0]['description'], $message);

      // ==== REMOVE comment ABOVE ============================================================================

    } else if ($processEmail == "processEmail") {

      // =============== get Email Content ==================================================
      $getContent = $this->master_model->loadRec("v_reference", "where switch ='" . $emailTemplate . "' and exclude=0 and fair_code='" . $vproject[0]['fair_code'] . "' AND sector like '%" . $vproject[0]['sector'] . "%' order by sortfield LIMIT 1");
      if ($getContent == "") {
        die("contact SMDD... project not defined1");
      }
      $messContent = $getContent[0]['header'] . $getContent[0]['content_message'] . $getContent[0]['footer'];     //$getContent[0]['content_message'];
      $messTitle = $getContent[0]['c_profile'];

      // =====================================================================================

      // ===== CREATE qrcode ===============================================================
      // ===== createQRdetails($visitorType,$position,$fn,$ln,$email,$mobile,$comp,$ctry,$rcode,$fcode,$XincludeAllProfile,$vrsPrereg) ======

      $qrcods = $this->createQRdetails($vtypes, "", $vprofiles[0]['cont_per_fn'], $vprofiles[0]['cont_per_ln'], $vprofiles[0]['email'], "", $vprofiles[0]['co_name'], $vprofiles[0]['country'], $vprofiles[0]['rep_code'], $vproject[0]['fair_code'], "yes", "P", "");

      //$qrValue = base_url("idbadge/".$vproject[0]['fair_code']."-".$vprofiles[0]['rep_code'].".png");
      //$qrcode = " <img class='gitna' src='".$qrValue."' alt='Your QRcode' width='150' height='150'> ";

      $qrcode = " <img src='" . base_url("idbadge/" . $qrcods) . "' alt='Your QRcode' width='150' height='150'> ";

      //$proofPaymentURL = "https://citem.com.ph/paymentproof/?loc=email&m=".$registeredEmail."&vcode=".$repcode;
      //$ankor1 = "<a href='".$proofPaymentURL."' style='text-decoration: none; color:blue;' target='_blank'>";
      //$ankor2 = "</a>";

      $nameProper1 = ucwords(strtolower($vprofiles[0]['cont_per_fn']));
      $nameProper2 = ucwords(strtolower($vprofiles[0]['cont_per_ln']));

      $mess0 = str_replace("{first_name}", $nameProper1, $messContent);
      $mess1 = str_replace("{last_name}", $nameProper2, $mess0);
      $mess2 = str_replace("{co_name}", $vprofiles[0]['co_name'], $mess1);
      $mess3 = str_replace("{email}", $vprofiles[0]['email'], $mess2);
      $mess4 = str_replace("{qrcode}", $qrcode, $mess3);
      $mess5 = str_replace("{repcode}", $vprofiles[0]['rep_code'], $mess4);
      //$mess6 = str_replace("{a1}",$ankor1,$mess5);
      //$mess7 = str_replace("{a2}",$ankor2,$mess6);
      //$urlMes = str_replace("{tag2}","</a>",$mess2);    

      //$vcontact = $fname." ".$lname;

      $message = $mess5;
      //=======================================================

      $sendto = $vprofiles[0]['email'];
      $vfrom = $vproject[0]['emailto_registration'];
      $vcc   = $vproject[0]['emailto'];

      // ==== REMOVE comment BELOW ============================================================================

      $naSend = $this->sendEmail($sendto, $vfrom, "", $vcc, "", $vproject[0]['description'], $message);

      // ==== REMOVE comment ABOVE ============================================================================

    } else if ($processEmail == "noEmail") {

      // no process landing page only
    } else {    // rec does not exist or no DTCP found in attendance

      $vGuest = $processEmail . "~~~~" . $vproject[0]['fair_code'];
      $TMP1 = $this->encryption->encrypt($vGuest);
      $vt1 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);
      $mess1 = str_replace("{pid}", $vt1, $PageMess);
      $PageMess = $mess1;
    }

    if ($naSend <> "") {
      //die($naSend);
      $errDesc = $naSend;
      $messageX = "We are very sorry but there was a problem in Sending Email, please try again later... (v1)";
      $this->writeError($errDesc, $vproject[0]['description'], $vprofiles[0]['rep_code'], $vprofiles[0]['barcode'], $vproject[0]['fair_code'], $vproject[0]['sector'], $sendto, "execSendProc()");
      $updateTable = "0";

      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='template' and exclude=0 AND sector like '%" . $vproject[0]['sector'] . "%' order by sortfield LIMIT 1");
      if ($getCPage == "") {
        die("contact SMDD... project not defined3");
      }
      $PageMess = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];    //$getCPage[0]['content_message'];
      $PageMessage = str_replace("{message}", $messageX, $PageMess);
    } else {
      $PageMessage = str_replace("{message}", "", $PageMess);
    }



    return $PageMessage;
  }

  function sendEmail($vRecipient, $vFrom, $vReplyto, $vCC, $vBCC, $vSubject, $vContent)
  {

    // Load PHPMailer library
    $this->load->library('phpmailer_lib');

    // PHPMailer object
    $mail = $this->phpmailer_lib->load();

    // SMTP configuration
    $mail->isSMTP();
    $mail->Host     = SMTP_HOST;
    $mail->SMTPAuth = SMTP_AUTH;
    $mail->SMTPAutoTLS = SMTP_AutoTLS;
    $mail->Username = SMTP_USER;
    $mail->Password = SMTP_PASS;
    $mail->SMTPSecure = SMTP_ENCRYPT;     // 'ssl'    // tsl
    $mail->Port     = SMTP_PORT;

    //$mail->SMTPDebug  = 2;              // to enable debug


    $mail->setFrom($vFrom);                  //  ('<EMAIL>', 'CodexWorld');
    if ($vReplyto <> "") {
      $mail->addReplyTo($vReplyto);            //  ('<EMAIL>', 'CodexWorld');
    }

    // Add a recipient
    $mail->addAddress($vRecipient);

    // Add cc or bcc 
    if ($vCC <> "") {
      $mail->addCC($vCC);                       //('<EMAIL>');
    }

    if ($vBCC <> "") {
      $mail->addBCC($vBCC);                     //('<EMAIL>');
    }

    // Email subject
    $mail->Subject = $vSubject;

    // Set email format to HTML
    $mail->isHTML(true);

    // Email body content
    $mailContent = $vContent;
    $mail->Body = $mailContent;

    // Send email
    if (!$mail->send()) {
      // echo 'Message could not be sent.';
      // echo 'Mailer Error: ' . $mail->ErrorInfo;
      $result = $mail->ErrorInfo;
    } else {
      //echo 'Message has been sent';
      $result = "";
    }

    return $result;
  }



  function confirmreg()
  {
    if (!isset($_GET['pid'])) {

      $this->endOfError("illegal value, please <NAME_EMAIL> (5.0)");
      die("illegal value, please <NAME_EMAIL> (5.0)");
    }

    // $this->load->model('master_model');
    // $this->load->model('site_read_model');	

    $data['step'] = "confirmreg";

    $data['pid'] = $_GET['pid'];
    $vpid = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['pid']);
    $pid = $this->encryption->decrypt($vpid);

    //die("wazzhmm1 = ".$pid);

    $getPID = explode("~", $pid);
    $data['vRepcode'] = $getPID[0];

    if (!ctype_alnum($data['vRepcode'])) {

      $this->endOfError("illegal value, please <NAME_EMAIL> (5.1)");
      die("illegal value, please <NAME_EMAIL> (5.1)");
    }

    $data['fcode'] = $getPID[4];
    $data['project'] = $this->master_model->getRec("busmatch_date", "where active='1' and fair_code = ?", $data['fcode'], '');
    $data['cprofile'] = $this->site_read_model->getRec("v_contact_profile", "where rep_code = ?", $data['vRepcode'], '');

    // ==== chk /update module if active in child tables ==============================
    //=================================================================================
    $data['chkStat'] = $this->chkModuleTable($data['project'], $data['vRepcode'], strtoupper($data['fcode']), "YES");

    //die("wazz=".$data['chkStat']);
    //=================================================================================    

    $this->load->view('formsuccess', $data);
  }



  function endOfError($errorMessage)
  {
    //die("");
    $data['step'] = "endOfError";
    $data['messError'] = $errorMessage;
    $this->load->view('formsuccess', $data);

    $this->output->_display();    // stop program
    exit();
  }


  function step2()
  {

    // $sessionArray = array(
    //      'realname' => "aaaa",
    //      'urights' => "bbbb",
    //      'controller_CI' => "registration"
    // ); 

    // $this->session->set_userdata('sessionData',$sessionArray);

    if (SHOW_ERROR == FALSE) {
      //error_reporting(0);
      ini_set('error_reporting', 0);
      //ini_set('display_errors',ERR_DISPLAY); 
    }

    if (isset($_GET['newwindow']) && $_GET['newwindow'] == "no") {
      $set_data = $this->session->all_userdata();
      $data = sessionRights($set_data);
    }
    if (isset($_POST['newwindow']) && $_POST['newwindow'] == "no") {
      $set_data = $this->session->all_userdata();
      $data = sessionRights($set_data);
    }

    $data['controller_CI'] = "registration";

    //====== TEMP lang ito replace PID searching with repcode nalang =======
    //====== from function vrsedit() in vleftcolmenu_func.php ==============
    //======================================================================
    if (isset($_GET['getpid']) && $_GET['getpid'] <> "") {
      $temp = $_GET['getpid'];
      $getPID = $this->site_read_model->getRec("v_contact_profile", "where rep_code = ? limit 1", $temp, '');

      unset($_GET['pid']);
      $_GET['pid'] = $getPID[0]['pid'];
      //die("aaa=". $_GET['pid']);
    }
    //====== from function vrsedit() in vleftcolmenu_func.php ==============
    //======================================================================
    //====== TEMP lang ito replace PID searching with repcode nalang =======

    // === faircode required
    $data['fcode'] = (isset($_GET['fcode']) ? $_GET['fcode'] : "");               //print_r($_FILES);
    if ($data['fcode'] == "") {
      $data['fcode'] = $this->input->post('fcode');
    }      // print_r($_POST); //die("yyy=".$data['fcode']);  

    // === check if Government is selected in "Landing Page Disapproved/Deactivated" (editMessage?m=CPAGE3) ===============================
    $data['isGovernment'] = (isset($_GET['gov']) ? $_GET['gov'] : (isset($_POST['gov']) ? $_POST['gov'] : ""));
    // ====================================================================================================================================

    $data['allow'] = (isset($_GET['allow']) ? $_GET['allow'] : $this->input->post('allow'));    // ==== allow edit even if prereg is closed
    $data['rel']   = (isset($_GET['rel']) ? $_GET['rel'] : $this->input->post('rel'));          // ==== used for opereations marketing officers   
    $data['vedit'] = (isset($_GET['vedit']) ? $_GET['vedit'] : $this->input->post('vedit'));


    $data['promoCode'] = "";
    $data['isInvited'] = "";
    $data['vtypefromInvite'] = "";
    $data['onSiteReg'] = "";

    $data['verifiedEmail'] = (isset($_POST['verifiedEmail']) && $_POST['verifiedEmail'] == "verifiedEmail" ? $_POST['verifiedEmail'] : "");
    $data['allowEmailInput'] = (isset($_POST['allowEmailInput']) && $_POST['allowEmailInput'] == "allowEmailInput" ? $_POST['allowEmailInput'] : "");

    if ($data['fcode'] == "") {     //die("aaa= ");

      if (isset($_GET['pid'])) {

        $vpid = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['pid']);
        $getPID = explode("~", $this->encryption->decrypt($vpid));                        // die($this->encryption->decrypt($vpid));

        $data['fcode'] = (isset($getPID[4]) ? $getPID[4] : "");
        $data['promoCode'] = (isset($getPID[5]) ? $getPID[5] : "");
        $data['verifiedEmail'] = (isset($getPID[6]) ? $getPID[6] : "");
        $data['allowEmailInput'] = (isset($getPID[7]) ? $getPID[7] : "");     // "~allowEmailInput" for onsite entry, disable readonly in registration_form_new, email field

        $data['vtypefromInvite'] = (isset($getPID[8]) ? $getPID[8] : "");

        $data['isInvited'] = ($data['promoCode'] <> "" ? "yes" : "no");  //die($data['promoCode']);        

      } elseif (isset($_GET['qr'])) {

        $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['qr']);  // 2#MFIO2024#0#MEDIA#WTC#N#yes#2      
        $getQRvalue = explode("#", $this->encryption->decrypt($decryptX));
        //die($this->encryption->decrypt($decryptX));
        $data['fcode'] = $getQRvalue[1];

        $data['onSiteReg'] = "1";
      } else {
        die("Problem with Link value(2)... pls report to smdd");
      }
    }

    // === get project 
    $data['project'] = $this->master_model->getRec("busmatch_date", "where active='1' and fair_code = ?", strtoupper($data['fcode']), '');

    if ($data['project'] == '') {
      die($data['fcode'] . " - do not exist2");
    }

    if ($data['allow'] == "1" || $data['allow'] == "2") {

      $pregClose = '0';
    } else {

      $pregClose = $this->check_status1($data['project'][0]['disable_prereg'], 0);    // chek value of explode (0 - array[0] , 1 - array[1]) 

    }

    //die("aaax=".$pregClose);


    if ($pregClose == '1') {

      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='PCLOSE' and exclude=0 AND sector like '%" . $data['project'][0]['sector'] . "%' order by sortfield LIMIT 1");

      if ($getCPage == "") {
        die("contact SMDD... no ref found (j2)");
      }

      $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message'];    

      //$this->load->view('vrs_message', $data); // pre-registration CLOSE   
      echo $data['PageMessage'];

      exit();
    }

    //die("bbb=".$pregClose);


    // ============================ self service ====================================================================
    if (isset($_GET['qr'])) {

      //============ create session for registration_form_new.php ==================================================
      $data['userAccessMod'] = $this->master_model->loadRec("v_reference", "where switch ='VMOD' and exclude=0 order by sortfield");

      $ctr = 0;
      $sessionArray = array();
      if ($data['userAccessMod'] <> "") {
        foreach ($data['userAccessMod'] as $r2) {
          $ctr++;
          $sessionArrayT1[$ctr] = array(
            $ctr . "r" => $r2['c_code']
          );
          $sessionArray = array_merge($sessionArray, $sessionArrayT1[$ctr]);
          // $sessionArray = $sessionArrayT1[$ctr];
        }
      }
      //insert number of recs in $data['userAccessMod']
      $sessionArrayT2 = array('numrights' => $ctr);
      $sessionArray = array_merge($sessionArray, $sessionArrayT2);

      $this->session->set_userdata('sessionData', $sessionArray);
      //============ create session for registration_form_new.php ==================================================
      //============================================================================================================


      //  $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['qr']);

      //  if (!ctype_print($this->encryption->decrypt($decryptX)))   
      //    {
      //    	die("Problem... 001a");
      //    }

      // $getQRvalue = explode("#", $this->encryption->decrypt($decryptX));   

      //print_r($this->encryption->decrypt($decryptX)); die();

      // 2#MFIO2024#0#MEDIA#WTC#N#yes#2 
      unset($_GET['vrs']);
      unset($_GET['fcode']);
      unset($_GET['station']);
      unset($_GET['vt']);
      unset($_GET['venue']);
      unset($_GET['bcard']);
      unset($_GET['qrcode']);
      unset($_GET['allow']);

      $_GET['vrs'] = $getQRvalue[0];
      $_GET['fcode'] = $getQRvalue[1];
      $_GET['station'] = $getQRvalue[2];
      $_GET['vt'] = $getQRvalue[3];
      $_GET['venue'] = $getQRvalue[4];
      $_GET['bcard'] = $getQRvalue[5];
      $_GET['qrcode'] = $getQRvalue[6];
      $_GET['allow'] = $getQRvalue[7];


      //redirect('registration/step2?vrs='.$getQRvalue[0].'&fcode='.$getQRvalue[1].'&station='.$getQRvalue[2].'&vt='.$getQRvalue[3].'&venue='.$getQRvalue[4].'&bcard='.$getQRvalue[5].'&pid='.'&qrcode='.$getQRvalue[6]); 

    }



    //====================== Barkada package =====================================================================

    $data['refNumberForPackage'] = (isset($_POST['refNumberForPackage']) ? $_POST['refNumberForPackage'] : "");

    if (isset($_GET['pkg']) && $_GET['pkg'] <> "") {

      $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['pkg']);
      $packageUrl = $this->encryption->decrypt($decryptX);

      //$packageUrl = $this->encrypt2->decode2($_GET['pkg']);  //die("aaa= ". $packageUrl);
      $tempvar = str_replace('_', '', $packageUrl);
      if (!ctype_alnum(str_replace('#', '', $tempvar))) {
        die("illegal packageProc(1), please <NAME_EMAIL>");
      }
      //$vlinkURL = "fcode=".$vproject[0]['fair_code']."&pid=&vt=VISITOR&preg=1&refNumber=".$vpaymentOrder."&vrs=2&station=0";
      // $vlinkURL = $vproject[0]['fair_code']."#VISITOR#1#".$vpaymentOrder."#2#0";
      $rsUrl = explode("#", $packageUrl);

      $_GET['fcode'] = $rsUrl[0];
      $_GET['vt'] = $rsUrl[1];
      $_GET['preg'] = $rsUrl[2];
      $_GET['vrs'] = $rsUrl[4];            // 2
      $_GET['station'] = $rsUrl[5];        // 0

      $data['refNumberForPackage'] = $rsUrl[3];

      //=============== CHECK if number of use of package is CONSUMED ======================

      $paymentOrder = $this->site_read_model->getRec("v_payment_order", "where item_code = ?", $data['refNumberForPackage'], '');

      $rsCountNumberForPackage = $this->site_read_model->countRec("v_contact_profile", "add_value='" . $data['refNumberForPackage'] . "'");

      //die("zzz= ". $rsCountNumberForPackage."<br>"."xxx= ".$rspOrder[0]['url_link_number']);      
      if ($rsCountNumberForPackage >= $paymentOrder[0]['url_link_number']) {
        redirect('registration/failed');
        //$this->load->view('failed',$data,TRUE); 
        //die();
      }


      //====================================================================================

    }
    //============================================================================================================

    //============= chk if PREREG so date_apply field can be UPDATED =====================================================================
    $data['updateIfPrereg'] = (isset($_GET['preg']) ? $_GET['preg'] : (isset($_POST['updateIfPrereg']) ? $_POST['updateIfPrereg'] : ""));
    //====================================================================================================================================

    //print_r($_POST['pconame'][1]);die();

    $this->form_validation->set_error_delimiters('<div style="background-color:#FF6666">', '</div>'); //globally change the error delimiters 

    // ======== if larger than your POST_MAX_SIZE ini setting using file upload ===========================================================
    if (!empty($_SERVER['CONTENT_LENGTH']) && empty($_FILES) && empty($_POST)) {
      $maxSize = UPLOAD_MAXFILESIZE * 1024;
      $this->endOfError("The Uploaded File is too large. Upload smaller than " . UPLOAD_MAXFILESIZE . "KB");
    }
    //echo 'The uploaded zip was too large. You must upload a file smaller than ' . ini_get("upload_max_filesize");
    // ====================================================================================================================================



    if ($data['project'][0]['stand_alone_form'] == '1') {
      $_GET['vrs'] = '3';
    }

    $data['fullView'] = $data['project'][0]['full_screen_view'];       //die("zzz= ".$data['fullView']);

    $genericFaircode = $data['project'][0]['event'];

    // ==============================================================================  
    // ******* IF used in VRS (include &vrs=1 to indicate used by VRS)***************
    // ******* valuss "1" is vrs without validation  ==== valuss "2" is vrs with validation
    if (isset($_GET['vrs'])) {
      $data['vrs'] = $_GET['vrs'];
    } else {
      $data['vrs'] = $this->input->post('vrs');
    }

    //die('xxx= '.$data['vrs']);


    // ==== used for MOBILE APP ====================================================================================
    $data['app'] = (isset($_GET['app']) ? $_GET['app'] : $this->input->post('app'));    // ==== used for MOBILE APP
    //$data['allowValidated'] = (isset($_GET['aV']) ? $_GET['aV'] : $this->input->post('aV'));
    // =============================================================================================================

    $data['qrcode'] = (isset($_GET['qrcode']) ? $_GET['qrcode'] : $this->input->post('qrcode'));    // ==== used for MOBILE APP

    if (isset($_GET['autoprint'])) {
      $data['autoprint'] = $_GET['autoprint'];
    } else {
      $data['autoprint'] = $this->input->post('autoprint');
    } //die("aaa =".$pid);}   

    // ======= VRS use ==============================================================
    // ==============================================================================  
    $data['bcard'] = "";
    $data['vclass'] = "";
    $data['venue'] = "";
    $data['station'] = "";
    $data['eventGoin'] = "";
    $data['vib1'] = "";

    if ($data['vrs'] <> "" && $data['vrs'] <> "3") {
      if (isset($_GET['venue'])) {
        $data['venue'] = $_GET['venue'];
      } else {
        $data['venue'] = $this->input->post('venue');
      }

      if (isset($_GET['station'])) {
        $data['station'] = $_GET['station'];
      } else {
        $data['station'] = $this->input->post('station');
      }

      //if ($data['app']=="")
      //{
      if ($data['station'] == "") {
        die("STATION # is Required... report to IT personel");
      }
      //}

      $data['eventGoin'] = (isset($_GET['eventGoin']) ? $_GET['eventGoin'] : $this->input->post('eventGoin'));
      $data['vib1'] = (isset($_GET['vib1']) ? $_GET['vib1'] : $this->input->post('vib1'));
      $data['bcard'] = (isset($_GET['bcard']) ? $_GET['bcard'] : $this->input->post('bcard'));

      $data['vclass'] = (isset($_GET['vclass']) ? $_GET['vclass'] : $this->input->post('vclass'));

      $data['userAccessMod'] = $this->master_model->loadRec("v_reference", "where switch ='VMOD' and exclude=0 order by sortfield");

      //$data['vedit'] = (isset($_GET['vedit']) ? $_GET['vedit'] : $this->input->post('vedit'));      

      // ===== CREATEPHILs =========================================================== 
      //$data['workshop'] = (isset($_GET['workshop']) ? $_GET['workshop'] : $this->input->post('workshop')); 
      // ===== CREATEPHILs =========================================================== 

    }


    // faircode required
    // *********** ORIGINAL position of $data[$fcode] ---- moved it at TOP so value of fcode can be get for "standalone" form purpose
    // ======================================================================================================== 
    //$data['fcode'] = (isset($_GET['fcode']) ? $_GET['fcode'] : "");
    //if ($data['fcode'] == "") {$data['fcode']= $this->input->post('fcode'); }   //die("zzz=".$data['fcode']);  

    // ======= get VISITOR TYPE ===============================================================================

    $faircode = strtoupper($data['fcode']);

    //=== check if sector GENERAL ====================
    $tmpSector = $data['project'][0]['sector'];

    // if($tmpSector=="24" || $tmpSector=="30" || $tmpSector=="31" || $tmpSector=="32" || $tmpSector=="33" || $tmpSector=="34" || $tmpSector=="35" || $tmpSector=="36" || $tmpSector=="37")
    // {
    //    $vt =  'GUEST';     
    // } 
    // else
    // {
    //    $vt =  'TB'; 
    // }
    //=== check if sector GENERAL ====================

    //$vt = ($this->input->get('vt')==NULL || $this->input->get('vt')=="" ? "GUEST" : $this->input->get('vt'));
    $vt = ($this->input->get('vt') == NULL || $this->input->get('vt') == "" ? $this->input->post('visitorType') : $this->input->get('vt'));

    if (strpos($faircode, '-VISITOR') !== false) {
      $faircode = trim(str_replace("-VISITOR", "", $faircode));
      $vt = 'VISITOR';
    }
    if (strpos($faircode, '-GUEST') !== false) {
      $faircode = trim(str_replace("-GUEST", "", $faircode));
      $vt = 'GUEST';
    }
    if (strpos($faircode, '-MEDIA') !== false) {
      $faircode = trim(str_replace("-MEDIA", "", $faircode));
      $vt = 'MEDIA';
    }
    if (strpos($faircode, '-GP') !== false) {
      $faircode = trim(str_replace("-GP", "", $faircode));
      $vt = 'GENERAL PUBLIC';
    }
    if (strpos($faircode, '-VIB') !== false) {
      $faircode = trim(str_replace("-VIB", "", $faircode));
      $vt = 'VIB';
    }

    // ========================================================================================================

    // get value of visitor type to display fields in forms
    // ==================================================== 

    if ($this->input->post('visitorType') <> "") {
      $getType = $this->input->post('visitorType');
    }

    if (isset($vt)) {
      $vtype1 = strtoupper($vt);   // die("yyyy");

    } else {

      //$vtype1 = ($this->input->post('visitorType')<>"" ? $this->input->post('visitorType') : "GUEST");
      $vtype1 = ($this->input->post('visitorType') == "" ? $data['vtypefromInvite'] : $this->input->post('visitorType')); //die("xxx=".$vtype1);   //$getType;    
    }
    //die("zz= ".$this->input->post('visitorType'));
    // die("zz= ".$vtype1);
    $sessionData = $_SESSION['sessionData'];

    $formField = false;

    for ($i = 1; $i <= 30; $i++) {
        $key = $i . 'r';
        if (isset($sessionData[$key])) {
            if ($sessionData[$key] === 'enable_formFields') {
                $formField = true;
                break; // No need to keep checking
            }
        }
    }



    switch ($vtype1) {
      case "TB":
      case "TRADE BUYER":
        if ($formField) {
        $data['chkShowFields'] = $this->loadFromValue($data['project'], '1', 2, '0');     //=== loadFromValue($projectDB,$ifenable,$ifrequired,$step)
        } else {
          $data['chkShowFields'] = $this->loadFromValue($data['project'], 1, 2, 9);     //=== loadFromValue($projectDB,$ifenable,$ifrequired,$step)
        }
        $data['chkRequired'] = 2;
        $data['numModule'] = $this->check_status1($data['project'][0]['number_of_page_in_form'], 0);
        break;

      case "VISITOR":
      case "GUEST":
        if ($formField) {
          $data['chkShowFields'] = $this->loadFromValue($data['project'], '1', 4, '0');     //=== loadFromValue($projectDB,$ifenable,$ifrequired,$step)
        } else {
          $data['chkShowFields'] = $this->loadFromValue($data['project'], 3, 4, 10);     //=== loadFromValue($projectDB,$ifenable,$ifrequired,$step)
        }
        $data['chkRequired'] = 4;
        $data['numModule'] = $this->check_status1($data['project'][0]['number_of_page_in_form'], 1);    // die("xxxc");
        break;

      case "MEDIA":
        if ($formField) {
          $data['chkShowFields'] = $this->loadFromValue($data['project'], '1', 6, '0');     //=== loadFromValue($projectDB,$ifenable,$ifrequired,$step)
        } else {
          $data['chkShowFields'] = $this->loadFromValue($data['project'], 5, 6, 11);     //=== loadFromValue($projectDB,$ifenable,$ifrequired,$step)
        }
        $data['chkRequired'] = 6;
        $data['numModule'] = $this->check_status1($data['project'][0]['number_of_page_in_form'], 2);
        break;
      case "GP":
      case "GENERAL PUBLIC":

        if ($formField) {
          $data['chkShowFields'] = $this->loadFromValue($data['project'], '1', 7, '0');     //=== loadFromValue($projectDB,$ifenable,$ifrequired,$step)
        } else {
          $data['chkShowFields'] = $this->loadFromValue($data['project'], 7, 8, 12);     //=== loadFromValue($projectDB,$ifenable,$ifrequired,$step)
        }
        $data['chkRequired'] = 8;
        $data['numModule'] = $this->check_status1($data['project'][0]['number_of_page_in_form'], 3);
        break;
    }

    //================== LOAD values in Profile if to be shown/required =================================
    //$data['chkShowFields'] = $this->loadShowValue($data['project']); //die("aaa= ".$data['chkShowFields']['show_company_name']); print_r($zzz); 
    //===================================================================================================    

    //print_r($data['chkShowFields']);


    //$data['faircode'] = $faircode;

    // chk if PID is declared
    if (isset($_GET['pid'])) {

      //====================================================
      //======== mobile app from pointwest =================
      //====================================================
      //if(isset($_GET['app']) && $_GET['app']=="1")
      if (isset($data['app']) && ($data['app'] == "1" || $data['app'] == "2")) {
        $pid = $_GET['pid'];  // email is used instead PID in url

        $data['pticRefer']  = "";
        $data['pticReferID']  = "";
      }
      //====================================================
      //====================================================
      else { //die("waz");
        //====== chk if valid email is passed in PID ===================================================================
        //====== used for prereg confirmation page =====================================================================	
        $TMP1 = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['pid']);
        $vemail = $this->encryption->decrypt($TMP1);

        //die("xx=".$vemail);
        //die($TMP1);

        $vemail = filter_var($vemail, FILTER_SANITIZE_EMAIL);      // Remove all illegal characters from email
        if (filter_var($vemail, FILTER_VALIDATE_EMAIL)) {          // Validate e-mail
          //$data['app'] = "4";
          //die("zzz= ".$vemail);

          $xComp  = $this->site_read_model->getRec("v_contact_profile", "where email = ? limit 1", $vemail, '');
          $vComp = $xComp[0]['rep_code'] . "~~~~" . $data['fcode'];          //FAIR_CODE;
          $TMP1 = $this->encryption->encrypt($vComp);
          $TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);

          //die("zzz= ".$TMP1);

          // ========== used in registration_form_new.php to enable/didable "Submit" =======================
          // ========== if registration.php is called in VRS viewpreg() ====================================

          $iScannotSubmit = ((isset($_GET['can']) && $_GET['can'] == "2") ? "&can=" . $_GET['can'] : "");
          //die("aaa1= ".$iScannotSubmit);
          // ===============================================================================================

          redirect('registration/step2?pid=' . $TMP2 . "&chk=1&app=4" . $iScannotSubmit);
        }


        //$getPID = explode("~", $pid);
        //$vRepcode = $getPID[0];	

        //====== use for email,fcode and app=3 =========================================================================
        //====== for sending prereg an ecard to register ===============================================================
        //====== will redirect to registration FORM ==================================================================== 
        if ($data['app'] == "3") {

          $vpid = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['pid']);
          $pid = $this->encryption->decrypt($vpid);

          //$pid = $this->encryption->decrypt($_GET['pid']);

          $getPID = explode("~", $pid);
          $data['vRepcode'] = $getPID[0];
          $data['fcode'] = (isset($getPID[4]) ? $getPID[4] : "");

          //die($_GET['pid']."<br>".$data['vRepcode']."<br>".$pid);

          if (!ctype_alnum($data['vRepcode'])) {
            die("illegal value, please <NAME_EMAIL> (5.3)");
          }

          $data['xComp'] = $this->site_read_model->getRec("v_contact_profile", "where rep_code = ? limit 1", $data['vRepcode'], '');

          $vComp = $data['xComp'][0]['pid'] . "~~~~" . $data['fcode'];
          //$vComp = $_GET['pid'];
          //die($vComp);

          $TMP1 = $this->encryption->encrypt($vComp);
          $TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);
          //die($TMP2);

          // ========== used in registration_form_new.php to enable/didable "Submit" =======================
          // ========== if registration.php is called in VRS viewpreg() ====================================

          $iScannotSubmit = ((isset($_GET['can']) && $_GET['can'] == "2") ? "&can=" . $_GET['can'] : "");

          //die("aaa3= ".$iScannotSubmit);

          // ===============================================================================================

          redirect('registration/step2?pid=' . $TMP2 . "&rel=" . $data['rel'] . "&app=5" . $iScannotSubmit);
        }
        //====== use for marking registraion as confirmed pre-registered RSVP ==========================================
        //====== will redirect to confirmation page ====================================================================
        // step2?app=4
        if ($data['app'] == "4") {

          if (!isset($_GET['chk'])) {
            $xComp  = $this->site_read_model->getRec("v_contact_profile", "where email = ? limit 1", $_GET['pid'], '');
            $vComp = $xComp[0]['rep_code'] . "~~~~" . $data['fcode'];
            $TMP1 = $this->encryption->encrypt($vComp);
            $TMP2 = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);

            //die($vComp);

            redirect('registration/step2?pid=' . $TMP2 . "&chk=1&app=4");
          } else {

            // ========== used in registration_form_new.php to enable/didable "Submit" =======================
            // ========== if registration.php is called in VRS viewpreg() ====================================

            $iScannotSubmit = ((isset($_GET['can']) && $_GET['can'] == "2") ? "&can=" . $_GET['can'] : "");

            //die("aaa2= ".$iScannotSubmit);
            // ===============================================================================================

            redirect('registration/step2?pid=' . $_GET['pid'] . "&app=3" . $iScannotSubmit);

            //redirect('registration/confirmreg?pid='.$_GET['pid']);

          }
        }
        //==============================================================================================================	
        //==============================================================================================================	

        $pidTMP = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['pid']);

        // $pid = $this->encrypt2->decode2($_GET['pid']);    // DEPCRICATED

        $pid = $this->encryption->decrypt($pidTMP);

        //die("zzz= ".$pid);

        //====================================================
        //======== for Refer.php =============================
        //====================================================
        $getPID = explode("~", $pid);

        $pid = $getPID[0]; //die("zzz11= ".$pid);

        $data['pcode']  = ((isset($getPID[1])) ? $getPID[1] : "");  // ===== check if promo code was passed in vrs.php (createPromo func) 
        //$data['voucher_code'] = $data['pcode'];										

        $data['pticRefer']  = ((isset($getPID[1])) ? $getPID[1] : "");
        $data['pticReferID']  = ((isset($getPID[2])) ? $getPID[2] : "");   //die($pid);
        //====================================================
        //====================================================
        $data['buyerlist'] = ((isset($getPID[3])) ? $getPID[3] : "");
        //$data['Tmpfcode'] = ( (isset($getPID[4])) ? $getPID[4] : "" );


        $data['fcode'] = ((isset($getPID[4])) ? $getPID[4] : $data['fcode']);

        //print_r($getPID); die();

        $isemail = "0";
        if (!ctype_alnum($pid)) {
          if (filter_var($pid, FILTER_VALIDATE_EMAIL)) {    //=== from step0 if mew record --- $isemail=="1"
            $isemail = "1";
            $newEmail = $pid;   //die("xxx222=".$newEmail);

          } else {
            if ($data['vrs'] == "") {
              die("illegal value, please report to SMDD (6z)");
            } else {
              $pid = '0';
            }
          }
        }   // value of PID

      }

      if ($pid == "" || $pid == "0") {
        $pid = (isset($_GET['pid']) ? $_GET['pid'] : '0');
      }

      if ($isemail == "1") {
        $pid = $newEmail;
      }

      //die("ooo= ".$pid);

    } else {
      if (isset($data['app']) && ($data['app'] == "1" || $data['app'] == "2")) {
        $pid = $this->input->post('email');
      } else {
        $pid = (($this->input->post('pid') == "") ? '0' : $this->input->post('pid'));     //die("aaa");
      }
      //======== for Refer.php ================================================================================

      $data['pticRefer']    = (($this->input->post('pticRefer') == "") ? '' : $this->input->post('pticRefer'));
      $data['pticReferID']  = (($this->input->post('pticReferID') == "") ? '' : $this->input->post('pticReferID'));

      //=======================================================================================================
    }


    // ========== used in registration_form_new.php to enable/didable "Submit" =======================
    // ========== if registration.php is called in VRS viewpreg() ====================================

    if (isset($_GET['can']) && $_GET['can'] == "2") {
      $data['cannotSubmit'] = "yes";
    }

    //================================================================================================

    //die("aaa1 - ".$data['fcode']); 

    if (isset($_GET['showall'])) {
      $data['showall'] = $_GET['showall'];
    } else {
      $data['showall'] = $this->input->post('showall');
    }

    if (isset($data['buyerlist']) && $data['buyerlist'] <> "") {
      $data['showall'] = "1";
      $data['allow'] = "1";
    }

    //$data['showall']="1"; $data['allow']="1";

    // ==== used for MOBILE APP ====================================================================================
    //if($data['app']<>"") {$data['showall'] = "1";}
    // ============================================================================================================= 

    $data['base'] = $this->config->item('base_url'); // get base_url from config.php
    $data['main_page'] = ""; //$this->config->item('index_page'); // get index_page from config.php
    $data['css']  = $this->config->item('css');      // load mystyles.css defined in config.php 

    //chk if fcode exists and get project details
    // *********** ORIGINAL position of $data[project] ---- moved it at TOP so value of fcode can be get for "standalone" form purpose
    // ========================================================================================================   
    // $data['project']= $this->master_model->getRec("busmatch_date","where fair_code = ?",strtoupper($data['fcode']),'');
    // if ($data['project']=='') {die($data['fcode']." - do not exist");}  

    //================== LOAD values in Profile if to be shown/required =================================
    //$data['chkShowFields'] = $this->loadShowValue($data['project']); //die("aaa= ".$data['chkShowFields']['show_company_name']); print_r($zzz); 
    //===================================================================================================

    foreach ($data['project'] as $proj1) {
      $data['vsector']              = $proj1['sector'];
      $data['vdesc']                = $proj1['description'];
      $data['$url_event_page1']     = $proj1['url_event_page1'];
      $data['emailto_registration'] = $proj1['emailto_registration'];
      $data['ccEmail']              = $proj1['emailto'];
      $data['bannerpic']            = $proj1['bannerpic'];
      $data['footerpic']            = $proj1['footerpic'];
      $data['backgroundpic']        = $proj1['backgroundpic'];

      if ($data['allow'] == '1' || $data['allow'] == "2") {
        $pregClose = '0';
      } else {
        $pregClose = $this->check_status1($proj1['disable_prereg'], 0); // chek value of explode (0 - array[0] , 1 - array[1])
      }

      //======= get LAST EVENT (faircode)==================================
      $previousEvent = $proj1['url_registration_close'];
      //===================================================================	 

      //$data['v_hotel_voucher']       = $this->check_status1($proj1['show_hotel'],0);    // use for voucher code
      //$data['v_hotel_voucher_req']   = $this->check_status1($proj1['show_hotel'],2);    // use for voucher code 

      //$data['v_jobf']        = $this->check_status1($proj1['show_jobfunction'],0);    // chek value of explode (0 - array[0] , 1 - array[1])  
      //$data['v_repre']       = $this->check_status1($proj1['show_representation'],0);
      //$data['v_repre_req']       = $this->check_status1($proj1['show_representation'],2);  
      //$data['v_show_organization'] = $this->check_status1($proj1['show_organization'],0);
      //$data['v_show_dietary_information'] = $this->check_status1($proj1['show_dietary_information'],0);
      //$data['v_show_user_agreement'] = $this->check_status1($proj1['show_user_agreement'],0);
      //$data['v_show_learnedabout']      = $this->check_status1($proj1['show_learnedabout'],0);
      //$data['v_showProducts']      = $this->check_status1($proj1['show_products'],0);
      //$data['v_1stTime']      = $this->check_status1($proj1['show_firsttime'],0);   // chek value of explode (0 - array[0] , 1 - array[1])
      //$data['v_1stTime_req']      = $this->check_status1($proj1['show_firsttime'],2);
      //$data['v_showAnnualSales'] = $this->check_status1($proj1['show_annual_sales'],0);   
      //$data['v_showReason']      = $this->check_status1($proj1['show_reason'],0);   
      //$data['v_show_existing_arrangements_with_philippine_suppliers']      = $this->check_status1($proj1['show_existing_arrangements_with_philippine_suppliers'],0);
      //$data['v_showMarketSegments']      = $this->check_status1($proj1['show_market_segments'],0);	
      //$data['v_show_list_of_clients']      = $this->check_status1($proj1['show_list_of_clients'],0);  
      //$data['v_show_activity_would_like_to_join']         = $this->check_status1($proj1['show_activity_would_like_to_join'],0);
      //$data['v_show_require_an_interpreter']      = $this->check_status1($proj1['show_require_an_interpreter'],0);  
      //$data['v_show_interested_in_arrange_meetings']      = $this->check_status1($proj1['show_interested_in_arrange_meetings'],0);            

      $data['v_showFrequentTravelToAsia']      = $this->check_status1($proj1['show_frequent_travel_to_asia'], 0);
      $data['v_show_orders_placed_last_visit']      = $this->check_status1($proj1['show_orders_placed_last_visit'], 0);

      $data['v_showImpt']     = $this->check_status1($proj1['show_trade_shows_important'], 0);

      $data['v_showspecificProducts']     = $this->check_status1($proj1['show_specific_product'], 0);
      $data['v_showProductClassification']      = $this->check_status1($proj1['show_product_classification'], 0);
      $data['v_showImportantFactor']      = $this->check_status1($proj1['show_important_factor'], 0);
      $data['v_showWorkshop']     = $this->check_status1($proj1['show_workshop_or_seminar'], 0);
      $data['v_showWorkshopVenue']     = $this->check_status1($proj1['show_workshop_or_seminar_venue'], 0);

      $data['v_disable_epayment']    = $this->check_status1($proj1['disable_epayment'], 0);
      $data['v_show_upload_files']   = $this->check_status1($proj1['show_upload_files'], 0);


      //==== used to display all survey in form ===================
      //==== used to view profile of registered visitor ===========
      if ($data['showall'] == "1")  //isset($data['showall']) && 
      {
        $data['v_jobf_step']                                                    = "1";
        $data['v_repre_step']                                                   = "1";
        $data['v_showMarketSegments_step']                                      = "1";
        $data['v_showAnnualSales_step']                                         = "1";
        $data['v_showFrequentTravelToAsia_step']                                = "1";
        $data['v_1stTime_step']                                                 = "1";
        $data['v_show_orders_placed_last_visit_step']                           = "1";
        $data['v_showReason_step']                                              = "1";
        $data['v_show_learnedabout_step']                                       = "1";
        $data['v_showImpt_step']                                                = "1";
        $data['v_showProducts_step']                                            = "1";
        $data['v_showspecificProducts_step']                                    = "1";
        $data['v_showProductClassification_step']                               = "1";
        $data['v_showImportantFactor_step']                                     = "1";
        $data['v_showWorkshop_step']                                            = "1";
        $data['v_showWorkshopVenue_step']                                       = "1";
        $data['v_show_activity_would_like_to_join_step']                        = "1";
        $data['v_show_interested_in_arrange_meetings_step']                     = "1";
        $data['v_show_list_of_clients_step']                                    = '1';
        $data['v_show_existing_arrangements_with_philippine_suppliers_step']    = '1';

        $data['v_show_organization_step']                                       = '1';
        $data['v_show_dietary_information_step']                                = '1';
        $data['v_show_user_agreement_step']                                     = '1';
        $data['v_show_require_an_interpreter_step']                             = '1';

        $data['v_show_upload_files_step']                                       = '1';
        //======= get how many steps should the preregistration should proceed =================
        $data['numModule']                                                      = "1";
        //======================================================================================	  
      } else {
        //$data['v_jobf_step']    = $this->check_status1($proj1['show_jobfunction'],1);
        //$data['v_repre_step']  = $this->check_status1($proj1['show_representation'],1);
        //$data['v_show_organization_step'] = $this->check_status1($proj1['show_organization'],1);
        //$data['v_show_dietary_information_step'] = $this->check_status1($proj1['show_dietary_information'],1);
        //$data['v_show_user_agreement_step'] = $this->check_status1($proj1['show_user_agreement'],1);
        //$data['v_show_learnedabout_step'] = $this->check_status1($proj1['show_learnedabout'],1);
        //$data['v_showProducts_step'] = $this->check_status1($proj1['show_products'],1);   
        //$data['v_1stTime_step']  = $this->check_status1($proj1['show_firsttime'],1);
        //$data['v_showAnnualSales_step'] = $this->check_status1($proj1['show_annual_sales'],1);
        //$data['v_showReason_step'] = $this->check_status1($proj1['show_reason'],1); 
        //$data['v_show_existing_arrangements_with_philippine_suppliers_step']      = $this->check_status1($proj1['show_existing_arrangements_with_philippine_suppliers'],1);  
        //$data['v_showMarketSegments_step'] = $this->check_status1($proj1['show_market_segments'],1);
        //$data['v_show_list_of_clients_step']      = $this->check_status1($proj1['show_list_of_clients'],1); 
        //$data['v_show_activity_would_like_to_join_step']         = $this->check_status1($proj1['show_activity_would_like_to_join'],1);
        //$data['v_show_require_an_interpreter_step'] = $this->check_status1($proj1['show_require_an_interpreter'],1);
        //$data['v_show_interested_in_arrange_meetings_step']      = $this->check_status1($proj1['show_interested_in_arrange_meetings'],1);


        $data['v_showFrequentTravelToAsia_step'] = $this->check_status1($proj1['show_frequent_travel_to_asia'], 1);
        $data['v_show_orders_placed_last_visit_step'] = $this->check_status1($proj1['show_orders_placed_last_visit'], 1);
        $data['v_showImpt_step'] = $this->check_status1($proj1['show_trade_shows_important'], 1);

        $data['v_showspecificProducts_step'] = $this->check_status1($proj1['show_specific_product'], 1);
        $data['v_showProductClassification_step'] = $this->check_status1($proj1['show_product_classification'], 1);
        $data['v_showImportantFactor_step'] = $this->check_status1($proj1['show_important_factor'], 1);
        $data['v_showWorkshop_step'] = $this->check_status1($proj1['show_workshop_or_seminar'], 1);
        $data['v_showWorkshopVenue_step']     = $this->check_status1($proj1['show_workshop_or_seminar_venue'], 1);

        $data['v_show_upload_files_step'] = $this->check_status1($proj1['show_upload_files'], 1);

        //======= get how many steps should the preregistration should proceed =================
        //	$data['numModule'] = $this->check_status1($proj1['disable_prereg'],1);   // chek value of explode (0 - array[0] , 1 - array[1])
        //======================================================================================	  
      } // endif  $data['showall']  

    }  // end foreach($data['project'] as $proj1)


    //====================================================
    //======== mobile app from pointwest =================
    //====================================================
    //if(isset($_GET['app']) && $_GET['app']=="1")
    if (isset($data['app']) && ($data['app'] == "1" || $data['app'] == "2"))        // var app will change to 3 upon submit
    {

      $data['profiles'] = $this->site_read_model->getRec("v_contact_profile", "where email = ? limit 1", $pid, '');

      if ($data['profiles'] == "") {
        die("Record Not Found, pleaze report to SMDD (2)");
      }

      $data['pre'] = (isset($_GET['pre']) && $_GET['pre'] <> ""  ? $_GET['pre'] : "");
      $data['fn'] = (isset($_GET['fn']) && $_GET['fn'] <> ""  ? $_GET['fn'] : "");
      $data['ln'] = (isset($_GET['ln']) && $_GET['ln'] <> ""  ? $_GET['ln'] : "");
      $data['num'] = (isset($_GET['num']) && $_GET['num'] <> ""  ? $_GET['num'] : "");
      $data['co'] = (isset($_GET['co']) && $_GET['co'] <> ""  ? $_GET['co'] : "");
      $data['cntry'] = (isset($_GET['cntry']) && $_GET['cntry'] <> ""  ? $_GET['cntry'] : "");
      $data['bus'] = (isset($_GET['bus']) && $_GET['bus'] <> ""  ? $_GET['bus'] : "");
      $data['emailFrmApp'] = (isset($_GET['pid']) && $_GET['pid'] <> ""  ? $_GET['pid'] : "");
      //die($data['fn']." - ".$data['ln']);

      if ($vt == "VISITOR" || $vt == "GENERAL PUBLIC" || $vt == "GUEST") {

        if ($data['v_repre'] == "1") {

          $representation = $this->master_model->loadRec("v_reference", "where switch ='B2' and exclude=0 and sector like '%30%' order by sortfield");
          $data['ctrX'] = 0;
          foreach ($representation as $r3) {
            $chk = ($r3['c_profile'] == $data['bus'] ? TRUE : "");
            $data['data_radio1' . $data['ctrX']] = array(
              'name' => 'repre[]',
              'value' => $r3['c_code'] . "#" . $r3['c_profile'] . "#" . $r3['p_switch'] . "#" . $data['ctrX'],
              'checked' => $chk,
            );
            $data['description' . $data['ctrX']] = $r3['c_profile'];

            $data['ctrX']++;
          }
          //print_r($data['data_radio10']); die();
        }

        //     $vbustype = $this->master_model->loadRec("v_reference","where switch='B2' and sector='30' and c_profile='".$data['bus']."' limit 1");
        //     //=== delete record in sub table first =============
        //     $this->site_model->deleteRecTable("v_representationxxx",$refId,$data['fcode'],$data['sector']); 
        //     $this->site_model->insertRecTable("v_representation",$refId,$barcodeValue,$vbustype[0]['c_code'],$buss,$data['fcode'],"02");
      }

      $pid = $data['profiles'][0]['pid'];
    } else {
      //die("waz22 = ".$pid);
      if (isset($isemail) && $isemail == "1") {
        $data['profiles'] = $this->site_read_model->getRec("v_contact_profile", "where email = ? limit 1", $pid, '');
      } else {
        //$data['profiles']= $this->site_read_model->getRec("v_contact_profile","where rep_code = ? limit 1",$pid,'');

        //$pid = $data['profiles'][0]['pid']; //die("aaa=".$pid);
        $data['profiles'] = $this->site_read_model->getRec("v_contact_profile", "where pid = ? limit 1", $pid, '');
      }
    }
    //====================================================
    //====================================================


    //die('xxx= '.$data['vrs']);
    if ($data['profiles'] == "" && $data['vrs'] == "" && $data['app'] == "") { //die($isInvited);
      if ($data['isInvited'] == "no") {

        $this->endOfError("Record Not Foundz, please report to SMDD (3)");
        die("Record Not Foundz, please report to SMDD (3)");
      }
    }

    if ($data['profiles'] == "") {
      if (isset($isemail) && $isemail == "1") {
      }
      $repcode = "";      //die("xx");
      $pid = "";
      $emailCurrent = "";
      $data['vdate_apply'] = date('m/d/Y H:i', strtotime(date('Y-m-d H:i:s')));
      $data['vdate_input'] = date('m/d/Y H:i', strtotime(date('Y-m-d H:i:s')));
      $data['vgender']  = "";
      $data['vage_group']  = "";
      $data['vsalutation'] = "";
      $data['vcountry']  = "";
      $data['vregion']  = "";
      $data['vvisitor_type'] = "";
      $data['vclass'] = ($data['vclass'] == "" ? "" : $data['vclass']);
      $data['vreg_status'] = "";
      $data['vauthority_title']  = "";
      $data['remarks2'] = "";
      //$this->input->post('visitorType')
      if ($this->input->post('visitorType') == null && !isset($vt)) {
        die("VISITOR TYPE is Required... report to IT personel");
      }

      if (isset($newEmail) && $newEmail <> "") { //====== from step0 --- if email not exist 
        //die("zz=".$newEmail);
        $data['newEmail'] = $newEmail;
      }
    } else {
      foreach ($data['profiles'] as $rcode) {
        $repcode      = $rcode['rep_code'];
        $barcode      = $rcode['barcode'];
        $emailCurrent = trim($rcode['email']);
        $data['vdate_apply'] = date('m/d/Y H:i', strtotime($rcode['date_apply']));
        $data['vdate_input'] = date('m/d/Y H:i', strtotime($rcode['date_input']));
        $data['vgender']  = $rcode['gender'];
        $data['vsalutation']  = $rcode['salutation'];
        $data['vage_group']  = $rcode['age_group'];
        $data['vcountry']  = $rcode['country'];
        $data['vregion']  = $rcode['region'];
        $data['vclass']  = $rcode['buyerclass'];
        $data['vvisitor_type'] = $rcode['visitor_type'];
        $data['vauthority_title']  = $rcode['authority_title'];
        $data['vreg_status'] = ($rcode['reg_status'] == "T" ? "Yes" : ($rcode['reg_status'] == "F" ? "No" : ""));
      }
    }

    //=== MODULE ======================
    //$data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");
    //==============================k==				
    //load references	
    $data['ctry']     = $this->master_model->loadRec("v_reference", "where switch ='R1' order by c_profile");
    $data['region']     = $this->master_model->loadRec("v_reference", "where switch ='REG' order by c_profile");
    $data['TitleList'] = $this->master_model->loadRec("v_reference", "where switch='MN9' and exclude='0' order by c_profile");
    $data['rsAuthority_title'] = $this->master_model->loadRec("v_reference", "where switch ='ATITLE' and exclude='0' order by sortfield");
    //$data['dSalutation']= $this->master_model->loadRec("v_reference","where switch ='MN9' and SECTOR like '%".$data['vsector']."%' order by sortfield,c_profile");
    $data['genderRef'] = $this->master_model->loadRec("v_reference", "where switch ='B16' and sector like '%" . $data['vsector'] . "%' order by sortfield");
    $data['ageGroup'] = $this->master_model->loadRec("v_reference", "where switch ='B17' and sector like '%" . $data['vsector'] . "%' order by sortfield");

    $data['RegType'] = $this->master_model->loadRec("v_reference", "where switch='BSTAT' and sector like '%" . $data['vsector'] . "%' order by sortfield");

    //=================== SURVEY ==================================	
    //======== initialize variable ================================
    $data['jobf_data'] = null;
    $data['represent_data'] = null;
    $data['Products_data'] = null;
    $data['AnnualSales_data'] = null;
    $data['ExistingArrangement_data'] = null;
    $data['MarketSegments_data'] = null;
    $data['MarketSegments_getFcode'] = null;
    $data['visitReason_data'] = null;
    $data['visitReason_getFcode'] = null;
    $data['activity_data'] = null;
    $data['activity_getFcode'] = null;
    $data['Learnedabout_data'] = null;
    $data['Learnedabout_getFcode'] = null;
    $data['meetings_data'] = null;
    $data['Workshop_data'] = null;
    $data['show_organization_data'] = null;
    $data['show_dietary_information_data'] = null;
    $data['show_user_agreement_data1'] = null;
    $data['show_user_agreement_data2'] = null;
    $data['orders_placed_last_visit_data'] = null;

    // $faircode = strtoupper($data['fcode']);
    // if (strpos($faircode, '-VISITOR') !== false) {$faircode = trim(str_replace("-VISITOR","",$faircode));}
    // if (strpos($faircode, '-MEDIA') !== false) {$faircode = trim(str_replace("-MEDIA","",$faircode));}
    // if (strpos($faircode, '-GP') !== false) {$faircode = trim(str_replace("-GP","",$faircode));}
    // if (strpos($faircode, '-VIB') !== false) {$faircode = trim(str_replace("-VIB","",$faircode));}


    if ($data['v_show_orders_placed_last_visit'] == "1") {
      if ($this->input->post('submit1') == "finish") {
        //if(count($_POST['pconame'])<>0 && count($_POST['pproducts'])<>0 && count($_POST['pquantity'])<>0) {
        //if(!empty($_POST['pconame'])) {
        $cname = (isset($_POST['pconame']) ? $_POST['pconame'] : "");
        $cprod = (isset($_POST['pproducts']) ? $_POST['pproducts'] : "");
        $cqty = (isset($_POST['pquantity']) ? $_POST['pquantity'] : "");

        if ($cname <> "" && $cprod <> "" && $cqty <> "") {
          $this->updateOrderPlaced("v_orders_placed_last_visit", $repcode, $barcode, $faircode, $data['vsector'], $cname, $cprod, $cqty, "");
        }
        //}
        //}
      }

      $data['orders_placed_last_visit_data'] = $this->site_read_model->getRec("v_orders_placed_last_visit", "where fair_code='" . $faircode . "' AND rep_code = ? order by refno", $repcode, $data['vsector']);
    }
    //================================================================================================= 
    //================================================================================================= 
    if (isset($vt)) //========== use in REPRESENTATION if general public or not ======================
    {
      if (strtoupper($vt) == "GP" || strtoupper($vt) == "GENERAL PUBLIC" || strtoupper($vt) == "VISITOR" || strtoupper($vt) == "GUEST") {
        $nSector = "20";
      }                                       // $nSector= "30";
      elseif (strtoupper($vt) == "MEDIA") {
        $nSector = "22";
      }     // $nSector= "22";  sector 30 in repre is GUEST
      else {
        $nSector = $data['vsector'];
      }
    } else {
      $nSector = $data['vsector'];
    }


    if (isset($_POST['visitorType'])) //========== use in REPRESENTATION if general public or not
    {
      if (strtoupper($_POST['visitorType']) == "GENERAL PUBLIC" || strtoupper($_POST['visitorType']) == "VISITOR" || strtoupper($_POST['visitorType']) == "GUEST") {
        $nSector = "20";
      }   // $nSector= "30"; 
      elseif (strtoupper($_POST['visitorType']) == "MEDIA") {
        $nSector = "22";
      } else {
        $nSector = $data['vsector'];
      }
    }

    if ($data['vsector'] == "20") {
      $nSector = "20";
    }
    //================================================================================================= 
    //=================================================================================================   


    if ($data['chkShowFields']['v_jobf'] == "1") {    // $data['v_jobf']
      $data['jobf'] = $this->master_model->loadRec("v_reference", "where switch ='JOB_F' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");

      if ($this->input->post('submit1') <> "finish") {
        $data['jobf_data'] = $this->site_read_model->getRec("v_job_function", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC LIMIT 1", $repcode, $data['vsector']);
      }

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_jobf_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('jobf[]', "Role in company's purchasing activities", $vrepReq);
      }
    }

    if ($data['chkShowFields']['v_repre'] == "1" && $data['isGovernment'] <> "1") {
      $data['represent'] = $this->master_model->loadRec("v_reference", "where switch ='B2' and exclude=0 and sector like '%" . $nSector . "%' order by sortfield");

      if ($this->input->post('submit1') <> "finish") {
        $data['represent_data'] = $this->site_read_model->getRec("v_representation", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $repcode, $data['vsector']);
      }

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_repre_req'] == "1" ? "trim|required" : "");

        if ($vtype1 == "VISITOR" || $vtype1 == "GUEST" || $vtype1 == "GENERAL PUBLIC" || $vtype1 == "GP") {
          $this->form_validation->set_rules('repre[]', 'Category', $vrepReq);
        } elseif ($vtype1 == "MEDIA") {
          $this->form_validation->set_rules('repre[]', 'Representation', $vrepReq);
        } else {
          $this->form_validation->set_rules('repre[]', 'Nature of Business', $vrepReq);
        }
      }
    }


    if ($data['chkShowFields']['v_showProducts'] == "1") {
      $data['Products'] = $this->master_model->loadRec("v_reference", "where switch ='GP' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      $data['specificProd'] = $this->master_model->loadRec(MASTER_DB . ".v_product_lib", "where " . MASTER_DB . ".v_product_lib.classification='1' and " . MASTER_DB . ".v_product_lib.sector like '%" . $data['vsector'] . "%' and " . MASTER_DB . ".v_product_lib.exclude='0' ORDER BY " . MASTER_DB . ".v_product_lib.prod_cat," . MASTER_DB . ".v_product_lib.sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['Products_data'] = $this->site_read_model->getRec("v_genproducts", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $repcode, $data['vsector']);
      } // $data['previousEvent']
      // === GET LATEST EVENT RECORDS if faircode VPS_ not exist ==== 
      $data['Products_getFcode'] = $this->site_read_model->getRec("v_genproducts", "where sector like '%" . $data['vsector'] . "%' and rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC LIMIT 1", $repcode, $data['vsector']);

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_showProducts_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('showProducts[]', 'Sector/Product of Interest', $vrepReq);
      }
    }
    if ($data['v_showspecificProducts'] == "1") {
      $data['specificProducts'] = $this->master_model->loadRec("v_reference", "where switch ='GP_O' and sector like '%" . $data['vsector'] . "%' order by sortfield");
      $data['specificProducts_data'] = $this->site_read_model->getRec("v_product_requirement", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC LIMIT 1", $repcode, $data['vsector']);  //$data['previousEvent']
    }
    if ($data['chkShowFields']['v_showAnnualSales'] == "1") {
      $data['AnnualSales'] = $this->master_model->loadRec("v_reference", "where switch ='MN2' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['AnnualSales_data'] = $this->site_read_model->getRec("v_mn_annualsales", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC LIMIT 1", $repcode, $data['vsector']);
      } //$data['previousEvent']

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_showAnnualSales_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('showAnnualSales[]', "Company's annual sales volume", $vrepReq);
      }
    }

    if ($data['chkShowFields']['v_show_existing_arrangements_with_philippine_suppliers'] == "1") {
      $data['ExistingArrangement'] = $this->master_model->loadRec("v_reference", "where switch ='SUP1' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['ExistingArrangement_data'] = $this->site_read_model->getRec("v_existing_arrangement", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC ", $repcode, $data['vsector']);
      } //$data['previousEvent'] 

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_show_existing_arrangements_with_philippine_suppliers_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('showExistingArragement[]', "Existing Arragements", $vrepReq);
      }
    }


    if ($data['chkShowFields']['v_show_list_of_clients'] == "1") {
      $data['listOfClients_data'] = $this->site_read_model->getRec("v_contact_profile", "where rep_code = ?", $repcode, "");

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_show_list_of_clients_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('list_of_clients[]', "List of Clients", $vrepReq);
      }
    }

    if ($data['chkShowFields']['v_showMarketSegments'] == "1") {
      $data['MarketSegments'] = $this->master_model->loadRec("v_reference", "where switch ='MN3' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['MarketSegments_data'] = $this->site_read_model->getRec("v_mn_marketsegment", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $repcode, $data['vsector']); //$data['previousEvent']
        // === GET LATEST EVENT RECORDS if faircode not exist ====            
        $data['MarketSegments_getFcode'] = $this->site_read_model->getRec("v_mn_marketsegment", "where sector like '%" . $data['vsector'] . "%' and rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC LIMIT 1", $repcode, "");
      }

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_showMarketSegments_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('showMarketSegments[]', "Market segments", $vrepReq);
      }
    }
    if ($data['chkShowFields']['v_showReason'] == "1") {
      $data['visitReason'] = $this->master_model->loadRec("v_reference", "where switch ='MN1' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['visitReason_data'] = $this->site_read_model->getRec("v_showreason", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $repcode, $data['vsector']); //$data['previousEvent']
        // === GET LATEST EVENT RECORDS if faircode VPS_ not exist ====            
        $data['visitReason_getFcode'] = $this->site_read_model->getRec("v_showreason", "where sector like '%" . $data['vsector'] . "%' and rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC LIMIT 1", $repcode, "");
      }

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_showReason_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('showReason[]', 'Reason/s for visiting', $vrepReq);
      }
    }
    if ($data['chkShowFields']['v_show_activity_would_like_to_join'] == "1") {
      $data['activity'] = $this->master_model->loadRec("v_reference", "where switch ='ACT1' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['activity_data'] = $this->site_read_model->getRec("v_join_activity", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $repcode, "");  //$data['previousEvent']
        // === GET LATEST EVENT RECORDS if faircode VPS_ not exist ====            
        $data['activity_getFcode'] = $this->site_read_model->getRec("v_join_activity", "where sector like '%" . $data['vsector'] . "%' and rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC LIMIT 1", $repcode, "");
      }

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_show_activity_would_like_to_join_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('showactivity[]', "Activity would like to Join", $vrepReq);
      }
    }
    if ($data['chkShowFields']['v_1stTime'] == "1") {
      $data['ftime'] = $this->master_model->loadRec("v_reference", "where switch ='MN6' and sector like '%" . $data['vsector'] . "%' order by sortfield");

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_1stTime_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('firsttime', 'Frequency of visit', $vrepReq);
      }
    }

    if ($data['chkShowFields']['v_show_learnedabout'] == "1") {
      $data['Learnedabout'] = $this->master_model->loadRec("v_reference", "where switch ='B1' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['Learnedabout_data'] = $this->site_read_model->getRec("v_informthru", "where fair_code='" . $data['fcode'] . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $repcode, $data['vsector']);    //$data['previousEvent']);  
        // === GET LATEST EVENT RECORDS if faircode VPS_ not exist ====            
        $data['Learnedabout_getFcode'] = $this->site_read_model->getRec("v_informthru", "where sector like '%" . $data['vsector'] . "%' and rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC LIMIT 1", $repcode, "");
      }

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_show_learnedabout_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('show_learnedabout[]', 'Source of Information', $vrepReq);
      }
    }
    // ================= old if using v_arranged_meeting table =================
    // if ($data['v_show_interested_in_arrange_meetings'] =="1") {	
    //   $data['meetings']= $this->master_model->loadRec("v_reference","where switch ='MET1' and exclude=0 and sector like '%".$data['vsector']."%' order by sortfield");
    //   if ($this->input->post('submit1')<>"finish") 
    //     {$data['meetings_data']= $this->site_read_model->getRec("v_arranged_meeting","where fair_code='".$data['fcode']."' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC",$repcode,$data['vsector']);}

    //     if($data['vedit']<>"1") { 
    //       $this->form_validation->set_rules('showmeetings[]', "Pre-arrange Business Meetings", 'trim|required');
    //     }  
    // }
    //============================================================================
    if ($data['chkShowFields']['v_show_interested_in_arrange_meetings'] == "1") {

      $data['optionYesNo'] = $this->master_model->loadRec("v_reference", "where switch ='MET2' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");

      $data['meetings_data'] = $this->site_read_model->loadRec("v_attendance", "where fair_code ='" . $faircode . "' and rep_code= '" . $repcode . "'");

      if ($data['vedit'] <> "1") {
        $vrepReq = ($data['chkShowFields']['v_show_interested_in_arrange_meetings_req'] == "1" ? "trim|required" : "");
        $this->form_validation->set_rules('showmeetings[]', "Pre-arrange Business Meetings", $vrepReq);
      }
    }

    if ($data['chkShowFields']['v_show_require_an_interpreter'] == "1") {

      $data['optionYesNo'] = $this->master_model->loadRec("v_reference", "where switch ='MET2' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");

      if ($this->input->post('showmeetings')) {
        $tempValue1 = element(0, $this->input->post('showmeetings'));
        $tempValue2 = explode('#', $tempValue1);
        //die("aaa=".$tempValue2[0]);
      }

      if (isset($tempValue2[0]) && $tempValue2[0] == "Yes") {
        $required = 'trim|required';
        $data['meetingData'] = "Yes";
      } else {
        $required = 'trim';
      }

      if ($data['vedit'] <> "1") {
        if ($data['chkShowFields']['v_show_require_an_interpreter_req'] == "1") {
          $this->form_validation->set_rules('interpreter', "Require an Interpreter", 'trim|required');
        }
      }
    }

    if ($data['chkShowFields']['v_show_organization'] == "1") {
      $data['show_organization'] = $this->master_model->loadRec("v_reference", "where switch ='OR' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['show_organization_data'] = $this->site_read_model->getRec("v_organization", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $repcode, "");
      }

      if ($data['vedit'] <> "1") {
        if ($data['chkShowFields']['v_show_organization_req'] == "1") {
          $this->form_validation->set_rules('showorganization[]', "Organization", 'trim|required');
        }
      }
    }

    if ($data['chkShowFields']['v_show_dietary_information'] == "1") {
      $data['show_dietary_information'] = $this->master_model->loadRec("v_reference", "where switch ='DI' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['show_dietary_information_data'] = $this->site_read_model->getRec("v_dietary", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $repcode, "");
      }

      if ($data['vedit'] <> "1") {
        if ($data['chkShowFields']['v_show_dietary_information_req'] == "1") {
          $this->form_validation->set_rules('showdietaryinformation[]', "Dietary Information", 'trim|required');
        }
      }
    }

    //print_r($_POST['showWorkshop[]']);

    if ($data['v_showWorkshop'] == "1") {

      //if($data['vcountry']<>"Philippines" && $data['vsector']=="20") {$pswitch = ($data['vcountry']=="" ? "PHP" : "USD");} else {$pswitch ="PHP";}

      $pswitch = "PHP";

      $data['Workshop1'] = $this->master_model->loadRec("v_reference", "where switch ='WS1' and p_switch='" . $pswitch . "' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      $data['Workshop2'] = $this->master_model->loadRec("v_reference", "where switch ='WS2' and p_switch='" . $pswitch . "' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      $data['Workshop3'] = $this->master_model->loadRec("v_reference", "where switch ='WS3' and p_switch='" . $pswitch . "' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      $data['Workshop4'] = $this->master_model->loadRec("v_reference", "where switch ='WS4' and p_switch='" . $pswitch . "' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");

      if ($this->input->post('submit1') <> "finish") {
        $data['Workshop_data'] = $this->site_read_model->getRec("v_fair_tracks", "where fair_code='" . $faircode . "' AND rep_code = ?", $repcode, "");
      }

      if ($data['vcountry'] == "Philippines") {
        $this->form_validation->set_rules('showWorkshop[]', 'Select Payment', 'trim|required');
      }

      if (isset($_POST['showWorkshop']) && $_POST['showWorkshop'] == "0#Barkada Package#PHP#0") {
        $a = (isset($_POST['prof']) ? $_POST['prof'] : "");
        $b = (isset($_POST['stud']) ? $_POST['stud'] : "");
        $Xvalue = $a + $b;                                  // die("zzz=".$a."<br>bbb=". $b. "<br>ccc= ".$Xvalue);

        $this->form_validation->set_error_delimiters('<div style="background-color:#FF6666; color:#FFFFFF; text-align:center; padding:6px;">', '</div>');
        $this->form_validation->set_rules('vpackage', 'Bundle Package', 'callback_check_values[' . $Xvalue . ']'); // from function check_values($str)
      }

      //$data['show_epayment1']= $this->master_model->loadRec("v_reference","where switch ='pay1' and exclude=0 and sector like '%".$data['vsector']."%' order by sortfield"); 
    }

    //echo '<pre>';
    //print_r($_POST['showWorkshop'][0]);
    //echo '</pre>';  

    if ($data['v_disable_epayment'] == "0") {
      $data['show_epayment1'] = $this->master_model->loadRec("v_reference", "where switch ='pay1' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      $data['show_epayment2'] = $this->master_model->loadRec("v_reference", "where switch ='pay2' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      $data['show_epayment3'] = $this->master_model->loadRec("v_reference", "where switch ='pay3' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['show_epayment_data'] = $this->site_read_model->getRec("v_payment_online", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $repcode, "");
      }

      if (isset($_POST["onlinepayment"]) && $_POST["onlinepayment"] == "1") {
        //echo "<script>alert('yes');</script>";
        $this->form_validation->set_rules('epayment[]', "Mode of Paymnet", 'trim|required');
      } else {
        //echo "<script>alert('no');</script>";
      }
    }


    if ($data['v_showWorkshopVenue'] == "1") {

      $data['WorkshopVenue'] = $this->master_model->loadRec("v_reference", "where switch ='venue' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      $data['WorkshopVenue_data'] = $this->site_read_model->getRec("v_fair_tracks_venue", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC LIMIT 1", $repcode, $data['vsector']);
    }

    if ($data['chkShowFields']['v_show_user_agreement'] == "1") {
      $data['show_user_agreement1'] = $this->master_model->loadRec("v_reference", "where switch ='UA1' and exclude=0 and sector like '%" . $data['vsector'] . "%' order by sortfield");
      if ($this->input->post('submit1') <> "finish") {
        $data['show_user_agreement_data1'] = $this->site_read_model->getRec("v_user_agreement", "where fair_code='" . $faircode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $repcode, $data['vsector']);
      }

      if ($data['vedit'] <> "1") {
        if ($data['chkShowFields']['v_show_user_agreement_req'] == "1") {
          $this->form_validation->set_rules('showuseragreement1[]', "Confirmation", 'trim|required');
        }
      }
      if ($data['vedit'] <> "1") {
      }

      //$data['show_user_agreement2']= $this->master_model->loadRec("v_reference","where switch ='UA2' and exclude=0 and sector like '%".$data['vsector']."%' order by sortfield");
      //if ($this->input->post('submit1')<>"finish") 
      //  {$data['show_user_agreement_data2']= $this->site_model->getRec("v_user_agreement","where fair_code='".$faircode."' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC",$repcode,"");}
      //$this->form_validation->set_rules('showuseragreement2[]', "Confirmation", 'trim|required');
    }

    //=================== SURVEY ==================================	

    //======= get visitor_type [TRADE BUYER/VISITOR] in attendance table =============================	 		
    //======= get visitor_type [TRADE BUYER/VISITOR] in attendance table =============================


    $data['typeAttendance'] = $this->site_read_model->getRec("v_attendance", "where fair_code='" . $faircode . "' and sector='" . $data['vsector'] . "' and rep_code = ? limit 1", $repcode, '');
    //$tempVisitortype =""; 
    //print_r($data['typeAttendance']); die();

    if ($data['typeAttendance'] <> "" && $data['typeAttendance'][0]['rep_code'] <> 0) {
      foreach ($data['typeAttendance'] as $rvtype) {
        $visitorType     = $rvtype['visitor_type'];            //trade buyer,visitor,media
        $visitorStatus   = $rvtype['visitor_status'];          //regular,new
        $visitorStatusRem = $rvtype['visitor_status_remarks'];  //if regular last fair attended	  
      }
      $existAttendance = "1";    //die("aa");
    } else  //======== not in attendance table / get registered previous event =======
    {
      //die("watup");
      //$data['typeAttendance']= $this->site_model->getRec("v_attendance","where fair_code='".strtoupper($previousEvent)."' and sector='".$data['vsector']."' and rep_code = ?",$repcode,'');
      $data['typeAttendance'] = $this->site_read_model->getRec("v_attendance", "where sector='" . $data['vsector'] . "' and rep_code = ?" . " ORDER BY fair_code DESC limit 1", $repcode, '');
      if ($data['typeAttendance'] <> "" && $data['typeAttendance'][0]['rep_code'] <> 0) {
        foreach ($data['typeAttendance'] as $rvtype) {
          $visitorType     = $rvtype['visitor_type'];           //trade buyer,visitor,media
          $visitorStatus   = $rvtype['visitor_status'];         //regular,new
          $visitorStatusRem = $rvtype['visitor_status_remarks']; //if regular last fair attended
        }

        foreach ($data['typeAttendance'] as &$value)            // notice the & symbol. It gives you ability to change the array
        {
          $value['validated'] = "0";
          $value['buyerclass'] = "";
        }
      } else { //die("none");
        $visitorType     = "";
        $visitorStatus   = "";
        $visitorStatusRem = "";
      }
      $existAttendance = "0";
    }   //======== visitor to verify profile and add current faircode to attendance if none ======

    //die("aaa = ".$existAttendance);

    if (isset($vt)) // vt required if for viewing profile
    {
      switch (strtoupper($vt)) {
        case "TB":
          $visitorType = "TRADE BUYER";
          break;
        case "VISITOR":
          $visitorType = "VISITOR";
          break;
        case "GUEST":
          $visitorType = "GUEST";
          break;
        case "MEDIA":
          $visitorType = "MEDIA";
          break;
        case "GP":
        case "GENERAL PUBLIC":
          $visitorType = "GENERAL PUBLIC";
          break;
      }
    }

    if ($this->input->post('visitorType') <> "") {
      $visitorType = $this->input->post('visitorType');
    }

    //==========================================================================

    //======= disable update records if validated in attendance table====
    $data['mfo_validated'] = $this->site_read_model->getRec("v_attendance", "where validated=1 and fair_code='" . $faircode . "' and rep_code = ?", $repcode, '');
    if ($data['mfo_validated'] == "") {
      $data['enableUpdate'] = "T";
    } else {
      $data['enableUpdate'] = "F";
    }
    if ($data['vrs'] <> "") {
      $data['enableUpdate'] = "T";
    }  // allow edit if vrs<>""   
    //===================================================================	 

    $data['vtype'] = $visitorType; //die("aa= ".$visitorType);

    if ($data['vtype'] == 'TRADE BUYER') {
      //======= get how many steps should the preregistration should proceed =================
      //$data['numModule'] = $this->check_status1($proj1['disable_prereg'],1);   // chek value of explode (0 - array[0] , 1 - array[1])
      //======================================================================================
    } else {

      //$data['numModule'] = $this->check_status1($proj1['disable_prereg'],1);
    }

    //==== used to display all survey in form ===================
    //==== used to view profile of registered visitor ===========
    if (isset($data['showall']) && $data['showall'] == "1") {
      //======= get how many steps should the preregistration should proceed =================	  
      $data['numModule'] = "1";
      //======================================================================================	   
    }

    /*$this->load->helper(array('form', 'url'));       //create the form opening   
   $this->load->library(array('form_validation','session'));   
   $this->form_validation->set_error_delimiters('<div style="background-color:#FF6666">', '</div>'); //globally change the error delimiters */


    //if(isset($_POST['firsttime'])) {echo($_POST['firsttime']);}   


    $data['step'] = "step2";
    $data['stepMsg'] = 1; //page
    $data['tempCount'] = 1;
    //$data['stepMsg'] = 2;

    if ($data['vtype'] == 'TRADE BUYER') {
      $data['stepImg'] = "step2.jpg";
    }
    //else {$data['stepImg'] = "step2_visitor.jpg";}

    //================== VALIDATION ===============================================

    if ($data['v_show_orders_placed_last_visit'] == "1") {
      if (isset($_POST['firsttime']) && $_POST['firsttime'] == "REGULAR")  // && isset($_POST['pconame']))
      {
        //$this->form_validation->set_rules('pconame[0]', 'Company Name', 'required|trim');
        // $this->form_validation->set_rules('pproducts[]', 'Products', 'required|trim');
        // $this->form_validation->set_rules('pquantity[]', 'Quantity', 'required|trim');
      }
    }


    //========= VALIDATION FOR VOUCHER ================================
    if ($data['chkShowFields']['v_hotel_voucher'] == "1" && $data['isGovernment'] <> "1")   //== $data['chkShowFields'] 
    {


      //$data["onsite"] = (isset($_GET['onsite']) ? $_GET['onsite'] : ( isset($_POST['onsite']) ? $_POST['onsite'] : "" ) );

      if ($data['chkShowFields']['v_hotel_voucher_req'] == "1") {             //die("aaa=". $data["v_hotel_voucher_req"]);

        if ($data["allow"] <> "1") {

          $this->form_validation->set_rules('voucher', 'Promo Code', 'required');
        }
      }

      //$data['voucher_code'] = (isset($_POST['voucher']) ? $this->input->post('voucher') : (isset($_GET['voucher']) ? $_GET['voucher'] : "") ); 
      $data['voucher_code'] = (isset($_POST['voucher']) ? $this->input->post('voucher') : (isset($_GET['pcode']) ? $_GET['pcode'] : (isset($data['promoCode']) ? $data['promoCode'] : "")));

      $data['withInvite'] = (isset($_POST['invi']) ? $this->input->post('invi') : (isset($_GET['pcode']) ? "1" : (isset($data['promoCode']) && $data['promoCode'] <> "" ? "1" : "")));

      if ($data['onSiteReg'] == "1") {
        $data['withInvite'] = "0";
      }

      //die("ccc=".$data['withInvite']."<br>pcode=".$_GET['pcode']."<br>promo=".$data['promoCode']);

      if ($data['voucher_code'] <> "") {
        //  $data['withInvite'] = (isset($_POST['invi']) ? $this->input->post('invi') : (isset($_GET['invi']) ? $_GET['invi'] : "") ); 
        //  $data['withInvite'] = (isset($_POST['invi']) ? $this->input->post('invi') : "" ); 

        //die("ccccs=".$data['voucher_code']);	

        //$data['withInvite'] = (isset($_POST['invi']) ? $this->input->post('invi') : (isset($_GET['pcode']) ? "1" : (isset($data['promoCode']) ? "1" : "")) );

        //$data['allowEmailInput']

        //$RequiredCode = ($data['vrs']=="3" ? "required" : "");
        //$this->form_validation->set_rules('voucher', "Voucher", $RequiredCode);   

        //$this->form_validation->set_rules('voucher', 'Voucher', 'alpha_numeric');

        $Xcode = $repcode . '_' . $data['vsector'] . "_" . $data['vrs'] . "_" . $data['chkShowFields']['v_hotel_voucher_req'] . "_" . $data['emailto_registration'];
        //die("aa=".$Xcode);
        $this->form_validation->set_rules('voucher', 'Voucher', 'callback_voucher_check[' . $Xcode . ']'); // from function voucher_check($str)
      }
    }

    //die("aaa = ".$existAttendance);

    //========= VALIDATION FOR Date Arrival ===========================
    if (isset($_POST['vrs_disable_date_apply']) && $_POST['vrs_disable_date_apply'] == "1") {
      $this->form_validation->set_rules('date_apply', 'Arrival Date', 'required');
    }
    //================================================================= 

    //========= VALIDATION FOR Date Arrival ===========================
    if (isset($_POST['vrs_disable_visitor_type']) && $_POST['vrs_disable_visitor_type'] == "1") {
      $this->form_validation->set_rules('visitor_type', 'Visitor Type', 'required');
    }
    //=================================================================      

    //========= VALIDATION FOR Date Arrival ===========================
    if (isset($_POST['vrs_disable_reg_status']) && $_POST['vrs_disable_reg_status'] == "1") {
      $this->form_validation->set_rules('reg_status', 'Arrival Status', 'required');
    }
    //=================================================================  


    //================================================================= 
    //========= Check EMAIL ===========================================   
    //if ($this->check_status1($data['project'][0]['show_email'],3)=="1" && $this->check_status1($data['project'][0]['show_email'],0)=="1") 
    if ($data['chkShowFields']['req_email'] == "1") {
      // die("111");
      //if ($emailCurrent <> trim($this->input->post('email')) && $data['vrs']=="" && $data['app']=="" ) {    //in VRS allow email if not unique


      $vstr = explode("_", $data['project'][0]['show_email']);  // ====== format must have 3
      //$chk = count($vstr);  die("zzz= ".$vstr[0]);

      if ($vstr[0] == 0) {
        $is_unique = '';    //die("111");
      } else {

        if ($emailCurrent <> trim($this->input->post('email'))) {      // $this->check_status1($data['project'][0]['show_email'],2)=="1" && 

          $is_unique = '|is_unique[v_contact_profile.email]';    //die("aaa");

        } elseif ($data['vedit'] == "2") {                                  // $this->check_status1($data['project'][0]['show_email'],2)=="1" && 

          $is_unique = '|is_unique[v_contact_profile.email]';    //die("bbb");      
        } else {

          $is_unique = '';    //die("ccc");
        }
      }

      if ($data['vrs'] == "1") {
        $vrequired = 'required|';
      }                               // dati --- $vrequired = ''
      else {
        $vrequired = 'required|';
      }

      if ($data['vtype'] == "VISITOR") {
        $vrequired = 'required|';
      } //email optional if VISITOR
      if ($data['vtype'] == "GUEST") {
        $vrequired = 'required|';
      }

      $this->form_validation->set_message('is_unique', '%s already registered.');
      $this->form_validation->set_rules('email', 'Email', $vrequired . 'valid_email|trim' . $is_unique);  // add to chk if exist 
    } else {

      $this->form_validation->set_rules('email', 'Email', 'valid_email|trim');  // add to chk if exist 

    }
    //================================================================= 


    $requireThis = ($data['vsector'] == "25" ? "25" : $data['pticRefer']);

    $this->validation_step1($data['vtype'], $data['vsector'], $requireThis, $data['project'], $data['vedit'], $data['chkRequired']);


    if ($data['vrs'] <> "1" && $data['vedit'] <> "1")     // valuss "1" is vrs without validation  ==== valuss "2" is vrs with validation
    {

      // die("aaa= ". $_POST['country']);
      $data['isPhilippines'] = (isset($_POST['country']) ? $_POST['country'] : "");

      $this->validation_step2($visitorType, $data['bcard'], $requireThis, $data['project'], $data['isPhilippines'], $data['chkRequired']);
    }


    //============ check if UPLOAD FILE IS ENABLED ====================
    //============ check if pticRefer enabled ========================== 
    //=================================================================


    if (isset($_GET['back'])) {
      $data['back'] = $_GET['back'];
    }


    //if ($pregClose=="1")
    //{
    // $this->load->view('formsuccess', $data); // pre-registration CLOSE
    //}
    //else
    //{

    if ($this->form_validation->run() == FALSE)  // runs the validation routine
    {
      // include || $data['msg']=='Mismatch, try again' for captcha 

      //===== if CREATEPHILS ==============
      //die($data['vtype']);
      //die($data['vsector']);
      $chk1newwindow = ((isset($_GET['newwindow']) && $_GET['newwindow'] == "no") ? "no" : "yes");

      $chk2newwindow = ((isset($_POST['newwindow']) && $_POST['newwindow'] == "no") ? "no" : "yes");

      $enableFormFields = $this->session->userdata('enable_formFields') ? true : false;
      

      //die("aaa=".$chk1newwindow."br"."bbb=".$chk2newwindow);
      
      //if(isset($_GET['newwindow']) && $_GET['newwindow']=="no")
      if ($chk1newwindow == "no" || $chk2newwindow == "no") {
        
        //$set_data = $this->session->all_userdata();
        //$data = sessionRights($set_data);  

        if ($data['vrs_diy_visitor_print'] == "") {
          $data['disableVisitPrint'] = "";
        } else {
          $data['disableVisitPrint'] = "&autoprint=no";
        }
        $data['header1'] = "Registration Form";

        $data['newwindow'] = "no";
        // $data['enable_formFields'] = $enableFormFields;

        $this->load->view('vrs_form_template', $data);

        // print_r($data);
        // die();
      } else {

        if ($data['vsector'] == "01") {
          if ($data['vtype'] == "GUEST" || $data['vtype'] == "GENERAL PUBLIC" || $data['vtype'] == "MEDIA") {
            $this->load->view('vrs_form_template_content', $data);         // STEP 2  $data['vsector'] == "01"    registration_form_ifex  registration_form_new
          } else {
            $this->load->view('vrs_form_template_content', $data);             // registration_form_new 
          }
        } else {
          $this->load->view('vrs_form_template_content', $data);                // registration_form_new
        }
      }


    } else {
      //================================================================================		
      //==== VRS use =====================================================================
      //================================================================================
      //die("aaa=".$data['pticRefer']);		

      if ($data['vedit'] == "2") {
        $pid = "";
        $existAttendance = "0";
      }    // ====== add_person selected



      if ($pid == "") {  //die("aaa=".$pid);

        $pid = $this->generatePID();
        $data['pid'] = $pid;
        //$refId = $this->site_model->insertRecord("v_regcode");  // ==== get new REP_CODE =========
        //die("aaa=".$refId);
        if ($data['refNumberForPackage'] == "") {
          //$refId = $this->site_model->insertRecord("v_regcode");  // ==== get new REP_CODE =========              
          //$this->site_model->insertRecStep1_registration($pid,'v_contact_profile');

          $refId = $this->site_model->insertRecStep1_registration($pid, 'v_contact_profile');
          $barcode = $refId;  // + 2000000;
        } else {
          //die("ssss");
          $refId = $this->site_model->insertRecStep1_registration($pid, 'v_contact_profile');
          $barcode = $refId;  // + 2000000;
          //die("aaass=".$refId);
        }
        $repcode = $refId;

        $this->site_model->updateBarcode('v_contact_profile', $barcode, "pid = '" . $pid . "'");
        $this->site_model->updateRecord('v_contact_profile', 'rep_code', $repcode, "pid = '" . $pid . "'");
        $this->site_model->updateRecord('v_contact_profile', 'venue', $data['venue'], "pid = '" . $pid . "'");

        //============== for CREATE PHILS (update buyerclass field) ==============================================
        //if($data['vsector']=="20" && $data['workshop']<>"") { $this->site_model->updateRecord('v_contact_profile','buyerclass',$data['workshop'],"pid = '".$pid."'"); }

        //$this->site_model->updateRecord('v_contact_profilexxxxx','buyerclass',$data['workshop'],"pid = '".$pid."'"); }
        //========================================================================================================  
        //die("aaa");        
        // } 
      }
      //================================================================================
      //================================================================================ 



      if ($query = $this->site_model->updateRecStep2('v_contact_profile', "pid = '" . $pid . "'")) {

        // ======== CHECK IF EMAIL IS DELETED... "UNDELETE" IF FROM PRE-REG =================
        // ======== CHECK IF EMAIL IS DELETED... "UNDELETE" IF FROM PRE-REG =================

        // if() {
        //   $this->site_model->updateField("v_contact_profile","deleted","0","pid = '".$pid."'");
        // }

        // ======== CHECK IF EMAIL IS DELETED... "UNDELETE" IF FROM PRE-REG =================


        //========= VALIDATION FOR Date Arrival ===========================
        if (isset($_POST['vrs_disable_date_apply']) && $_POST['vrs_disable_date_apply'] == "1") {
          $this->site_model->updateField("v_contact_profile", "date_apply", date('Y-m-d H:i:s', strtotime($_POST['date_apply'])), "pid = '" . $pid . "'");
          date('m/d/Y H:i', strtotime($rcode['date_apply']));
        }
        //================================================================= 

        //========= VALIDATION FOR Date Arrival ===========================
        if (isset($_POST['vrs_disable_visitor_type']) && $_POST['vrs_disable_visitor_type'] == "1") {
          $this->site_model->updateField("v_contact_profile", "visitor_type", $_POST['visitor_type'], "pid = '" . $pid . "'");
        }
        //=================================================================      

        //========= VALIDATION FOR Date Arrival ===========================
        if (isset($_POST['vrs_disable_reg_status']) && $_POST['vrs_disable_reg_status'] == "1") {
          $regStatValue = ($_POST['reg_status'] == "Yes" ? "T" : "F");
          $this->site_model->updateField("v_contact_profile", "reg_status", $regStatValue, "pid = '" . $pid . "'");
        }
        //=================================================================  

        //============== update DATE_APPLY if preregistered ===================
        if ($data['updateIfPrereg'] <> "") {
          $this->site_model->updateField("v_contact_profile", "date_apply", date('Y-m-d H:i:s'), "pid = '" . $pid . "'");
        }
        //=====================================================================


        $v_userAgreement = "";
        if ($this->input->post('showuseragreement1[]') != null || $this->input->post('showuseragreement1[]') <> "") {

          $temp1 = implode("", $this->input->post('showuseragreement1[]'));
          $temp2 = explode("#", $temp1);
          $v_userAgreement = $temp2[1];
        }

        if ($data['vedit'] <> "1")         // if edit(edit=1 frm vrsedit()) do not insert faircode if not exist
        {
          if ($existAttendance == "0") // chk if rec exist in attendance table
          {
            $this->site_model->insertRecTable('v_attendance', $repcode, $barcode, '', '', strtoupper($data['fcode']), $data['vsector']);
            $this->site_model->updateRecTable('v_attendance', 'date_apply', date('Y-m-d H:i:s'), strtoupper($data['fcode']), $data['vsector'], $repcode);

            $vregStatus = ($data['chkShowFields']['auto_tag_reg_status'] == "1" ? "T" : "F");

            $this->site_model->updateRecTable('v_attendance', 'reg_status', $vregStatus, strtoupper($data['fcode']), $data['vsector'], $repcode);
            $this->site_model->updateField("v_contact_profile", "reg_status", $vregStatus, "pid = '" . $pid . "'");
          }

          // ================ CHK for generic code (HOME FOOD SSX GENERAL CREATE) ========================================
          // ================ CHK for generic code (HOME FOOD SSX GENERAL CREATE) ========================================
          $chkGenericFaircode = $this->site_read_model->getRec("v_attendance", "where fair_code='" . $genericFaircode . "' and rep_code = ?" . " ORDER BY fair_code DESC limit 1", $repcode, '');
          if ($chkGenericFaircode == "") {
            $this->site_model->insertRecTable('v_attendance', $repcode, $barcode, '', '', $genericFaircode, $data['vsector']);
            $this->site_model->updateRecTable('v_attendance', 'visitor_type', $data['vtype'], $genericFaircode, $data['vsector'], $repcode);
            $this->site_model->updateRecTable('v_attendance', 'visitor_status', $this->input->post('firsttime'), $genericFaircode, $data['vsector'], $repcode);
            $this->site_model->updateRecTable('v_attendance', 'date_apply', date('Y-m-d H:i:s'), $genericFaircode, $data['vsector'], $repcode);
            $this->site_model->updateRecTable('v_attendance', 'user_agreement', $v_userAgreement, $genericFaircode, $data['vsector'], $repcode);

            $this->site_model->updateRecTable('v_attendance', 'reg_status', 'T', $genericFaircode, $data['vsector'], $repcode);
          }
        }
        // ============================================================================================================


        //================= Mobile APP used ===========================================================================  
        if (isset($data['app']) && $data['app'] == "1")  // from registration_form_new.php
        {
          $this->site_model->updateField("v_contact_profile", "exhi_conf_gov", "MOBILEAPP", "pid = '" . $pid . "'");
          $this->site_model->updateField("v_contact_profile", "reg_status", "F", "pid = '" . $pid . "'");
          $this->site_model->updateRecTable('v_attendance', 'remarks', "MOBILEAPP", strtoupper($data['fcode']), $data['vsector'], $repcode);
        }
        //==================================================================================================== 

        $this->site_model->updateRecTable('v_attendance', 'visitor_type', $data['vtype'], strtoupper($data['fcode']), $data['vsector'], $repcode);
        $this->site_model->updateRecTable('v_attendance', 'date_input', date('Y-m-d H:i:s'), strtoupper($data['fcode']), $data['vsector'], $repcode);
        $this->site_model->updateRecTable('v_attendance', 'user_agreement', $v_userAgreement, strtoupper($data['fcode']), $data['vsector'], $repcode);

        if (isset($_POST['date_apply'])) {
          $this->site_model->updateRecTable('v_attendance', 'date_apply', date('Y-m-d H:i:s', strtotime($_POST['date_apply'])), strtoupper($data['fcode']), $data['vsector'], $repcode);
        } else {
          if (isset($_POST['vedit']) && $_POST['vedit'] <> "1") {
            $this->site_model->updateRecTable('v_attendance', 'date_apply', date('Y-m-d H:i:s'), strtoupper($data['fcode']), $data['vsector'], $repcode);
          }
        }

        //$this->site_model->updateRecTable('v_attendance','visitor_status',$visitorStatus,strtoupper($data['fcode']),$data['vsector'],$repcode);			
        //$this->site_model->updateRecTable('v_attendance','visitor_status_remarks',$visitorStatusRem,strtoupper($data['fcode']),$data['vsector'],$repcode);			  

        //============================================================================================================
        //====================== Barkada package insert Ref Code from payment ========================================
        if ($data['refNumberForPackage'] <> "") {
          $this->site_model->updateRecTable('v_contact_profile', 'add_value', $data['refNumberForPackage'], "", "", $repcode);
          $this->site_model->updateRecTable('v_attendance', 'pre_reg', 'P', strtoupper($data['fcode']), $data['vsector'], $repcode);
          $this->site_model->updateRecTable("v_attendance", "date_apply", date('Y-m-d H:i:s'), strtoupper($data['fcode']), $data['vsector'], $repcode);
        }
        //============================================================================================================


        if ($data['vrs'] == "" || $data['vrs'] == "3") {
          $this->site_model->updateRecTable('v_attendance', 'valid_email', '1', strtoupper($data['fcode']), $data['vsector'], $repcode);
        } else {
          if ($data['vsector'] <> "20") {
            //$this->site_model->updateRecTable('v_contact_profile','remarks',$data['eventGoin'],"","",$repcode);
          }

          if ($data['vclass'] <> "") {
            $this->site_model->updateRecTable('v_attendance', 'buyerclass', $data['vclass'], strtoupper($data['fcode']), $data['vsector'], $repcode);
            $this->site_model->updateRecTable('v_contact_profile', 'buyerclass', $data['vclass'], "", "", $repcode);
            //$this->site_model->updateRecTable('v_contact_profile','buyer_type',"NEW","","",$repcode);
          } else {
            $this->site_model->updateRecTable('v_attendance', 'buyerclass', $data['vib1'], strtoupper($data['fcode']), $data['vsector'], $repcode);
            $this->site_model->updateRecTable('v_contact_profile', 'buyerclass', $data['vib1'], "", "", $repcode);
          }
        }


        if ($data['pticRefer'] == "REGULAR") { //die("aaa=".$data['pticRefer']); 
          $vrem = "Prefer as Regular TRADE BUYER";
          //$vrem ="Prefer";
          $this->site_model->updateRecTable('v_attendance', 'buyerclass_remarks', $vrem, strtoupper($data['fcode']), $data['vsector'], $repcode);
          $this->site_model->updateRecTable('v_contact_profile', 'exhi_conf_gov', $vrem, "", "", $repcode);     // FOR buyer class remarks

          $this->site_model->updateRecTable('v_attendance', 'buyerclass', "", strtoupper($data['fcode']), $data['vsector'], $repcode);
          $this->site_model->updateRecTable('v_contact_profile', 'buyerclass', "", "", "", $repcode);

          $this->site_model->updateRecTable('v_users_refer_buyer', 'rep_coname', $this->input->post('coname'), "", "", $repcode);
          $this->site_model->updateRecTable('v_users_refer_buyer', 'rep_lname', $this->input->post('lname'), "", "", $repcode);
          $this->site_model->updateRecTable('v_users_refer_buyer', 'rep_fname', $this->input->post('fname'), "", "", $repcode);
          $this->site_model->updateRecTable('v_users_refer_buyer', 'rep_email', $this->input->post('email'), "", "", $repcode);
        }
        if ($data['pticRefer'] == "REFER") {
          $this->site_model->updateRecTable('v_attendance', 'validated', "1", strtoupper($data['fcode']), $data['vsector'], $repcode);

          $this->site_model->updateRecTable('v_users_refer_buyer', 'rep_coname', $this->input->post('coname'), "", "", $repcode);
          $this->site_model->updateRecTable('v_users_refer_buyer', 'rep_lname', $this->input->post('lname'), "", "", $repcode);
          $this->site_model->updateRecTable('v_users_refer_buyer', 'rep_fname', $this->input->post('fname'), "", "", $repcode);
          $this->site_model->updateRecTable('v_users_refer_buyer', 'rep_email', $this->input->post('email'), "", "", $repcode);
        }

        $this->site_model->updateRecTable('v_contact_profile', 'org_name', $this->input->post('org_name'), "", "", $repcode);
        $this->site_model->updateRecTable('v_contact_profile', 'org_website', $this->input->post('org_website'), "", "", $repcode);
        $this->site_model->updateRecTable('v_contact_profile', 'salutation', $this->input->post('salutation'), "", "", $repcode);
        //$this->site_model->updateRecTable('v_contact_profile','buyerclass',$data['vclass'],"","",$repcode); 

        switch ($this->input->post('salutation')) {
          case "Mr.":
            $this->site_model->updateRecTable('v_contact_profile', 'gender', "Male", "", "", $repcode);
            break;
          default:
            $this->site_model->updateRecTable('v_contact_profile', 'gender', "Female", "", "", $repcode);
        }

        //die("aaa=".$data['vtype']);
        //======= CREATE CSV for pointwest APP ===================================================
        //======= CREATE CSV for pointwest APP ===================================================
        //======= CREATE CSV for pointwest APP ===================================================
        if ($data['vtype'] == 'TRADE BUYER' || $data['vtype'] == 'TB') {
          // $this->createcsv($this->input->post('email'),$this->input->post('email2'),$barcode);
        }

        //========================================================================================
        //========================================================================================
        //========================================================================================  

        //=======  CREATE HISTORY DATA FROM contact_profile =================
        if ($data['profiles'] <> "") {
          foreach ($data['profiles'] as $history) {
            $query = $this->site_model->insertHistory('v_contact_history', $history['rep_code'], $history['co_name'], $history['cont_per_ln'], $history['cont_per_fn'], $history['position'], $history['add_st'], $history['add_city'], $history['zipcode'], $history['country'], $history['continent'], $history['tel_off'], $history['mobile'], "", $history['webpage'], $history['email'], $history['visitor_type'], $history['sector'], strtoupper($data['fcode']), $history['gender'], $history['age_group']);
          }
        }
        //===================================================================


        //if($data['v_hotel_voucher'] == "1")
        if ($data['chkShowFields']['v_hotel_voucher'] == "1" && $data['isGovernment'] <> "1")  //$data['v_hotel_voucher']
        {
          if ($data['voucher_code'] <> "") {
            $this->site_model->updateRecord('v_voucher_code', 'rep_code', $repcode, "item_code = '" . $data['voucher_code'] . "'");
            $this->site_model->updateRecord('v_voucher_code', 'barcode', $barcode, "item_code = '" . $data['voucher_code'] . "'");
            $this->site_model->updateRecord('v_voucher_code', 'status', 'U', "item_code = '" . $data['voucher_code'] . "'");
            //=== skip below since updated by separate system =======================================================================
            //=== later include in reference assigning the faircode in voucher table ================================================

            //$this->site_model->updateRecord('v_voucher_code','fair_code',$faircode,"item_code = '".$data['voucher_code']."'");
            //$this->site_model->updateRecord('v_voucher_code','sector',$data['vsector'],"item_code = '".$data['voucher_code']."'");
            //=======================================================================================================================

            // ============= for PROMO CODES ========================================================================================
            if (isset($data['voucher_code']) && $data['voucher_code'] <> "") {
              $this->site_model->updateRecTable('v_attendance', 'item_code', $data['voucher_code'], strtoupper($data['fcode']), $data['vsector'], $repcode);
            }
          }
        }


        //updateOrderPlaced($vtable,$rcode,$vbarcode,$vfcode,$vsec,$vfield1,$vfield2,$vfield3,$vremarks)  

        if ($data['chkShowFields']['v_1stTime'] == "1")   // || $data['v_1stTime'] =="0"
        {
          $this->site_model->updateRecStep3('v_contact_profile', "pid = '" . $pid . "'");
          if ($data['vrs'] == "" || $data['vrs'] == "3") {
            $this->site_model->updateRecTable('v_attendance', 'pre_reg', 'P', strtoupper($data['fcode']), $data['vsector'], $repcode);
          }
          $this->site_model->updateRecTable('v_attendance', 'visitor_status', $this->input->post('firsttime'), strtoupper($data['fcode']), $data['vsector'], $repcode);
          $this->site_model->updateRecTable('v_attendance', 'visitor_status_remarks', $this->input->post('attendLast'), strtoupper($data['fcode']), $data['vsector'], $repcode);
          $this->site_model->updateRecTable('v_attendance', 'date_input', date('Y-m-d H:i:s'), strtoupper($data['fcode']), $data['vsector'], $repcode);
        }

        // ==== job function ================================================

        if ($data['chkShowFields']['v_jobf'] == "1")     // $data['v_jobf']
        {
          $this->updateSurvey('v_job_function', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'jobf', 'other_jobf');
        }

        // ==== representation ==============================================
        //if ($data['v_repre'] =="1")
        if ($data['chkShowFields']['v_repre'] == "1" && $data['isGovernment'] <> "1") {
          $this->updateSurvey('v_representation', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'repre', 'other_buss');

          //if($data['refNumberForPackage']=="")
          //print_r($this->input->post('repre'));
          //die("xxx=");
          if ($data['vrs'] <> "" && $data['vsector'] <> "20") {
            $x = 0;
            $aaa = "";
            if (is_array($this->input->post('repre'))) {

              $vcnt = count($this->input->post('repre')) - 1;
              for ($x = 0; $x <= $vcnt; $x++) {

                $tempvalue1 = element($x, $this->input->post('repre'));
                $itemcode1  = explode('#', $tempvalue1);
                $aaa = $aaa . $itemcode1[1] . "|" . $itemcode1[0];      // ==== ORIG ---- > $aaa = $aaa.$itemcode1[1]."|";

              }
            }

            $this->site_model->updateRecord('v_contact_profile', 'remarks', $aaa, "pid = '" . $pid . "'");
          }
        }

        if ($data['isGovernment'] == "1") {

          $vitemCode = 'B2Q';
          $vrem = 'Government';
          $this->site_model->insertRecTable('v_representation', $repcode, $barcode, $vitemCode, $vrem, strtoupper($data['fcode']), $data['vsector']);
          $this->site_model->updateRecord('v_contact_profile', 'remarks', $vrem, "pid = '" . $pid . "'");
        }


        // ==== show interested in arrange meetings ====
        if ($data['chkShowFields']['v_show_interested_in_arrange_meetings'] == "1") {
          //$this->updateSurvey('v_arranged_meeting',$repcode,$barcode,strtoupper($data['fcode']),$data['vsector'],'showmeetings','other_meetings'); 
          $this->site_model->updateRecTable('v_attendance', 'pre_arranged_meeting', $this->input->post('showmeetings[0]'), strtoupper($data['fcode']), $data['vsector'], $repcode);
        }

        if ($data['chkShowFields']['v_show_require_an_interpreter'] == "1") {

          // $vshowmeetings = explode("#",$this->input->post('showmeetings[0]'));
          // $needInterpreter = explode("#",$this->input->post('interpreter'));
          // $valueInterpreter = (($vshowmeetings[0]=="Yes") ? $needInterpreter[0] : "");
          // $this->site_model->updateRecTable('v_contact_profile','specific_request',$valueInterpreter,"","",$repcode);

          $this->site_model->updateRecTable('v_attendance', 'interpreter', $this->input->post('interpreter'), strtoupper($data['fcode']), $data['vsector'], $repcode);
        }

        // ==== learn about =======

        if ($data['chkShowFields']['v_show_learnedabout'] == "1") {
          $this->updateSurvey('v_informthru', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'show_learnedabout', 'other_Learnedabout');
        }

        if ($data['chkShowFields']['v_showProducts'] == "1") {
          $this->updateSurveyProd('v_genproducts', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'showProducts', 'other_Product');
        }

        // ==== show specific product ===========
        if ($data['v_showspecificProducts'] == "1") {
          $this->site_model->deleteRecTable('v_product_requirement', $repcode, strtoupper($data['fcode']), $data['vsector']);
          if (trim($this->input->post('remarks_major')) <> "") {
            $this->site_model->insertRecTable('v_product_requirement', $repcode, $barcode, $this->input->post('prod_cat'), $this->input->post('remarks_major'), strtoupper($data['fcode']), $data['vsector']);
          }
        }

        if ($data['chkShowFields']['v_show_organization'] == "1") {
          $this->updateSurvey('v_organization', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'showorganization', 'other_showorganization');
        }

        if ($data['chkShowFields']['v_show_dietary_information'] == "1") {
          $this->updateSurvey('v_dietary', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'showdietaryinformation', 'other_showdietaryinformation');
        }

        if ($data['chkShowFields']['v_show_user_agreement'] == "1") {
          $this->updateSurvey('v_user_agreement', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'showuseragreement1', '');
          //$this->updateSurvey('v_user_agreement',$repcode,$barcode,strtoupper($data['fcode']),$data['vsector'],'showuseragreement2','other_Agreement');
        }


        // ==== market segment =======
        if ($data['chkShowFields']['v_showMarketSegments'] == "1") {
          $this->updateSurvey('v_mn_marketsegment', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'showMarketSegments', 'other_MarketSegments');
        }

        // ==== annual sales =======
        if ($data['chkShowFields']['v_showAnnualSales'] == "1") {
          $this->updateSurvey('v_mn_annualsales', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'showAnnualSales', 'other_AnnualSales');
        }

        // ==== Existing Arrangements with Philippine Suppliers =======
        if ($data['chkShowFields']['v_show_existing_arrangements_with_philippine_suppliers'] == "1") {
          $this->updateSurvey('v_existing_arrangement', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'showExistingArragement', 'other_ExistingArrangement');
        }
        // ==== LIST OF CLIENTS =======
        if ($data['chkShowFields']['v_show_list_of_clients'] == "1") {
          $this->site_model->updateRecTable('v_contact_profile', 'list_of_clients', $this->input->post('list_of_clients'), "", "", $repcode);
        }

        // ==== reason for visiting =======			
        if ($data['chkShowFields']['v_showReason'] == "1") {
          $this->updateSurvey('v_showreason', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'showReason', 'other_showReason');
        }

        // ==== Which Activity would you Like to Join =====
        if ($data['chkShowFields']['v_show_activity_would_like_to_join'] == "1") {
          $this->updateSurvey('v_join_activity', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'showactivity', 'other_activity');
        }


        // ==== show interested in arrange meetings ====
        if ($data['v_showWorkshopVenue'] == "1") {
          $this->updateSurvey('v_fair_tracks_venue', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'WorkshopVenue', 'other_meetings');
        }


        //==== workshop ================================
        $epay = "";

        if ($data['v_showWorkshop'] == "1" && $data['refNumberForPackage'] == "") {

          if (isset($_POST['prof'])) {
            $subTot = 0;
            if ($_POST['prof'] <> "0" || $_POST['stud'] <> "0") {
              $chkProf = $_POST['prof'] * AMOUNT_PROFESSIONAL;
              $chkStud = $_POST['stud'] * AMOUNT_STUDENT;
              $subTot  = $chkProf + $chkStud;

              //$amount = ($showWorkshop[0]=="0" ? $subTot : $showWorkshop[0]);

              //$_POST['showWorkshop'] = $subTot."#Barkada Package#PHP#0";
              $wshop = $subTot . "#Barkada Package#PHP#0";
            }
          } else {
            //print_r($_POST['showWorkshop']); die();
            $_POST['prof'] = 0;
            $_POST['stud'] = 0;
            $wshop = $_POST['showWorkshop'][0];
          }

          $showWorkshop = explode('#', $wshop);
          //$showWorkshop = explode('#', $_POST['showWorkshop']);

          // $this->updateSurvey('v_fair_tracks',$repcode,$barcode,strtoupper($data['fcode']),$data['vsector'],'showWorkshop','other_Workshop');

          $delete1 = $this->site_model->deleteRecTable('v_fair_tracks', $repcode, strtoupper($data['fcode']), $data['vsector']);
          $insert = $this->site_model->insertRecTable('v_fair_tracks', $repcode, $barcode, $showWorkshop[0], $showWorkshop[1], strtoupper($data['fcode']), $data['vsector']);

          $CreditCard = "NA";
          $idnum = "";

          $amount_professional  = (($_POST['prof'] == 0) ? "0" : AMOUNT_PROFESSIONAL);
          $amount_student       = (($_POST['stud'] == 0) ? "0" : AMOUNT_STUDENT);

          $tmp = str_replace('<strong>', '', $showWorkshop[1]);
          $serviceType = str_replace('</strong>', '', $tmp);


          $chkIfOnlinePament = explode("#", $_POST['showWorkshop'][0]);
          // if($chkIfOnlinePament[3] == "1" && isset($_POST['epayment'])== FALSE)

          if (isset($_POST["showWorkshop"]) && $chkIfOnlinePament[3] == "1") {
            $tempvalue  = element(0, $_POST["epayment"]);    // helper(array('array')) required      
            $getValue   = explode('#', $tempvalue);

            $getFcode = explode('-', strtoupper($data['fcode']));
            //print_r($tempvalue); print_r($showWorkshop); die();                          
            $referenceCode = $this->createPaymentOrder($data['profiles'], $data['project'], $showWorkshop[0], $amount_professional, $amount_student, $serviceType, $getValue[0], $data['$url_event_page1'], $getFcode[0]);
          }

          if ($data['v_disable_epayment'] == "0") {
            switch ($getValue[0]) {

              //======== DBP =================================
              case "CreditCard":
                //if($getValue[0]=="CreditCard") {$CreditCard="yes";} else {$CreditCard="no";}
                $CreditCard = "yes";


                $idnum = $this->updatePayment('v_payment_online', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'epayment', 'showWorkshop', $referenceCode, $CreditCard, $data['vdesc'], $_POST['email'], 'DBP');

                //print_r();die();
                //$epay = $CreditCard."#".$idnum;

                break;
              //======== dragonpay ==========================  
              case "DragonPay":
                $CreditCard = "no";
                $idnum = $this->updatePayment('v_payment_online', $repcode, $barcode, strtoupper($data['fcode']), $data['vsector'], 'epayment', 'showWorkshop', $referenceCode, $CreditCard, $data['vdesc'], $_POST['email'], 'DragonPay');
                //$epay = $CreditCard."#".$idnum;
                break;

              //======= PNB over the counter  
              default:
                $CreditCard = "NA";
                $idnum = "";
            }
          }

          $epay = $CreditCard . "#" . $idnum . "#" . $referenceCode; //die($epay);  
        }

        //$data['epayCHK'] = $this->encrypt2->encode2($epay);

        $TMP1 = $this->encryption->encrypt($epay);
        $data['epayCHK'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);


        // ========== MARK PREREG  =====================================================================
        if ($data['vrs'] == "" || $data['vrs'] == "3" || $data['refNumberForPackage'] <> "") {
          $this->site_model->updateRecord('v_contact_profile', 'pre_reg', 'P', "pid = '" . $pid . "'");
          $this->site_model->updateRecTable('v_attendance', 'pre_reg', 'P', strtoupper($data['fcode']), $data['vsector'], $repcode);
        }
        //==============================================================================================

        // ========== BARKADA package ==================================================================
        if ($data['refNumberForPackage'] <> "") {
          //$this->site_model->updateRecord('v_contact_profile','pre_reg','P',"pid = '".$pid."'");
          //$this->site_model->updateRecord('v_contact_profile','reg_status','F',"pid = '".$pid."'"); 
        }
        //==============================================================================================       

        //$data['pid'] = $this->encrypt2->encode2($pid);    // encrypt PID

        $TMP1 = $this->encryption->encrypt($pid);
        $data['pid'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);


        //===== upload to POINTWEST ======================
        if ($data['vrs'] == "2" && $data['vedit'] <> "1") {
          //$this->push();
        }

        //die("vvvv");

        //================================================

        //die("aaa2=".$data['vtype']."<br>".$repcode);




        //=== check if send email ===========================================================================================================

        //$vedit   = (isset($_GET["vedit"]) ? $_GET["vedit"] : ""); 
        //die("aaa=".$data['vedit']);           

        $vstatus = $this->input->post('firsttime');
        $email   = $this->input->post('email');
        $lname   = $this->input->post('lname');
        $fname   = $this->input->post('fname');
        $coname  = $this->input->post('coname');
        $country = $this->input->post('country');

        $promocode = $this->input->post('voucher');

        $arrayInfo = array($repcode, $vstatus, $email, $lname, $fname, $coname, $country, $promocode, $data['vtype']);

        switch ($data['vtype']) {
          case "TRADE BUYER":
            $swValue = "CMESS1";
            $confirmationPage = "CPAGE1";

            $data['vtype'] = "TB";
            break;
          case "GUEST":
          case "VISITOR":
            //$swValue = (($iFwithInvite[0]['item_code']=="") ? "CMESS3" : "CMESS2"); 
            $swValue = "CMESS2";
            $confirmationPage = "CPAGE1";
            break;
          case "GENERAL PUBLIC":
            $swValue = "CMESS3";
            $confirmationPage = "CPAGE1";

            $data['vtype'] = "GP";
            break;
          case "MEDIA":
            $swValue = "CMESS4";
            $confirmationPage = "CPAGE1";
            break;
        }

        //die("qqqq");        

        //if($data['vedit']<>"")
        if ($data['allow'] == "1") {

          if ($data['autoprint'] == "no") {

            echo "<script type='text/javascript'>                        
                            window.close();                    
                          </script>";
          } else {


            redirect("vrs/printid/" . $repcode . "/DIYprint/" . $data['vtype']);    // DAPAT mag close window nito


          }
        } elseif (isset($_POST["verifiedEmail"]) && $_POST["verifiedEmail"] == "verifiedEmail") {
          $SendEmail = "Yes";
          $data['PageMessage'] = $this->createMessageQR($data['project'], $confirmationPage, $swValue, $arrayInfo, $data['vsector'], $data['fcode'], $SendEmail);

          //die("xxx");        

          $this->load->view('vrs_message', $data);
        } elseif ($data['project'][0]['send_confirmation_email'] == "1") { //die("zzz");
          //=== check if "Send confirmation Email" and "Verify Email" in bussmatch table ===
          //=== check if "Send confirmation Email" and "Verify Email" in bussmatch table ===
          if ($data['project'][0]['send_email_verification'] == "1") {

            $emailTemplate1   = "";   //die("sss");
            $confirmationPage = "CPAGE2";   // Landing Page Pending/Reviewed/Waitlisted Trade

            $data['PageMessage'] = $this->execSendProc($data['project'], $data['profiles'], $email, $emailTemplate1, $confirmationPage, "noEmail", "", $data['vtype']);

            //PENDING TRADE
            $this->site_model->updateRecTable('v_attendance', 'validation_status', 'PENDING TRADE', strtoupper($data['fcode']), $data['vsector'], $repcode);

            //die("zzz");  

            $this->load->view('vrs_message', $data);
          }
          //======================================================= 
          else {

            $SendEmail = "Yes";
            $data['PageMessage'] = $this->createMessageQR($data['project'], $confirmationPage, $swValue, $arrayInfo, $data['vsector'], $data['fcode'], $SendEmail);

            //die("000");  

            $this->load->view('vrs_message', $data);
          }
        } else {

          $SendEmail = "No";
          $data['PageMessage'] = $this->createMessageQR($data['project'], $confirmationPage, $swValue, $arrayInfo, $data['vsector'], $data['fcode'], $SendEmail);

          //die("111");  

          $this->load->view('vrs_message', $data);
        }
      } // updateRecStep2        

    }    // validation	  
    //}    // $pregClose			
  }        // step2


  function createMessageQR($vproject, $vconfirmationPage, $vswValue, $varrayInfo, $vsectr, $vfcode, $vSendEmail)
  {

    // value of varrayInfo is  array($repcode,$vstatus,$email,$lname,$fname,$coname,$country,$promocode,$data['vtype']);

    $repcode = $varrayInfo[0];
    $vstatus = $varrayInfo[1];
    $email   = $varrayInfo[2];
    $lname   = $varrayInfo[3];
    $fname   = $varrayInfo[4];
    $coname  = $varrayInfo[5];
    $country = $varrayInfo[6];
    $promocode = $varrayInfo[7];
    $vtype = $varrayInfo[8];

    // =============== confirmation page message for message.php ==========================
    $getCPage = $this->master_model->loadRec("v_reference", "where switch ='" . $vconfirmationPage . "' and exclude=0 AND sector like '%" . $vsectr . "%' order by sortfield LIMIT 1");
    $PageMessage = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];
    // ====================================================================================

    $getContent = $this->master_model->loadRec("v_reference", "where switch ='" . $vswValue . "' and exclude=0 AND sector like '%" . $vsectr . "%' order by sortfield LIMIT 1");
    $messContent = $getContent[0]['header'] . $getContent[0]['content_message'] . $getContent[0]['footer'];


    $subj = $vproject[0]['description'];
    $emailfrom = $vproject[0]['emailto_registration'];
    $bcc = $vproject[0]['emailto'];

    $qrcods = $this->createQRdetails($vtype, "", $fname, $lname, $email, "", $coname, $country, $repcode, $vfcode, "yes", "P", $vstatus);

    $qrcode = " <img src='" . base_url("idbadge/" . $qrcods) . "' alt='Your QRcode' width='150' height='150'> ";


    $proofPaymentURL = "https://citem.com.ph/paymentproof/?loc=email&m=" . $email . "&vcode=" . $repcode;

    $ankor1 = "<a href='" . $proofPaymentURL . "' style='text-decoration: none; color:blue;' target='_blank'>";
    $ankor2 = "</a>";


    $nameProper1 = ucwords(strtolower($fname));
    $nameProper2 = ucwords(strtolower($lname));

    $getPromocode = ((isset($promocode) && $promocode <> "") ? "Promo Code: " . $promocode : "#" . $repcode);

    $mess0 = str_replace("{first_name}", $nameProper1, $messContent);
    $mess1 = str_replace("{last_name}", $nameProper2, $mess0);
    $mess2 = str_replace("{qrcode}", $qrcode, $mess1);
    $mess3 = str_replace("{repcode}", $repcode, $mess2);
    $mess4 = str_replace("{promocode}", $getPromocode, $mess3);

    $mess5 = str_replace("{a1}", $ankor1, $mess4);
    $mess6 = str_replace("{a2}", $ankor2, $mess5);

    $message = $mess6;

    $this->site_model->updateRecord('v_contact_profile', 'pre_reg', "P", "rep_code = '" . $repcode . "'");
    $this->site_model->updateRecTable('v_attendance', 'pre_reg', 'P', $vfcode, $vsectr, $repcode);
    $this->site_model->updateRecTable("v_attendance", "date_apply", date('Y-m-d H:i:s'), $vfcode, $vsectr, $repcode);

    //===============sendEmail($vRecipient,$vFrom,$vReplyto,$vCC,$vBCC,$vSubject,$vContent)
    //===============sendEmail($vRecipient,$vFrom,$vReplyto,$vCC,$vBCC,$vSubject,$vContent)
    $data['errorMessage'] = "";


    if ($vSendEmail == "Yes") {

      $naSend = $this->sendEmail($email, $emailfrom, "", "", $bcc, $subj, $message);
      if ($naSend <> "") {
        //die($naSend);
        $data['errDesc'] = $naSend;
        $data['errorMessage'] = "We are very sorry but there was a problem in Sending Email, please try again later... (sc_e1)";
        $this->writeError($data['errDesc'], $subj, $repcode, $barcodeValue, $data['fcode'], $data['vsector'], $sendto, "stepEnd - Send QR");
      }
    } else {

      $PageMessage = $message;
    }

    //die($PageMessage);


    if (isset($data['errorMessage']) && $data['errorMessage'] <> "") {
      // =============== get email template message.php ======================================

      $getCPage = $this->master_model->loadRec("v_reference", "where switch ='template' and exclude=0 AND sector like '%" . $data['sector'] . "%' order by sortfield LIMIT 1");
      $data['template'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];

      $PageMessage = str_replace("{message}", $data['errorMessage'], $data['template']);
    }
    // =====================================================================================
    // =====================================================================================

    return $PageMessage;
  }

  function stependREMOVE()
  {

    // error_reporting(1);
    // ini_set('error_reporting', 1);
    // ini_set('display_errors',1);


    $org = "";

    // ==============================================================================  
    // ******* IF used in VRS (include &vrs=1 to indicate used by VRS)***************
    if (isset($_GET['vrs'])) {
      $data['vrs'] = $_GET['vrs'];
    } else {
      $data['vrs'] = $this->input->post('vrs');
    }

    $data['allow'] = (isset($_GET['allow']) ? $_GET['allow'] : $this->input->post('allow'));    // ==== used for opereations marketing officers

    if (isset($_GET['autoprint'])) {
      $data['autoprint'] = $_GET['autoprint'];
    } else {
      $data['autoprint'] = $this->input->post('autoprint');
    }

    //$data['autoprint']= (isset($_GET['autoprint']) ? $_GET['autoprint'] : $_POST['autoprint']); 

    //die("zzzz1=".$_GET['pcode']);   

    if ($data['vrs'] <> "") {
      $data['vedit'] = (isset($_GET['vedit']) ? $_GET['vedit'] : $this->input->post('vedit'));

      //die("xxx= ".$data['vedit']);

      if (isset($_GET['venue'])) {
        $data['venue'] = $_GET['venue'];
      } else {
        $data['venue'] = $this->input->post('venue');
      }

      switch ($data['venue']) {
        case "WTC":
          $vnum = "1";
          break;
        case "PTTC":
          $vnum = "2";
          break;
        case "HALL1":
          $vnum = "3";
          break;
        default:
          $vnum = "1";
          break;
      }

      if (isset($_GET['station'])) {
        $data['station'] = $_GET['station'];
      } else {
        $data['station'] = $this->input->post('station');
      }
      //if ($data['station']=="") {die("STATION # is Required... report to IT personel");}       
    }
    // ==============================================================================
    // ==============================================================================    

    if (isset($_GET['fcode'])) {
      $data['fcode'] = $_GET['fcode'];
    } else {
      $data['fcode'] = $this->input->post('fcode');
    }

    $faircode = strtoupper($data['fcode']);
    $vt = 'TB';
    if (strpos($faircode, '-VISITOR') !== false) {
      $faircode = trim(str_replace("-VISITOR", "", $faircode));
      $vt = 'VISITOR';
    }
    if (strpos($faircode, '-GUEST') !== false) {
      $faircode = trim(str_replace("-GUEST", "", $faircode));
      $vt = 'GUEST';
    }
    if (strpos($faircode, '-MEDIA') !== false) {
      $faircode = trim(str_replace("-MEDIA", "", $faircode));
      $vt = 'MEDIA';
    }
    if (strpos($faircode, '-GP') !== false) {
      $faircode = trim(str_replace("-GP", "", $faircode));
      $vt = 'GENERAL PUBLIC';
    }
    if (strpos($faircode, '-VIB') !== false) {
      $faircode = trim(str_replace("-VIB", "", $faircode));
      $vt = 'VIB';
    }

    //$data['fcode'] = $faircode;

    // chk if PID is declared
    if (isset($_GET['pid'])) {
      if (isset($_GET['resend'])) {     // ******** &resend=1 use for CREATEPHIL to resend payment proceedure    **********
        $pid = $_GET['pid'];
      } else {
        //$pid = $this->encrypt2->decode2($_GET['pid']);    //

        $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['pid']);
        $pid = $this->encryption->decrypt($decryptX);
        //die("aaa= ".$_GET['pid']);
        //die($pid);

        if (!ctype_alnum($pid)) {
          if ($data['vrs'] == "") {
            die("illegal value(2), please <NAME_EMAIL>");
          } else {
            $pid = '0';
          }
        }   // value of PID
        if ($pid == "") {
          $pid = '0';
        }
      }
    }

    //else {$pid = $this->input->post('pid');}
    else {
      $pid = (($this->input->post('pid') == "") ? '0' : $this->input->post('pid'));
    }


    $data['base'] = $this->config->item('base_url'); // get base_url from config.php
    $data['main_page'] = $this->config->item('index_page'); // get index_page from config.php
    $data['css']  = $this->config->item('css');      // load mystyles.css defined in config.php 

    //chk if fcode exists and get project details
    $data['project'] = $this->master_model->getRec("busmatch_date", "where active='1' and fair_code = ?", strtoupper($data['fcode']), '');
    if ($data['project'] == '') {
      die("Under Construction.....");
    }
    foreach ($data['project'] as $proj1) {
      $data['vsector'] = $proj1['sector'];
      if ($data['allow'] == '1' || $data['allow'] == "2") {
        $pregClose = '0';   //die("aa");
      } else {  //die("bb");
        $pregClose = $this->check_status1($proj1['disable_prereg'], 0); // chek value of explode (0 - array[0] , 1 - array[1])
      }

      $emailTo            = $proj1['emailto_registration'];
      $data['ccEmail']    = $proj1['emailto'];
      $data['projDesc']   = $proj1['description'];
      $banner             = $proj1['bannerpic'];
      $data['bannerpix']  = $proj1['bannerpic'];
    }

    $withPcode = "";
    if (isset($_GET['pcode']) && $_GET['pcode'] <> "") {

      $pregClose = '0';
      $withPcode = "1";
    }

    if ($data['project'][0]['send_confirmation_email'] == "1") {

      $pregClose = '0';
      $withPcode = "1";
    }

    //die("x = ".$withPcode."<br>aaa=".$_GET['pcode']);


    $data['profiles'] = $this->site_read_model->getRec("v_contact_profile", "where pid = ?", $pid, '');

    if ($data['profiles'] == "") {
      die("PID not found");
    }

    foreach ($data['profiles'] as $rcode) {
      $data['repcode'] = $rcode['rep_code'];
      $barcode = $rcode['barcode'];
      $coname = $rcode['co_name'];
      $email = $rcode['email'];
      $data['ctry'] = $rcode['country'];
      $data['lname'] = $rcode['cont_per_ln'];
      $data['fname'] = $rcode['cont_per_fn'];
      $data['representation'] = $rcode['remarks'];
      $refForBarkadaPackage = "";      //$rcode['add_value'];

    }

    $promocode = "";
    $chkPromocode = $this->site_read_model->getRec("v_attendance", "where fair_code='" . $faircode . "' and rep_code = ?", $data['repcode'], '');
    if ($chkPromocode <> "") {
      $promocode =  $chkPromocode[0]['item_code'];
    }

    $data['vtype'] = $chkPromocode[0]['visitor_type'];
    $vstatus = $chkPromocode[0]['visitor_status'];

    //   $this->load->library(array('form_validation','session'));   
    //   $this->form_validation->set_error_delimiters('<div class="error1">', '</div>'); //globally change the error delimiters   

    $data['step'] = "stepEnd";
    $data['stepMsg'] = "STEP End";
    $data['stepImg'] = "step4.jpg";

    //die("xxx=".$pregClose);

    if ($pregClose == "1") {
      $data['vpid'] = $pid;
      //if (isset($_GET['back'])) {$data['back']=$_GET['back'];}
      $this->load->view('formsuccess', $data); // pre-registration CLOSE
    } else {       // ===== vrs#fcode#station#visitortype#venue#busscard#qrcode#allow


      if ($data['vrs'] == "" || $data['vrs'] == "3") // ********* if vrs=3 in registration.php ***********
      {
        //=======  UPDATE V_REMINDER and V_WILL_NOT_ATTEND table ========================================
        //$deleteA = $this->site_model->deleteRecTable('v_will_not_attend',$data['repcode'],strtoupper($data['fcode']),$data['vsector']);
        //$deleteB = $this->site_model->deleteRecTable('v_reminder',$data['repcode'],strtoupper($data['fcode']),$data['vsector']);	 	 
        //===================================================================
        $vsend = "1";
      } else {


        if ($data['vedit'] <> '1') {

          // =========================================================================================

          if (isset($_GET['qrcode']) && trim($_GET['qrcode']) == "yes") {   //die("aaa");

            $this->site_model->updateRecord('v_contact_profile', 'reg_status', "F", "rep_code = '" . $data['repcode'] . "'");
            $this->site_model->updateRecTable('v_attendance', 'reg_status', 'F', strtoupper($data['fcode']), $data['vsector'], $data["repcode"]);
          } else {
            //die("bbb");
            $this->site_model->updateRecEnd_registration('v_contact_profile', "rep_code = '" . $data['repcode'] . "'");  //die("aaaa"); 
            $this->site_model->updateRecTable('v_attendance', 'reg_status', 'T', strtoupper($data['fcode']), $data['vsector'], $data["repcode"]);
          }



          // =========================================================================================
          // ========= CHEK IF GEN PUB print stub do not mark as arrive unless stub is scanned =======     

          // if($visitorType=="GENERAL PUBLIC" || (isset($_GET['qrcode']) && trim($_GET['qrcode'])<>"")  )
          //  { $this->site_model->updateRecord('v_contact_profile','reg_status',"F","rep_code = '".$data['repcode']."'"); }


          //=======================================================================================================
        }
        // ==== for venue purpose CAN DELETE no use? ======== 
        $this->site_model->updateRecord('v_contact_profile', 'marked', $vnum, "rep_code = '" . $data['repcode'] . "'");
        $vsend = "0";

        //=========== SEND EMAIL TO POINTWEST FOR MOBILE APP =======================================================
        if ($data['vtype'] == "TRADE BUYER") {
          //echo "<script> alert('send email');</script>";
        }
        //========================================================================================================== 
      }

      //======== Mobile APP used ===========================================================================  
      if (isset($_GET['app']) && trim($_GET['app']) <> "") {
        $vsend = "1";     //  email
        $org = "";
        //$org ='POINTWEST';
      }
      //======== Mobile APP used =========================================================================== 

      if ($vsend == '1')  //============ check value for epayment =====================================
      {
        //============ check value for epayment =====================================
        $CreditCard = "NA";

        $v_disable_epayment = $this->check_status1($data['project'][0]["disable_epayment"], 0);  //disable_epayment
        //die($v_disable_epayment);                  
        if (isset($_GET['epayment']) && trim($_GET['epayment']) <> "" && $v_disable_epayment == "0") {

          // $xxx = '9781884d7c98fbc9859a5d7daf3a5745bda262db8c0029f22c53eac49e3bb331e200980a7aed74a4117ba2a19013ef5d4a7fcece0517f5c49cfb11e81010300dILJvkDl1y8BW35GxrkeR3cr4DcrwKSK63P6IOvinhCI=';
          // $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $xxx);
          // die("zzz= ".$this->encryption->decrypt($decryptX));


          $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['epayment']);
          $epayment = $this->encryption->decrypt($decryptX);

          //die("waaaz yes =".$epayment);

          if ($epayment <> "") {
            $tempvar = str_replace('_', '', $epayment);
            if (!ctype_alnum(str_replace('#', '', $tempvar))) {
              if ($data['vrs'] == "") {
                die("illegal value(3x)..., please <NAME_EMAIL>");
              }
            }
            $epay = explode("#", $epayment);
            $rspay = $this->site_read_model->getRec("v_payment_online", "where refno = ?", $epay[1], '');
            $pnumber = $rspay[0]['referenceCode'];

            if ($epay[0] == "yes") {
              $CreditCard = "yes";
            }
            if ($epay[0] == "no") {
              $CreditCard = "no";
            }
          }
        }  //die("wazzz no=".$CreditCard);

        //============ Dragon Pay ===================================================  

        if ($CreditCard == "no") {
          //$environment = ENV_TEST;
          //$environment = ENV_LIVE;

          $errors = array();
          $is_link = false;

          $parameters = array(
            'merchantid' => MERCHANT_ID,
            'txnid' => $rspay[0]['referenceCode'],
            'amount' => $rspay[0]['amount'],
            'ccy' => $rspay[0]['currency'],
            'description' => $rspay[0]['serviceType'],
            'email' => $rspay[0]['email'],
          );

          //print_r($parameters); die("<br>aaa");  

          $fields = array(
            'txnid' => array(
              'label' => 'Transaction ID',
              'type' => 'text',
              'attributes' => array(),
              'filter' => FILTER_SANITIZE_STRING,
              'filter_flags' => array(FILTER_FLAG_STRIP_LOW),
            ),
            'amount' => array(
              'label' => 'Amount',
              'type' => 'number',
              'attributes' => array('step="0.01"'),
              'filter' => FILTER_SANITIZE_NUMBER_FLOAT,
              'filter_flags' => array(FILTER_FLAG_ALLOW_THOUSAND, FILTER_FLAG_ALLOW_FRACTION),
            ),
            'description' => array(
              'label' => 'Description',
              'type' => 'text',
              'attributes' => array(),
              'filter' => FILTER_SANITIZE_STRING,
              'filter_flags' => array(FILTER_FLAG_STRIP_LOW),
            ),
            'email' => array(
              'label' => 'Email',
              'type' => 'email',
              'attributes' => array(),
              'filter' => FILTER_SANITIZE_EMAIL,
              'filter_flags' => array(),
            ),
          );


          $_POST['submit'] = "Pay";

          if (isset($_POST['submit'])) {

            //die("bbb");

            // Check for set values.
            foreach ($fields as $key => $value) {
              // Sanitize user input. However:
              // NOTE: this is a sample, user's SHOULD NOT be inputting these values.
              if (isset($_POST[$key])) {
                $parameters[$key] = filter_input(
                  INPUT_POST,
                  $key,
                  $value['filter'],
                  array_reduce($value['filter_flags'], function ($a, $b) {
                    return $a | $b;
                  }, 0)
                );
              }
            }

            // Validate values.
            // Example, amount validation.
            // Do not rely on browser validation as the client can manually send
            // invalid values, or be using old browsers.
            if (!is_numeric($parameters['amount'])) {
              $errors[] = 'Amount should be a number.';
            } else if ($parameters['amount'] <= 0) {
              $errors[] = 'Amount should be greater than 0.';
            }

            if (empty($errors)) {
              // Transform amount to correct format. (2 decimal places,
              // decimal separated by period, no thousands separator)
              $parameters['amount'] = number_format($parameters['amount'], 2, '.', '');
              // Unset later from parameter after digest.
              $parameters['key'] = MERCHANT_PASSWORD;
              $digest_string = implode(':', $parameters);
              unset($parameters['key']);
              // NOTE: To check for invalid digest errors,
              // uncomment this to see the digest string generated for computation.
              // var_dump($digest_string); $is_link = true;

              $parameters['digest'] = sha1($digest_string);
              $url = 'https://gw.dragonpay.ph/Pay.aspx?';
              if (ENV_TEST == 1) {
                $url = 'http://test.dragonpay.ph/Pay.aspx?';
              }

              $url .= http_build_query($parameters, '', '&');  //die($url);


              if ($is_link) {
                echo '<br><a href="' . $url . '">' . $url . '</a>';
              } else {
                header("Location: $url");
              }
            }
          } else {
            die("No POST encountered(001)... please <NAME_EMAIL>");
          }
        }
        //============ DBP Payment Gateway ==========================================
        //===========================================================================         
        else if ($CreditCard == "yes")   //credit card was selected
        {
          // send email reply re payment
          //==============================

          //==============================
          // procedd to DBP online payment
          redirect(DBP_URL . "terminalID=" . TERMINAL_ID . "&referenceCode=" . $rspay[0]['referenceCode'] . "&amount=" . $rspay[0]['amount'] . "&serviceType=" . $rspay[0]['serviceType'] . "&securityToken=" . $rspay[0]['securityToken_request']);
        }
        //=========================================================================== 
        else {

          //====== check if for CREATE PHILS ============================================================ can be removed?
          //============================================================================================= can be removed?
          // if($data['vsector']=="xx20xx")  
          // {
          //   $rspaymentOrder = $this->site_read_model->getRec("v_payment_order","where item_code = ?",$epay[2],''); 

          //   $vcontact = $data['profiles'][0]['cont_per_fn']." ".$data['profiles'][0]['cont_per_ln'];          

          //   if(date("Y-m-d",MASTERCLASS_CDATE) <= date("Y-m-d", MASTERCLASS_XDATE) )        // values of MASTERCLASS_CDATE, MASTERCLASS_XDATE from constants.php
          //     {                    
          //       $urlMess = $this->mess_earlyBird($rspaymentOrder,$data['ctry']);
          //     }
          //     else
          //     {
          //       $urlMess = $this->mess_Regular($rspaymentOrder,$data['ctry']); 
          //     }

          //   $message= $this->messageReferer($data['projDesc'],$urlMess,"",$banner,$vcontact,$data['vsector'],$data['repcode']); 

          //   die($message);

          //       $this->load->library('email');
          //       //$this->email->clear();                
          //       $this->email->set_newline("\r\n");      
          //       $this->email->from($emailTo,$data['projDesc']); // change it to yours 
          //       $this->email->reply_to($emailTo);

          //       $this->email->to($email);// change it to yours ==== sendto
          //       $this->email->cc("<EMAIL>");
          //       $this->email->subject($data['projDesc']." Masterclasses");
          //       //$this->email->set_custom_header("X-MC-Subaccount", $subAccount); // uses application/libraries/MY_Email.php               
          //       $this->email->message($message);

          //        if($this->email->send())
          //          {}
          //        else
          //          {
          //            //show_error($this->email->print_debugger());
          //            $data['emailErr'] = "Error in Sending Email... (s5)";
          //          }    
          // }   // end  $data['vsector']=="20"
          //====== for CREATE PHILS ===================================================================== 
          //=============================================================================================       

          //====== SEND email to CITEM ==================================================================
          //=============================================================================================   
          $data['emailErr'] = "";

          if (SEND_EMAIL == "xxx") {     //"YES"

            $data['pticRefer']    = (($this->input->get('pticRefer') == "") ? '' : $this->input->get('pticRefer'));   // check if PRE-REG is VIB                    

            $isVIB = "";
            if ($data['pticRefer'] == "REFER") {
              $chkIfVIB = $this->site_read_model->getRec("v_users_refer_buyer", "where availed_vib ='1' and sector='" . $data['vsector'] . "' and fair_code='" . $data['fcode'] . "' and rep_code = ?", $data['repcode'], '');

              $isVIB = $chkIfVIB[0]['availed_vib'];
            }
            $isVIBtxt = (($isVIB == "1" ? "VIB - " : ""));

            //$org = $this->input->post('org');          // if used by other BSO (cebunext,manilanow,bijoux)
            if ($org == '') {
              $org = 'CITEM';
            }

            $fcode = $data['fcode'];
            $scode = $data['vsector'];
            $sendfrom = $email;
            //====== from busmatch_date table
            $sendto = $emailTo;
            $subj = $isVIBtxt . $data['projDesc'] . " - Pre-Registration Result";
            $banner = $banner;
            //==============================		             	 			
            //$step2link =$data['base'].$data['main_page']."/site/step2?fcode=".$fcode."&pid=".$pid;
            $mess1 = "<html><head><meta http-equiv='Content-Type' content='text/html; charset=iso-8859-1' /> 
                  			     <title>Pre Registration</title>";
            $mess2 = "</head><body>";
            $mess3 = "";    // ----- $this->generateEmail($sendfrom,$pid,$org,$fcode,$scode,$data['project'],$isVIB);			  
            $mess4 = "</body></html>";

            $message = $mess1 . $mess2 . $mess3 . $mess4;

            //echo $message; die();			                    			  

            // $this->load->library('email');  
            // for ($x = 1; $x <= 2; $x++) {

            //       if($x==1) {$toemail = $sendto;} else {$toemail = $data['ccEmail'];}
            //       if(trim($toemail)<> "")
            //         {                                        
            //             $this->email->set_newline("\r\n");      
            //             $this->email->from($emailTo,$data['projDesc']); // change it to yours 
            //             $this->email->to($toemail);// change it to yours ==== sendto
            //             $this->email->reply_to($sendfrom);

            //             $this->email->subject($subj); 
            //             $this->email->message($message);      
            //             if($this->email->send())
            //              {
            //               //echo 'Email sent.';       
            //              }
            //             else
            //              {
            //               //show_error($this->email->print_debugger());
            //               $data['errDesc'] = $this->email->print_debugger(array('headers'));
            //               $data['emailErr'] = "Error in Sending Email... (s3)";
            //               $this->writeError($data['errDesc'],$subj,$data['repcode'],$barcode,$data['fcode'],$data['vsector'],$toemail,"stepend - s3"); 
            //              }
            //         }            
            // }

            $naSend = "";
            //$naSend = $this->sendEmail($sendto,$sendto,"",$data['ccEmail'],"",$subj,$message);

            if ($naSend <> "") {
              //die($naSend);
              $data['errDesc'] = $naSend;
              $data['emailErr'] = "We are very sorry but there was a problem in Sending Email, please try again later... (s1)";
              $this->writeError($data['errDesc'], $subj, $repcode, $barcodeValue, $data['fcode'], $data['vsector'], $sendto, "step1 - s1");
            }

            //====== END SEND email to CITEM ==============================================================
            //=============================================================================================                    



            //====== SEND CONFIRMATION EMAIL TO VISITOR ===================================================
            //=============================================================================================
            $data['emailErr'] = "";
            if ($data['vsector'] == "20") {
              $this->barcode($barcode, $data['fcode']);

              $vcontact = $data['profiles'][0]['cont_per_fn'] . " " . $data['profiles'][0]['cont_per_ln'];

              $urlMess = "Welcome to MIPAM x CREATE PH!  You are now successfully registered for the event.  Please present the bar code below at the registration counter in exchange for your exhibit pass.  Each issued exhibit pass is applicable for one (1) day entry.  Should you wish to visit for more than a day, kindly  present again the bar code below or keep your old badge in exchange for a new one.
                                <p style='text-align:center;'>
                                      <img src='" . base_url('assets/images/barcode/barcode' . $data['fcode'] . '_' . $barcode . '.gif') . "' height='60'><br>
                                      " . $barcode . "  
                                </p>
                                <p>For more show details, please feel free to visit our website at www.createphilippines.com.ph.</p>
                                <p>Thank you and we look forward to welcoming you in MIPAM x CREATE PH!</p>
                                <p>Sincerely,</p>
                                MIPAM x CREATE PH Secretariat";

              // $urlMess = "You are now successfully registered to the Logistics Services Philippines (LSPH) Conference 2018, happening in Meeting Room 1 at the Philippine International Convention Center (PICC) on 06 December 2018.                             
              //         <p>                           
              // Present this confirmation email at the Registration Counter to get your DELEGATE ID. Registration opens at 9:00AM. 
              //         </p>
              //         <p style='text-align:center;'>
              //         <img src='".base_url('assets/images/barcode/barcode'.$data['fcode'].'_'.$barcode.'.gif')."' height='60' alt='".$barcode."'><br>
              //         ".$barcode."  
              //         </p>

              //         <p>
              //         Kindly observe below guidelines upon entry to the event venue:
              //         </p>

              //         <p>
              //         <ul>  
              //           <li>Only those with Delegate IDs will be allowed entry at the venue.</li>
              //           <li>Children below 12 years old are strictly prohibited from entering the event venue. </li>
              //           <li>Proper business attire is encouraged.</li>
              //         </ul>
              //         </p>
              //         <p>
              //           Should you have any question or concern, you may send an email to <a href='mailto:<EMAIL>'><EMAIL></a> or <a href='mailto:<EMAIL>'><EMAIL></a> or contact CITEM at 831-2201 local 226.

              //         </p>                            
              // ";

              //$message= $this->messageReferer($data['projDesc'],$urlMess,"",$data['project'][0]['bannerpic'],$vcontact,$data['vsector'],$data['repcode']); 
              //die($message);

              $subj = $data['projDesc'] . " - Pre-Registration Confirmation";

              //$this->load->library('email');  
              for ($x = 1; $x <= 2; $x++) {

                if ($x == 1) {
                  $toemail = $email;
                } else {
                  $toemail = $data['project'][0]['emailto'];
                }
                if (trim($toemail) <> "") {
                  // $this->email->set_newline("\r\n");      
                  // $this->email->from($data['project'][0]['emailto_registration'],$data['projDesc']); // change it to yours 
                  // $this->email->to($toemail);// change it to yours ==== sendto
                  // $this->email->reply_to($data['project'][0]['emailto_registration']);

                  // $this->email->subject($subj); 
                  // $this->email->message($message);      
                  //if($this->email->send())
                  // {

                  // }
                  //else
                  // {
                  //  $data['errDesc'] = $this->email->print_debugger(array('headers'));
                  //  $data['emailErr'] = "Error in Sending Email... (s6)";
                  //  $data['messageX'] = "Error in Sending Email... (s6)";

                  //  $this->writeError($data['errDesc'],$subj,$data['repcode'],$barcode,$data['fcode'],$data['vsector'],$toemail,"stepend - s6");
                  // }
                }
              }
            }
            //============================================================================================= 
            //=============================================================================================       

          }  // SEND_EMAIL == "YES"         

        } //  end  $epay[0]=="yes"

      } // email send  ($vsend=='1')


      //======== for Refer.php ==============================================================================
      $data['pticRefer']    = (($this->input->get('pticRefer') == "") ? '' : $this->input->get('pticRefer'));
      $data['pticReferID']  = (($this->input->get('pticReferID') == "") ? '' : $this->input->get('pticReferID'));
      //$data['pticEmail']    = $this->site_read_model->getRec("v_users_refer_buyer","where id = ?",$data['pticReferID'],"");


      if ($data['pticRefer'] == "REGULAR") {
        $this->site_model->updateRecord('v_users_refer_buyer', 'availed_regular', "1", "id = '" . $data['pticReferID'] . "'");
        $this->site_model->updateRecord('v_users_refer_buyer', 'availed_vib', "", "id = '" . $data['pticReferID'] . "'");
        $this->site_model->updateRecord('v_users_refer_buyer', 'date_apply', date('Y-m-d H:i:s'), "id = '" . $data['pticReferID'] . "'");
        $this->site_model->updateRecord('v_contact_profile', 'date_apply', date('Y-m-d H:i:s'), "rep_code = " . $data['repcode']);
      }

      if ($data['pticRefer'] == "REFER") {
        $this->site_model->updateRecord('v_users_refer_buyer', 'availed_regular', "", "id = '" . $data['pticReferID'] . "'");
        $this->site_model->updateRecord('v_users_refer_buyer', 'availed_vib', "1", "id = '" . $data['pticReferID'] . "'");
        $this->site_model->updateRecord('v_users_refer_buyer', 'date_apply', date('Y-m-d H:i:s'), "id = '" . $data['pticReferID'] . "'");
        $this->site_model->updateRecord('v_contact_profile', 'date_apply', date('Y-m-d H:i:s'), "rep_code = " . $data['repcode']);


        //$data['vRef'] = $this->encrypt2->encode2($data['vsector']."~".$data['fcode']."~".$data['repcode']);         

        $TMP1 = $this->encryption->encrypt($data['vsector'] . "~" . $data['fcode'] . "~" . $data['repcode']);
        $data['vRef'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);

        //====== CREATE VIP-FORM PDF ===========================================

        $data['urlPDF'] = $this->vibFormPDF($data['vRef']);

        //======== SEND EMAIL to CITEM and REFERER =============================
        //==== here ==========
        $data['emailErr'] = "";
        if (SEND_EMAIL == "xxx") {

          $org = $this->input->post('org');          // if used by other BSO (cebunext,manilanow,bijoux)
          if ($org == '') {
            $org = 'CITEM';
          }

          $fcode = $data['fcode'];
          $scode = $data['vsector'];
          //$sendfrom= $email;        
          //====== from busmatch_date table
          $emailFRM = $emailTo;
          $xxx = "Your application to " . htmlentities($data['projDesc']) . " has been received! "; //die($xxx);
          $subj = $xxx;

          $bannerpic = $banner;
          //==============================                        
          //$step2link =$data['base'].$data['main_page']."/site/step2?fcode=".$fcode."&pid=".$pid;

          //$vcontact = $data['pticEmail'][0]['rep_fname']." ".$data['pticEmail'][0]['rep_lname'];

          if ($data['vsector'] == "02") {
            $urlMess = "Your <a href='" . site_url($data['urlPDF']) . "' target='_blank'>application</a> as a Manila FAME Very Important Buyer is almost complete! 
                        Kindly wait for updates on the result of your application.<br><br>
                        For any question or concern, don’t hesitate to message us at <a href='mailto:<EMAIL>' target='_top'><EMAIL></a> or call +63 8312201 Local 242.
                        <p>Thank you very much.</p>
                        <p style='text-align: right;'><strong>-&nbsp;&nbsp;&nbsp; The Manila FAME Team</strong></p>";

            // $urlMess = "Your <a href='".site_url($data['urlPDF'])."' target='_blank'>application</a> as a Manila FAME Very Important Buyer is almost complete!<br><br>
            // Kindly email a copy of your passport, a business card, and company profile to <a href='mailto:<EMAIL>' target='_top'><EMAIL></a>
            // with the subject line: ".strtoupper("VIB_".$data['lname']."_".$data['ctry']).". Then wait for updates on the result of your application.<br><br>
            // For any question or concern, don’t hesitate to message us at <a href='mailto:<EMAIL>' target='_top'><EMAIL></a> or call +63 8312201 Local 242.
            // <p>Thank you very much.</p>
            // <p style='text-align: right;'><strong>-&nbsp;&nbsp;&nbsp; The Manila FAME Team</strong></p>";
          }

          if ($data['vsector'] == "01") {
            // $urlMess = "Your <a href='".site_url($data['urlPDF'])."' target='_blank'>application</a> as a IFEX Philippines Very Important Buyer is almost complete!<br><br>
            // Kindly email the copy of your passport and company profile to <a href='mailto:<EMAIL>' target='_top'><EMAIL></a>
            // with the subject line: VIB_".$data['ctry']."_".$data['pticEmail'][0]['rep_coname'].". Then wait for updates on the result of your application.<br><br>
            // For any question or concern, don’t hesitate to message us at <a href='mailto:<EMAIL>' target='_top'><EMAIL></a> or call +63 8322201 Local 231.
            // <p>Thank you very much.</p>
            // <p style='text-align: right;'><strong>-&nbsp;&nbsp;&nbsp; The IFEX Philippines Team</strong></p>";
          }


          $mess1 = "";
          $mess2 = "";
          //$mess3= $this->messageReferer($data['projDesc'],$urlMess,"",$bannerpic,$vcontact,$data['vsector'],$data['repcode']); 
          $mess4 = "";
        }
        //======== END SEND EMAIL to CITEM and REFERER =============================
        //==========================================================================                

      }
      //======== Refer.php =================================================================================

      //======== Mobile APP used ===========================================================================  
      if (isset($_GET['app']) && trim($_GET['app']) <> "") {
        $data['app'] = $_GET['app'];
        //die("waz - ".$data['app']);
      }
      //======== Mobile APP used ===========================================================================  

      //========= create QRCODE ===========================================


      if (isset($_GET['qrcode']) && trim($_GET['qrcode']) <> "") {
        $data['qrcode'] = $_GET['qrcode'];

        // //============== check if with PROMO CODE =================================================================================================
        // $iFwithInvite= $this->site_read_model->getRec("v_voucher_code","where sector='".$data['vsector']."' and rep_code = ?",$data['repcode'],'');  
        // //=========================================================================================================================================

        // $repre = explode('|',$data['representation']);

        // if (in_array("Government/Military", $repre)) {

        //     $data['vRep'] = "Government";
        //     $this->site_model->updateRecTable('v_attendance','visitor_type','GUEST',strtoupper($data['fcode']),$data['vsector'],$data['repcode']);
        //     $this->site_model->updateRecTable('v_contact_profile','visitor_type',"GUEST","","",$data['repcode']);

        // } else if (in_array("Media/Press", $repre)) {
        //     $data['vRep'] = "Media";

        //     $this->site_model->updateRecTable('v_attendance','visitor_type','MEDIA',strtoupper($data['fcode']),$data['vsector'],$data['repcode']);
        //     $this->site_model->updateRecTable('v_contact_profile','visitor_type',"MEDIA","","",$data['repcode']);

        // } else {

        //   if($data['vtype'] <> "TRADE BUYER") {

        //     if($iFwithInvite[0]['item_code']=="") {
        //       $data['vRep'] = "Cashier";
        //       $this->site_model->updateRecord('v_contact_profile','visitor_type','GENERAL PUBLIC',"rep_code = '".$data['profiles'][0]['rep_code']."'");
        //       $this->site_model->updateRecTable('v_attendance','visitor_type','GENERAL PUBLIC',$data['fcode'],$data['vsector'],$data['profiles'][0]['rep_code']);
        //     }  else {
        //       $data['vRep'] = "Guest";
        //       $this->site_model->updateRecord('v_contact_profile','visitor_type','GUEST',"rep_code = '".$data['profiles'][0]['rep_code']."'");
        //       $this->site_model->updateRecTable('v_attendance','visitor_type','GUEST',$data['fcode'],$data['vsector'],$data['profiles'][0]['rep_code']);
        //     }
        //   }  

        // }


        // //die("zzz= ".print_r($repre));

        // $data['barcode'] = $barcode;
        // //$qrcods = $this->createQR($data['barcode'],$data['fcode']);

        // $qrcods = $this->createQRdetails($data['vtype'],"",$data['fname'],$data['lname'],$email,"",$coname,$data['ctry'],$data['repcode'],$faircode,"no","onsite");

        // //die("waz - ".$data['qrcode']);
      }


      // ========================== used if using function visitor(), tb()... ==================================
      // =======================================================================================================

      $vedit = (isset($_GET["vedit"]) ? $_GET["vedit"] : "");
      if ($data['project'][0]['send_confirmation_email'] == "1" && $vedit == "")  // && $_GET["vedit"]<>""
      { //die("xxx"); // do not send email in edit if vedit=""    

        //die("bbb== ".$data['project'][0]['send_confirmation_email']);

        //die("wazzz=". $_GET["vedit"]);

        // ======== check if with invite =========================================================================

        //$iFwithInvite= $this->site_read_model->getRec("v_voucher_code","where sector='".$data['vsector']."' and rep_code = ?",$data['repcode'],''); 

        // =======================================================================================================

        switch ($data['vtype']) {
          case "TRADE BUYER":
            $swValue = "CMESS1";
            $confirmationPage = "CPAGE1";
            break;
          case "GUEST":
            //$swValue = (($iFwithInvite[0]['item_code']=="") ? "CMESS3" : "CMESS2"); 
            $swValue = "CMESS2";
            $confirmationPage = "CPAGE1";
            break;
          case "GENERAL PUBLIC":
            $swValue = "CMESS3";
            $confirmationPage = "CPAGE1";
            break;
          case "MEDIA":
            $swValue = "CMESS4";
            $confirmationPage = "CPAGE1";
            break;
        }
        // =============== confirmation page message for message.php ==========================
        $getCPage = $this->master_model->loadRec("v_reference", "where switch ='" . $confirmationPage . "' and exclude=0 AND sector like '%" . $data['vsector'] . "%' order by sortfield LIMIT 1");
        $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message'];
        // ====================================================================================

        $getContent = $this->master_model->loadRec("v_reference", "where switch ='" . $swValue . "' and exclude=0 AND sector like '%" . $data['vsector'] . "%' order by sortfield LIMIT 1");
        $messContent = $getContent[0]['header'] . $getContent[0]['content_message'] . $getContent[0]['footer'];     //$getContent[0]['content_message'];


        $subj = $data['project'][0]['description'];
        $emailfrom = $data['project'][0]['emailto_registration'];
        $bcc = $data['project'][0]['emailto'];

        $qrcods = $this->createQRdetails($data['vtype'], "", $data['profiles'][0]['cont_per_fn'], $data['profiles'][0]['cont_per_ln'], $data['profiles'][0]['email'], "", $data['profiles'][0]['co_name'], $data['profiles'][0]['country'], $data['profiles'][0]['rep_code'], $data['fcode'], "yes", "P", $vstatus);

        //$qrValue = base_url("idbadge/".$faircode."-".$data['profiles'][0]['rep_code'].".png");
        //$qrcode = " <img src='".$qrValue."' alt='Your QRcode' width='150' height='150'> ";

        $qrcode = " <img src='" . base_url("idbadge/" . $qrcods) . "' alt='Your QRcode' width='150' height='150'> ";


        $proofPaymentURL = "https://citem.com.ph/paymentproof/?loc=email&m=" . $email . "&vcode=" . $data['repcode'];

        $ankor1 = "<a href='" . $proofPaymentURL . "' style='text-decoration: none; color:blue;' target='_blank'>";
        $ankor2 = "</a>";


        $nameProper1 = ucwords(strtolower($data['profiles'][0]['cont_per_fn']));
        $nameProper2 = ucwords(strtolower($data['profiles'][0]['cont_per_ln']));

        $getPromocode = ((isset($promocode) && $promocode <> "") ? "Promo Code: " . $promocode : "#" . $data['repcode']);

        $mess0 = str_replace("{first_name}", $nameProper1, $messContent);
        $mess1 = str_replace("{last_name}", $nameProper2, $mess0);
        $mess2 = str_replace("{qrcode}", $qrcode, $mess1);
        $mess3 = str_replace("{repcode}", $data['repcode'], $mess2);
        $mess4 = str_replace("{promocode}", $getPromocode, $mess3);

        $mess5 = str_replace("{a1}", $ankor1, $mess4);
        $mess6 = str_replace("{a2}", $ankor2, $mess5);

        $message = $mess6;

        // die("waazzzzz- line 4447<br><br>".$message);

        //if($iFwithInvite[0]['item_code']=="") {
        // ===== disabled??? use in tanyag? =========================
        //$this->site_model->updateRecord('v_contact_profile','visitor_type',"GENERAL PUBLIC","rep_code = '".$data['profiles'][0]['rep_code']."'");
        //$this->site_model->updateRecTable('v_attendance','visitor_type','GENERAL PUBLIC',$data['fcode'],$data['vsector'],$data['profiles'][0]['rep_code']);
        //}

        $this->site_model->updateRecord('v_contact_profile', 'pre_reg', "P", "rep_code = '" . $data['profiles'][0]['rep_code'] . "'");
        $this->site_model->updateRecTable('v_attendance', 'pre_reg', 'P', $data['fcode'], $data['vsector'], $data['profiles'][0]['rep_code']);
        $this->site_model->updateRecTable("v_attendance", "date_apply", date('Y-m-d H:i:s'), $data['fcode'], $data['vsector'], $data['profiles'][0]['rep_code']);

        //===============sendEmail($vRecipient,$vFrom,$vReplyto,$vCC,$vBCC,$vSubject,$vContent)
        $data['errorMessage'] = "";

        $naSend = $this->sendEmail($email, $emailfrom, "", "", $bcc, $subj, $message);

        if ($naSend <> "") {
          //die($naSend);
          $data['errDesc'] = $naSend;
          $data['errorMessage'] = "We are very sorry but there was a problem in Sending Email, please try again later... (sc_e1)";
          $this->writeError($data['errDesc'], $subj, $repcode, $barcodeValue, $data['fcode'], $data['vsector'], $sendto, "stepEnd - Send QR");
        }

        if (isset($data['errorMessage']) && $data['errorMessage'] <> "") {
          // =============== get email template message.php ======================================

          $getCPage = $this->master_model->loadRec("v_reference", "where switch ='template' and exclude=0 AND sector like '%" . $data['sector'] . "%' order by sortfield LIMIT 1");
          $data['template'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];    //$getCPage[0]['content_message'];

          $data['PageMessage'] = str_replace("{message}", $data['errorMessage'], $data['template']);

          //die("aaaa");
          // =====================================================================================
        }

        //die ("<br>.aaaaaa");

        $this->load->view('vrs_message', $data);
      } else {
        //die("vvv1=".$withPcode);

        // bypass formsuccess... process here nalang 

        if ($data['vrs'] == "2" && $vedit <> "") {

          //die("aaa:line=4577");
          $this->load->view('formsuccess', $data);
        } else if ((isset($data['qrcode']) && $data['qrcode']  == "yes") || $withPcode == "1") {
          //die("vvv33=".$withPcode);

          switch ($data['vtype']) {
            case "TRADE BUYER":
              $swValue = "CMESS1";
              $confirmationPage = "CPAGE1";
              break;
            case "GUEST":
              //$swValue = (($iFwithInvite[0]['item_code']=="") ? "CMESS3" : "CMESS2"); 
              $swValue = "CMESS2";
              $confirmationPage = "CPAGE1";
              break;
            case "GENERAL PUBLIC":
              $swValue = "CMESS3";
              $confirmationPage = "CPAGE1";
              break;
            case "MEDIA":
              $swValue = "CMESS4";
              $confirmationPage = "CPAGE1";
              break;
          }
          // =============== confirmation page message for message.php ==========================
          $getCPage = $this->master_model->loadRec("v_reference", "where switch ='" . $confirmationPage . "' and exclude=0 AND sector like '%" . $data['vsector'] . "%' order by sortfield LIMIT 1");
          $data['PageMessage'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];     //$getCPage[0]['content_message'];
          // ====================================================================================

          $getContent = $this->master_model->loadRec("v_reference", "where switch ='" . $swValue . "' and exclude=0 AND sector like '%" . $data['vsector'] . "%' order by sortfield LIMIT 1");
          $messContent = $getContent[0]['header'] . $getContent[0]['content_message'] . $getContent[0]['footer'];     //$getContent[0]['content_message'];


          $subj = $data['project'][0]['description'];
          $emailfrom = $data['project'][0]['emailto_registration'];
          $bcc = $data['project'][0]['emailto'];

          $qrcods = $this->createQRdetails($data['vtype'], "", $data['profiles'][0]['cont_per_fn'], $data['profiles'][0]['cont_per_ln'], $data['profiles'][0]['email'], "", $data['profiles'][0]['co_name'], $data['profiles'][0]['country'], $data['profiles'][0]['rep_code'], $faircode, "yes", "P", $vstatus);

          //$qrValue = base_url("idbadge/".$faircode."-".$data['profiles'][0]['rep_code'].".png");
          //$qrcode = " <img src='".$qrValue."' alt='Your QRcode' width='150' height='150'> ";

          $qrcode = " <img src='" . base_url("idbadge/" . $qrcods) . "' alt='Your QRcode' width='150' height='150'> ";

          //die("aaa=".$qrValue);

          $nameProper1 = ucwords(strtolower($data['profiles'][0]['cont_per_fn']));
          $nameProper2 = ucwords(strtolower($data['profiles'][0]['cont_per_ln']));

          //$getPromocode = ( (isset($promocode) && $promocode<>"") ? "Promo Code: ".$promocode : "" );
          $getPromocode = ((isset($promocode) && $promocode <> "") ? "Promo Code: " . $promocode : "#" . $data['repcode']);

          $mess0 = str_replace("{first_name}", $nameProper1, $messContent);
          $mess1 = str_replace("{last_name}", $nameProper2, $mess0);
          $mess2 = str_replace("{qrcode}", $qrcode, $mess1);
          $mess3 = str_replace("{repcode}", $data['repcode'], $mess2);
          $mess4 = str_replace("{promocode}", $getPromocode, $mess3);

          $data['PageMessage'] = $mess4;

          if ($data['project'][0]['send_confirmation_email'] == "0") {
            $withPcode = "0";
          }

          if ($withPcode == "1") {

            //die("sss");
            //===============sendEmail($vRecipient,$vFrom,$vReplyto,$vCC,$vBCC,$vSubject,$vContent)
            $data['errorMessage'] = "";

            $naSend = $this->sendEmail($email, $emailfrom, "", "", $bcc, $subj, $mess4);
            //die("sss11");

            if ($naSend <> "") {
              //die($naSend);
              $data['errDesc'] = $naSend;
              $data['errorMessage'] = "We are very sorry but there was a problem in Sending Email, please try again later... (sc_e1)";
              $this->writeError($data['errDesc'], $subj, $data['repcode'], $barcode, $data['fcode'], $data['vsector'], $email, "stepEnd - Send QR");
            }

            if (isset($data['errorMessage']) && $data['errorMessage'] <> "") {
              // =============== get email template message.php ======================================

              $getCPage = $this->master_model->loadRec("v_reference", "where switch ='template' and exclude=0 AND sector like '%" . $data['vsector'] . "%' order by sortfield LIMIT 1");
              $data['template'] = $getCPage[0]['header'] . $getCPage[0]['content_message'] . $getCPage[0]['footer'];    //$getCPage[0]['content_message'];

              $data['PageMessage'] = str_replace("{message}", $data['errorMessage'], $data['template']);

              //die("aaaa");
              // =====================================================================================
            }
          }


          //die("aaa");

          $this->load->view('vrs_message', $data);
        } else {

          //die("bbb");

          if (!isset($_GET['resend'])) {
            $this->load->view('formsuccess', $data);    //vrs_message
          }
        }
      }
    }      // $pregClose

    // } //== $a

  }        // stepEnd


  function chkModuleTable($project, $rcode, $fcode, $chkIFCompleteOnly)
  {
    //die("aa="."<br>bbb=".$rcode."<br>ccc=".$fcode);	


    $vmtg  = ($this->check_status1($project[0]['show_interested_in_arrange_meetings'], 0) == "1" ? $this->addValueinTable("v_arranged_meeting", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "MET1") : 1);
    $vjobf  = ($this->check_status1($project[0]['show_jobfunction'], 0) == "1" ? $this->addValueinTable("v_job_function", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "JOB_F") : 1);
    $vprod  = ($this->check_status1($project[0]['show_products'], 0) == "1" ? $this->addValueinTable("v_genproducts", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "GP") : 1);
    $vrepre = ($this->check_status1($project[0]['show_representation'], 0) == "1" ? $this->addValueinTable("v_representation", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "B2") : 1);

    $vmkts  = ($this->check_status1($project[0]['show_market_segments'], 0) == "1" ? $this->addValueinTable("v_mn_marketsegment", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "MN3") : 1);
    $vsale  = ($this->check_status1($project[0]['show_annual_sales'], 0) == "1" ? $this->addValueinTable("v_mn_annualsales", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "MN2") : 1);
    $vexist  = ($this->check_status1($project[0]['show_existing_arrangements_with_philippine_suppliers'], 0) == "1" ? $this->addValueinTable("v_existing_arrangement", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "SUP1") : 1);

    $vspecific  = ($this->check_status1($project[0]['show_specific_product'], 0) == "1" ? $this->addValueinTable("v_product_requirement", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "GP_O") : 1);
    $vreason  = ($this->check_status1($project[0]['show_reason'], 0) == "1" ? $this->addValueinTable("v_showreason", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "MN1") : 1);
    $vabout  = ($this->check_status1($project[0]['show_learnedabout'], 0) == "1" ? $this->addValueinTable("v_informthru", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "B1") : 1);
    $vjoin  = ($this->check_status1($project[0]['show_activity_would_like_to_join'], 0) == "1" ? $this->addValueinTable("v_join_activity", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "ACT1") : 1);
    $vagree  = ($this->check_status1($project[0]['show_user_agreement'], 0) == "1" ? $this->addValueinTable("v_user_agreement", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "UA1") : 1);

    $vattend = $this->addValueinTable("v_attendance", $rcode, $fcode, $project[0]['sector'], $chkIFCompleteOnly, "");
    //buyer_type -> REGULAR
    //show_list_of_clients

    if ($chkIFCompleteOnly == "YES") {
      $vattend = "1";
    }

    $result = ($vmtg == "1" && $vjobf == "1" && $vrepre == "1" && $vmkts == "1" && $vsale == "1" &&
      $vexist == "1" && $vprod == "1"  && $vreason == "1" &&
      $vabout == "1" && $vjoin == "1" && $vagree == "1" && $vattend == "1" ? "1" : "0");         // == remove $vspecific=="1" since not required

    //die("xx=".$vmtg."<br>".$vjobf."<br>".$vprod."<br>".$vrepre."<br>".$vmkts."<br>".$vsale."<br>".$vexist."<br>".$vreason."<br>".$vabout."<br>".$vjoin."<br>".$vagree."<br>".$vattend);
    //die($result);

    return $result;
  }

  function addValueinTable($vtable, $vRepcode, $fcode, $sector, $chkIFCompleteOnly, $swt)
  {
    //$this->load->model('site_model');
    //die("aaa=".$chkIFCompleteOnly);

    $preregResult = 0;
    //$getdata  = $this->site_read_model->getRec($vtable,"where fair_code='".$fcode."' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC",$vRepcode,$sector);

    //loadRec("v_reference","where switch ='B2' and exclude=0 and sector like '%".$nSector."%' order by sortfield");
    //loadRec("citem_masterfile.product_lib","where citem_masterfile.product_lib.classification='1' and citem_masterfile.product_lib.sector like '%".$data['vsector']."%' ORDER BY citem_masterfile.product_lib.prod_cat,citem_masterfile.product_lib.sortfield" );

    $vfield = ($vtable == "v_genproducts" ? "prod_cat" : "item_code");

    $vquery = ($chkIFCompleteOnly == "YES" ? " and " . MASTER_DB . ".v_reference.sector like '%" . $sector . "%'" : " and " . MASTER_DB . ".v_reference.sector like '%" . $sector . "%'");
    //die("aaa=".$vquery);

    if ($vtable == "v_attendance") {
      $getdata  = $this->site_read_model->getRec($vtable, "where fair_code='" . $fcode . "' AND rep_code = ? order by SUBSTR(fair_code,-4) DESC,SUBSTR(fair_code,-5,1) DESC", $vRepcode, $sector);
      if ($getdata == "") {
        $getdata  = $this->site_read_model->getRec($vtable, "where rep_code = ? order by refno DESC limit 1", $vRepcode, "");
        foreach ($getdata as &$value)                           // notice the & symbol. It gives you ability to change the array
        {
          $value['valid_email'] = "1";
          $value['sector'] = $sector;
          $value['fair_code'] = $fcode;
          unset($value['refno']);                               // remove field "refno" since autonumber
        }
      }
      //print_r($getdata); die();
    } else {

      $getdata = $this->site_read_model->joinTable(
        $vtable,
        MASTER_DB . ".v_reference",
        $vtable . ".*",
        $vtable . "." . $vfield . " = " . MASTER_DB . ".v_reference.c_code",
        $vtable . ".fair_code='" . $fcode . "'" . $vquery . " and rep_code=" . $vRepcode . " and " . MASTER_DB . ".v_reference.switch = '" . $swt .
          "' and " . MASTER_DB . ".v_reference.exclude=0  
			order by SUBSTR(" . $vtable . ".fair_code,-4) DESC,SUBSTR(" . $vtable . ".fair_code,-5,1) DESC",
        "inner"
      );

      //===== if no record in $getdata using faircode try again using sector ===================================
      if ($getdata == "") {
        $getdata = $this->site_read_model->joinTable(
          $vtable,
          MASTER_DB . ".v_reference",
          $vtable . ".*",
          $vtable . "." . $vfield . " = " . MASTER_DB . ".v_reference.c_code",
          $vtable . ".sector='" . $sector . "'" . $vquery . " and rep_code=" . $vRepcode . " and " . MASTER_DB . ".v_reference.switch = '" . $swt .
            "' and " . MASTER_DB . ".v_reference.exclude=0  
				order by SUBSTR(" . $vtable . ".fair_code,-4) DESC,SUBSTR(" . $vtable . ".fair_code,-5,1) DESC limit 1",
          "inner"
        );
      }
      //=========================================================================================================
    }

    if ($chkIFCompleteOnly == "NO") {
      if ($getdata <> "") {
        foreach ($getdata as &$value)                            // notice the & symbol. It gives you ability to change the array
        {

          $value['fair_code'] = $fcode;
          unset($value['refno']);                                // remove field "refno" since autonumber

          if ($vtable == "v_attendance") {
            $value['date_apply'] = date("Y-m-d H:i:s");
            $value['date_input'] = date("Y-m-d H:i:s");
            $value['visitor_status'] = "REGULAR";
            $value['visitor_status_remarks'] = "";
            $value['buyerclass'] = "";
            $value['buyerclass_remarks'] = "";
            $value['validated'] = "0";
            $value['date_validated'] = "0000-00-00 00:00:00";
            $value['validated_by'] = "";
            $value['emailed'] = "";
            $value['date_emailed'] = "0000-00-00 00:00:00";
            $value['url_form'] = "";
            $value['pre_reg'] = "P";
          }
        }
        $delresult = $this->site_model->deleteRecTable($vtable, $vRepcode, $fcode, $sector);    // delete existing rec first

        //print_r($getdata); die();

        $preregResult = $this->site_model->insertBatch($vtable, $getdata);                    // insert

      }
    } else {
      //if($vtable == "v_attendance") { $value['validated'] = 0; }
      if ($getdata <> "") {
        $preregResult = "1";
      }
    }
    return $preregResult;
  }

  function uploadFileFunc($upFile, $repcode1, $fcode, $vsector, $dtype, $ifExist)
  {
    //print_r($_FILES[$upFile]['name']);
    //die("yes");
    $dtype = strtoupper($dtype);
    $messUpload = "";
    $filesCount = count($_FILES[$upFile]['name']);
    for ($i = 0; $i < $filesCount; $i++) {
      $_FILES['file']['name']     = $dtype . "_" . $fcode . "_" . $_FILES[$upFile]['name'][$i];
      $_FILES['file']['type']     = $_FILES[$upFile]['type'][$i];
      $_FILES['file']['tmp_name'] = $_FILES[$upFile]['tmp_name'][$i];
      $_FILES['file']['error']    = $_FILES[$upFile]['error'][$i];
      $_FILES['file']['size']     = $_FILES[$upFile]['size'][$i];

      // $_FILES['file']['name']     = $_FILES['file']['name']."_".$repcode1

      //die($_FILES['file']['type']);

      // File upload configuration
      $uploadPath = 'uploads/files/';
      $config['upload_path'] = $uploadPath;
      $config['overwrite'] = TRUE;
      //$config['allowed_types'] = 'pdf';	//'jpg|jpeg|png|gif';
      $config['allowed_types'] = 'jpg|jpeg|png|gif';
      $config['max_size'] = UPLOAD_MAXFILESIZE;

      // Load and initialize upload library
      $this->load->library('upload', $config);
      $this->upload->initialize($config);

      // Upload file to server
      if ($this->upload->do_upload('file')) {
        // Uploaded file data                    
        $fileData = $this->upload->data();

        // Chk file if exist
        //$chkRec= $this->site_read_model->loadRec("v_upload","where file_name ='".$fileData['file_name']."' and fair_code='".$fcode."' and sector='".$vsector."' and rep_code=".$repcode1);
        $chkRec = $this->site_read_model->loadRec("v_upload", "where doxtype ='" . $dtype . "' and fair_code='" . $fcode . "' and sector='" . $vsector . "' and rep_code=" . $repcode1);
        //$chkRec= $this->file->loadRec("files","where file_name ='".$fileData['file_name']."'"); //print_r($chkRec); die("aa=");
        // =================
        //if($chkRec[0]['file_name']<> $fileData['file_name']) {
        //if($chkRec[0]['doxtype']<> $dtype) {  
        if ($chkRec == "") {
          $uploadData[$i]['rep_code'] = $repcode1;
          $uploadData[$i]['file_name'] = $fileData['file_name'];
          $uploadData[$i]['fair_code'] = $fcode;
          $uploadData[$i]['sector'] = $vsector;
          $uploadData[$i]['doxtype'] = $dtype;
          $uploadData[$i]['file_path'] = base_url($uploadPath);
          $uploadData[$i]['uploaded_on'] = date("Y-m-d H:i:s");
        } else {

          $this->site_model->updateField("v_upload", "file_name", $fileData['file_name'], " fair_code='" . $fcode . "' and sector='" . $vsector . "' and rep_code=" . $repcode1 . " and doxtype='" . $dtype . "'");
          $this->site_model->updateField("v_upload", "uploaded_on", date("Y-m-d H:i:s"), " fair_code='" . $fcode . "' and sector='" . $vsector . "' and rep_code=" . $repcode1 . " and doxtype='" . $dtype . "'");
        }

        //$messUpload = " - File Uploaded";	
      } else {
        if ($_FILES['file']['size'] >= $config['max_size'] * 1024) {
          //$this->session->set_flashdata("statusMsg1","File size to large. File must not exceed ".$config['max_size']. "KB");   
          //$messUpload = "<p style='background-color:#FF6666'>File size to large. File must not exceed ".($config['max_size']/1024). "MB</p>"; 

          $messUpload = "File size to large. File must not exceed " . ($config['max_size'] / 1024) . "MB";
        } elseif ($_FILES['file']['type'] <> 'image/jpeg' && $_FILES['file']['type'] <> '') {

          //if($ifExist == "")
          //{ 
          $messUpload = "Upload JPEG format files only";
          //}
        } else {
          //$this->session->set_flashdata("statusMsg1","No Fileszzz Selected");
          //$messUpload = "No Filesz Selected";	
        }
      }
    }

    if (!empty($uploadData)) {
      // Insert files data into the database
      $insert = $this->site_model->insert($uploadData);

      // Upload status message
      $messUpload = $insert ? '' : 'Some problem occurred, please try again.';
      //$this->session->set_flashdata('statusMsg',$statusMsg);
    }

    return $messUpload;
  }

  function createQRdetails($visitorType, $position, $fn, $ln, $email, $mobile, $comp, $ctry, $rcode, $fcode, $XincludeAllProfile, $vrsPrereg, $vstat)
  //$qrcods = $this->createQR($data['fn'],$data['ln'],$data['email'],$data['mobile'],$data['comp'],$data['repcode'],$data['fcode']);
  {
    $this->load->library('infiQr');

    $tempDir = FCPATH . "idbadge/";


    $fcode = strtoupper($fcode);

    if (strpos($fcode, '-VISITOR') !== false) {
      $fcode = trim(str_replace("-VISITOR", "", $fcode));
    }
    if (strpos($fcode, '-GUEST') !== false) {
      $fcode = trim(str_replace("-GUEST", "", $fcode));
    }
    if (strpos($fcode, '-MEDIA') !== false) {
      $fcode = trim(str_replace("-MEDIA", "", $fcode));
    }
    if (strpos($fcode, '-GP') !== false) {
      $fcode = trim(str_replace("-GP", "", $fcode));
    }
    if (strpos($fcode, '-VIB') !== false) {
      $fcode = trim(str_replace("-VIB", "", $fcode));
    }


    $vtimestamp = date("y-m-d_h-i-s");

    // we building raw data 
    //$codeContents  = 'BEGIN:EESY_PROFILE'."\n";       // "|J"
    $codeContents  = 'BEGIN:VCARD' . "\n";            // "|J"
    $codeContents .= 'FN:' . $fn . "\n";              //  .$ln."\n";
    $codeContents .= 'N:' . $ln . "\n";
    $codeContents .= 'EMAIL:' . $email . "\n";

    if ($XincludeAllProfile == "yes") {
      if ($mobile <> "") {
        $codeContents .= 'TEL:' . $mobile . "\n";
      }
      if ($position <> "") {
        $codeContents .= 'TITLE:' . $position . "\n";
      }
      if ($ctry <> "") {
        $codeContents .= 'ADR:' . $ctry . "\n";
      }
    }

    $codeContents .= 'ORG:' . $comp . "\n";           // ORG            
    $codeContents .= 'VRS:' . $vrsPrereg . "\n";          // ===== indicates VRS created the QRcode ====
    $codeContents .= 'STATUS:' . $vstat . "\n";

    if (isset($visitorType)) {
      $codeContents .= 'VTYPE:' . $visitorType . "\n";
    }
    $codeContents .= 'RCODE:' . $rcode . "\n";

    //$codeContents .= 'END:EESY_PROFILE';
    $codeContents .= 'END:VCARD';

    // generating
    //QRcode::png($codeContents, $tempDir.$fcode."-".$rcode.'.png', QR_ECLEVEL_L, 2);
    QRcode::png($codeContents, $tempDir . $fcode . "-" . $rcode . "-" . $vtimestamp . '.png', QR_ECLEVEL_L, 2);

    //$qvalue = $tempDir.$fcode."-".$rcode."-".$vtimestamp.'.png';
    $qvalue = $fcode . "-" . $rcode . "-" . $vtimestamp . '.png';

    // displaying
    //echo '<img src="'.EXAMPLE_TMP_URLRELPATH.'025.png" />'; 

    //unset($this->infiqr);

    return $qvalue;
  }

  function createQR($rcode, $fcode)
  //$qrcods = $this->createQR($data['fn'],$data['ln'],$data['email'],$data['mobile'],$data['comp'],$data['repcode'],$data['fcode']);
  {
    $this->load->library('infiQr');

    $tempDir = FCPATH . "idbadge/";

    // we building raw data 
    $codeContents  = $rcode;       // "|J"

    // generating
    //QRcode::png($codeContents, $tempDir.'025.png', QR_ECLEVEL_L, 3);
    QRcode::png($codeContents, $tempDir . $fcode . "-" . $rcode . '.png', QR_ECLEVEL_L, 3);

    $qvalue = $tempDir . $fcode . "-" . $rcode . '.png';

    // displaying
    //echo '<img src="'.EXAMPLE_TMP_URLRELPATH.'025.png" />'; 

    //unset($this->infiqr);

    return $qvalue;
  }

  function vibFormPDF($ref)
  {
    //$this->load->model('site_model');
    //$this->load->library('encrypt2');
    //$this->load->helper('url');

    //die("dir = ". __DIR__ ."<br>app path = ".APPPATH);      

    //$ref=explode("~",$this->encrypt2->decode2($ref));

    $decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $ref);
    $ref = explode("~", $this->encryption->decrypt($decryptX));
    //print_r($ref);      


    if (count($ref) == 3) {

      $sector = $ref[0];
      $fairCode = $ref[1];
      $pid = $ref[2];

      //echo $sector." - ".$fairCode." - ".$pid; die();
      // $pidTemp = $this->encrypt2->encode2('02~MFIO2017~9603');
      //  echo $pidTemp;
      //  exit();
      //$pidTemp = $this->encrypt2->decode2($this->input->get('pid'));

      $this->load->model('model_pdf');
      $data = $this->model_pdf->m_getInformation($pid, $fairCode, $sector);
      // print_r($data);
      //exit();
      //load mPDF library

      //$this->load->library('M_pdf');
      //$this->data['sector']= $sector;
      $this->data['title'] = "MY APPLICATION";
      $this->data['description'] = "APPLICATION CONTRACT";
      //now pass the data //

      if ($sector == "02") {
        $vPDF = "view_visitorpdf";
      } else {
        $vPDF = "view_visitorpdf_ifex";
      }

      $page1 = $this->load->view($vPDF, $data, true); //load the pdf_output.php by passing our data and get all data in $html varriable.

      //this the the PDF filename that user will get to download
      //$mypdfName= base_url("pdf");
      //$pdfFilePath ="pdf/".$this->encrypt2->encode2($pid)."-".time()."-download.pdf";

      //$pdfFilePath ="pdf/VIB-FORM-".$this->encrypt2->encode2($pid).".pdf";
      // echo $page1;
      // exit();
      //$TMP1 = $this->encryption->encrypt($pid);
      $TMP1 = $pid;
      $pdfFilePath = "pdf/VIB-FORM-" . $fairCode . "-" . str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1) . ".pdf";

      //update VIB table        
      $this->site_model->updateRecord("v_users_refer_buyer", "vib_form", $pdfFilePath, "rep_code = " . $pid);
      //die("aaa="); 
      //===============        
      //actually, you can pass mPDF parameter on this load() function
      //$pdf = $this->m_pdf->load();
      require_once(APPPATH . 'third_party/mpdf/vendor/autoload.php');
      //$pdf = new \Mpdf\Mpdf();

      $pdf = new \Mpdf\Mpdf(['tempDir' => APPPATH . '/temp']);

      $pdf->useSubstitutions = false;
      $pdf->simpleTables = true;

      //generate the PDF!
      $pdf->WriteHTML($page1, 2);
      // $pdf->AddPage();
      // $pdf->WriteHTML($page2,2);

      //$pdf->WriteHTML($page2,2);
      //offer it to user via browser download! (The PDF won't be saved on your server HDD)
      //$pdf->Output($pdfFilePath, "D");
      $pdf->Output($pdfFilePath, "F");
      //$pdf->Output($pdfFilePath, "I");
    }

    return $pdfFilePath;
  }


  public function calc_deadline($dateRegister)
  {
    $dateExpire = $dateRegister;
    $i = 0;
    while ($i < 3) {
      $deadline = date('Y-m-d H:i:s', strtotime($dateExpire . "+1 days")); //pag day lang
      if (date('l', strtotime($deadline)) != 'Saturday' && date('l', strtotime($deadline)) != 'Sunday') {
        $i++;
      }
      $dateExpire = $deadline;
    }
    return date('Y-m-d H:i:s, l', strtotime($dateExpire)); //tanggal lang yung l kapag di kasama ang day na i-save
  }


  function createPaymentOrder($vprofile, $vproject, $vamount, $amt_prof, $amt_stud, $servicetype, $paymentMode, $baseURL, $refcode)
  {

    //==== check if there is existing paymentOrder ============ select * from v_payment_order WHERE date_expire > CURRENT_TIMESTAMP()
    //$rsNotExpireAndPaid= $this->site_model->getRec("v_payment_order", "where date_expire > CURRENT_TIMESTAMP() and fair_code='".$vproject[0]['fair_code']."' and rep_code =?",$vprofile[0]['rep_code'], '');

    $getnum = $this->site_model->insertRecord("v_payment_order");

    $rs1 = $this->site_read_model->getRec("v_payment_order", "where refno = ?", $getnum, '');

    //$vxpireDate = $this->site_model->loadRec2("SELECT DATE_ADD('".$rs1[0]['date_apply']."' , INTERVAL 3 DAY) as xpire"); 
    $xpireDATE = $this->calc_deadline($rs1[0]['date_apply']);

    if ($refcode == "") {
      $useCode = REFERENCE_CODE;
    } else {
      $useCode = $refcode;
    }

    $vpaymentOrder = $useCode . "_" . sprintf("%'03d", $getnum);

    $tempFcode = explode("-", $vproject[0]['fair_code']);

    //die("aaa= ".$vxpireDate[0]['xpire']);

    $this->site_model->updateField("v_payment_order", "item_code", $vpaymentOrder, "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "rep_code", $vprofile[0]['rep_code'], "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "barcode", $vprofile[0]['barcode'], "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "fair_code", $tempFcode[0], "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "sector", $vproject[0]['sector'], "refno ='" . $getnum . "'");

    $this->site_model->updateField("v_payment_order", "date_register", $rs1[0]['date_apply'], "refno ='" . $getnum . "'");
    //$this->site_model->updateField("v_payment_order","date_expire",$vxpireDate[0]['xpire'],"refno ='".$getnum."'");
    $this->site_model->updateField("v_payment_order", "date_expire", $xpireDATE, "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "amount", $vamount, "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "number_professional", $_POST['prof'], "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "number_student", $_POST['stud'], "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "amount_professional", $amt_prof, "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "amount_student", $amt_stud, "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "remarks", $servicetype, "refno ='" . $getnum . "'");
    $this->site_model->updateField("v_payment_order", "mode_payment", $paymentMode, "refno ='" . $getnum . "'");

    $this->site_model->updateField("v_payment_order", "url_link_number", $_POST['prof'] + $_POST['stud'], "refno ='" . $getnum . "'");

    //====== if barkada package create link for registration =======================================

    if ($servicetype == "Barkada Package") {
      $vbaseURL = explode('?', $baseURL);
      //$vlinkURL = "fcode=".$vproject[0]['fair_code']."&pid=&vt=VISITOR&preg=1&refNumber=".$vpaymentOrder."&vrs=2&station=0";
      $vlinkURL = $vproject[0]['fair_code'] . "#VISITOR#1#" . $vpaymentOrder . "#2#0";
      //$elinkURL = $this->encrypt2->encode2($vlinkURL);


      $TMP1 = $this->encryption->encrypt($vlinkURL);
      $elinkURL = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);


      $urlPackage = $vbaseURL[0] . "?pkg=" . $elinkURL;

      $this->site_model->updateField("v_payment_order", "url_link", $urlPackage, "refno ='" . $getnum . "'");
    }

    //==============================================================================================       


    //die("aa= ".$xpireDATE);
    //}

    return $vpaymentOrder;
  }

  function paymentRequest()
  {
    // Terminal ID:        00000083
    // Transaction Key:    04f08c7c8b27fdb52ffdf99a0445dc34a2d7c066
    // Test URL:           https://testipg.apollo.com.ph:8443/transaction/verify
  }

  public function success()
  {
    $data['vtitle'] = "Registration Successful";
    $data['mess1'] = "<p>Your delegate code will be sent to you in an e-mail. Our team will issue an official receipt to confirmed participants during the event proper.
                <p>Should you need an advance copy, kindly notify <NAME_EMAIL>.
                <p>Thank you and see you this September 22-23!";

    $this->load->view('success', $data);
  }

  public function failed()
  {
    $data['vtitle'] = "Registration Failed";
    $data['mess1'] = 'Maximum number of slots availed';

    $this->load->view('failed', $data);
  }


  function buyerprofile()
  {
    //$this->load->model('site_model');                

    if (isset($_GET['fcode'])) {
      $data['fcode'] = $_GET['fcode'];
    } else {
      $data['fcode'] = $this->input->post('fcode');
    }

    // chk if PID is declared
    if (isset($_GET['pid'])) {
      $pid = $_GET['pid'];
    } else {
      $pid = $this->input->post('pid');
    }
    //die("aaa =".$pid);
    //**************************** GET vsend

    $data['base'] = $this->config->item('base_url'); // get base_url from config.php
    $data['main_page'] = $this->config->item('index_page'); // get index_page from config.php
    $data['css']  = $this->config->item('css');      // load mystyles.css defined in config.php 


    //chk if fcode exists and get project details
    $data['project'] = $this->master_model->getRec("busmatch_date", "where active='1' and FAIR_CODE = ?", strtoupper($data['fcode']), '');
    if ($data['project'] == '') {
      die("No Project found.");
    }
    foreach ($data['project'] as $proj1) {
      $data['vsector'] = $proj1['sector'];
      $pregClose  = $proj1['disable_prereg'];
    }
    $data['profiles'] = $this->site_model->getRec("v_contact_profile", "where pid = ?", $pid, '');
    if ($data['profiles'] == "") {
      die("Record Not Found, please <NAME_EMAIL> bong" . $pid);
    }
    foreach ($data['profiles'] as $rcode0) {
      $rcode = $rcode0['rep_code'];
    }

    $faircode = strtoupper($data['fcode']);
    $sectorCode = $data['vsector'];
    //=============== job function ===============================================
    $data['jobfunction'] = "";
    $datajobf = $this->site_model->getJoinRec("v_reference", "v_job_function", "item_code", $rcode, $faircode, $sectorCode, 'JOB_F');
    if (isset($datajobf)) {
      foreach ($datajobf as $jobf) {
        $data['jobfunction'] = $data['jobfunction'] . $jobf['C_Profile'] . ", ";
      }
    }
    //=============== nature of bussiness ========================================
    $data['v_repre'] = "";
    $dataRepre = $this->site_model->getJoinRec("v_reference", "v_representation", "item_code", $rcode, $faircode, $sectorCode, 'B2');
    if (isset($dataRepre)) {
      foreach ($dataRepre as $Repre) {
        $data['v_repre'] = $data['v_repre'] . $Repre['C_Profile'] . ", ";
      }
    }
    //=============== market segment =============================================
    $data['v_marketsegment'] = "";
    $datamarketsegment = $this->site_model->getJoinRec("v_reference", "v_mn_marketsegment", "item_code", $rcode, $faircode, $sectorCode, 'MN3');
    if (isset($datamarketsegment)) {
      foreach ($datamarketsegment as $marketsegment) {
        $data['v_marketsegment'] = $data['v_marketsegment'] . $marketsegment['C_Profile'] . ", ";
      }
    }
    //=============== annual sales ===============================================
    $data['v_annualsales'] = "";
    $dataannualsales = $this->site_model->getJoinRec("v_reference", "v_mn_annualsales", "item_code", $rcode, $faircode, $sectorCode, 'MN2');
    if (isset($dataannualsales)) {
      foreach ($dataannualsales as $annualsales) {
        $data['v_annualsales'] = $data['v_annualsales'] . $annualsales['C_Profile'] . ", ";
      }
    }
    //=============== sector/product =============================================
    $data['v_products'] = "";
    $dataproducts = $this->site_model->getJoinRec("v_reference", "v_genproducts", "prod_cat", $rcode, $faircode, $sectorCode, 'GP');
    if (isset($dataproducts)) {
      foreach ($dataproducts as $products) {
        $data['v_products'] = $data['v_products'] . $products['C_Profile'] . ", ";
      }
    }
    //=============== important factor ===========================================
    $data['v_Factor'] = "";
    $dataFactor = $this->site_model->getJoinRec("v_reference", "v_importantfactor", "item_code", $rcode, $faircode, $sectorCode, 'B10');
    if (isset($dataFactor)) {
      foreach ($dataFactor as $Factor) {
        $data['v_Factor'] = $data['v_Factor'] . $Factor['C_Profile'] . ", ";
      }
    }
    //=============== trade shows visited ========================================
    $data['v_showsimportant'] = "";
    $datashowsimportant = $this->site_model->getJoinRec("v_reference", "v_tradeshowsimportant", "item_code", $rcode, $faircode, $sectorCode, 'B11');
    if (isset($datashowsimportant)) {
      foreach ($datashowsimportant as $showsimportant) {
        $data['v_showsimportant'] = $data['v_showsimportant'] . $showsimportant['C_Profile'] . ", ";
      }
    }


    //$this->load->helper(array('form', 'url'));       //create the form opening   
    $this->load->library(array('form_validation', 'session'));

    if ($pregClose == "1") {
      $this->load->view('formsuccess', $data); // pre-registration CLOSE
    } else   
     if ($this->form_validation->run() == FALSE)  // runs the validation routine
    {                                       // include || $data['msg']=='Mismatch, try again' for captcha 
      $this->load->view('buyerprofile', $data);
    }
  } // buyerprofile


  function writeError($errDesc, $errSubj, $rcode, $bcode, $fcode, $sector, $errEmail, $vfunc)
  {

    $errRef = $this->site_model->insertRecord("v_email_send_status");
    $this->site_model->updateField("v_email_send_status", "remarks", $errDesc, "refno=" . $errRef);
    $this->site_model->updateField("v_email_send_status", "subject", $errSubj, "refno=" . $errRef);
    $this->site_model->updateField("v_email_send_status", "rep_code", $rcode, "refno=" . $errRef);
    $this->site_model->updateField("v_email_send_status", "barcode", $bcode, "refno=" . $errRef);
    $this->site_model->updateField("v_email_send_status", "fair_code", $fcode, "refno=" . $errRef);
    $this->site_model->updateField("v_email_send_status", "sector", $sector, "refno=" . $errRef);
    $this->site_model->updateField("v_email_send_status", "func_module", $vfunc, "refno=" . $errRef);
    $this->site_model->updateField("v_email_send_status", "email", $errEmail, "refno=" . $errRef);
  }

  function barcode($bcode, $fcode)
  {
    //$visitor_id=$this->uri->segment(3);
    //$faircode=$this->uri->segment(4);
    $this->load->library('zend');
    $this->zend->load('Zend/Barcode');

    //Zend_Barcode::render('code39', 'image', array('text' => $visitor_id), array());
    $barcode = Zend_Barcode::draw('code39', 'image', array('text' => $bcode, 'barHeight' => 30, 'factor' => 2, 'fontSize' => 8, 'drawText' => FALSE), array());

    /*$config = new Zend_Config(array(
      'barcode'        => 'code39',
        'barcodeParams'  => array('text' => $visitor_id, 'barHeight'=>30, 'factor'=>2),
        'renderer'       => 'image',
        'rendererParams' => array('imageType' => 'gif'),
      ));
      $barcode = Zend_Barcode::factory($config);
      */
    imagegif($barcode, 'assets/images/barcode/barcode' . $fcode . '_' . $bcode . '.gif');
    //echo $barcode."bong".$faircode.'_'.$visitor_id;
    //file_put_contents('assets/images', $barcode);  
  } //End of Barcode generation function


  function createcsv($email1, $email2, $barcode)
  {
    $tempDir = FCPATH . "pointwest/inbox/";
    $csv = "email1,email2 \n"; //Column headers
    $csv .= $email1 . ',' . $email2; //row data
    //$filename="forupload\\".$barcode.'.csv';
    $filename = $tempDir . $barcode . '.csv';
    $csv_handler = fopen($filename, 'w');
    fwrite($csv_handler, $csv);
    fclose($csv_handler);

    //createcsv ("<EMAIL>","<EMAIL>","2000002");    
  }

  function loadFromValue($projectDB, $ifEnable, $ifrequired, $step)
  {

    $vChk = array(

      'auto_tag_reg_status' => $this->check_status1($projectDB[0]['auto_tag_reg_status'], 0),

      'disable_prereg'     => $this->check_status1($projectDB[0]['disable_prereg'], 0),
      'send_confirmation_email'     => $this->check_status1($projectDB[0]['send_confirmation_email'], 0),
      'send_email_verification'     => $this->check_status1($projectDB[0]['send_email_verification'], 0),
      'full_screen_view'     => $this->check_status1($projectDB[0]['full_screen_view'], 0),
      'display_header_footer'     => $this->check_status1($projectDB[0]['display_header_footer'], 0),


      'show_company_name'    => $this->check_status1($projectDB[0]['show_company_name'], $ifEnable),
      'req_company_name'     => $this->check_status1($projectDB[0]['show_company_name'], $ifrequired),
      'show_address'         => $this->check_status1($projectDB[0]['show_address'], $ifEnable),
      'req_address'          => $this->check_status1($projectDB[0]['show_address'], $ifrequired),
      'show_country'         => $this->check_status1($projectDB[0]['show_country'], $ifEnable),
      'req_country'          => $this->check_status1($projectDB[0]['show_country'], $ifrequired),
      'show_work_phone'      => $this->check_status1($projectDB[0]['show_work_phone'], $ifEnable),
      'req_work_phone'       => $this->check_status1($projectDB[0]['show_work_phone'], $ifrequired),
      'show_website'         => $this->check_status1($projectDB[0]['show_website'], $ifEnable),
      'req_website'          => $this->check_status1($projectDB[0]['show_website'], $ifrequired),
      'show_social_media_account'       => $this->check_status1($projectDB[0]['show_social_media_accounts'], $ifEnable),
      'req_social_media_account'        => $this->check_status1($projectDB[0]['show_social_media_accounts'], $ifrequired),
      'show_title'           => $this->check_status1($projectDB[0]['show_title'], $ifEnable),
      'req_title'            => $this->check_status1($projectDB[0]['show_title'], $ifrequired),

      'show_authority_title' => $this->check_status1($projectDB[0]['show_authority_title'], $ifEnable),
      'req_authority_title'  => $this->check_status1($projectDB[0]['show_authority_title'], $ifrequired),

      'show_designation'     => $this->check_status1($projectDB[0]['show_designation'], $ifEnable),
      'req_designation'      => $this->check_status1($projectDB[0]['show_designation'], $ifrequired),
      'show_nationality'     => $this->check_status1($projectDB[0]['show_nationality'], $ifEnable),
      'req_nationality'      => $this->check_status1($projectDB[0]['show_nationality'], $ifrequired),
      'show_age_group'       => $this->check_status1($projectDB[0]['show_age_group'], $ifEnable),
      'req_age_group'        => $this->check_status1($projectDB[0]['show_age_group'], $ifrequired),
      'show_birthdate'       => $this->check_status1($projectDB[0]['show_birthdate'], $ifEnable),
      'req_birthdate'        => $this->check_status1($projectDB[0]['show_birthdate'], $ifrequired),
      'show_gender'          => $this->check_status1($projectDB[0]['show_gender'], $ifEnable),
      'req_gender'           => $this->check_status1($projectDB[0]['show_gender'], $ifrequired),
      'show_mobile_number'   => $this->check_status1($projectDB[0]['show_mobile_number'], $ifEnable),
      'req_mobile_number'    => $this->check_status1($projectDB[0]['show_mobile_number'], $ifrequired),

      'show_email'           => $this->check_status1($projectDB[0]['show_email'], $ifEnable),
      'req_email'            => $this->check_status1($projectDB[0]['show_email'], $ifrequired),

      'show_fname'           => $this->check_status1($projectDB[0]['show_first_name'], $ifEnable),
      'req_fname'            => $this->check_status1($projectDB[0]['show_first_name'], $ifrequired),

      'show_lname'           => $this->check_status1($projectDB[0]['show_last_name'], $ifEnable),
      'req_lname'            => $this->check_status1($projectDB[0]['show_last_name'], $ifrequired),


      //======================= include this as validation =============================================
      //'show_region'           => $this->check_status1($projectDB[0]['show_region'],0),
      //'req_region'            => $this->check_status1($projectDB[0]['show_region'],1),
      //================================================================================================

      'show_company_email'            => $this->check_status1($projectDB[0]['show_company_email'], $ifEnable),
      'req_show_company_email'        => $this->check_status1($projectDB[0]['show_company_email'], $ifrequired),
      'show_year_establish'           => $this->check_status1($projectDB[0]['show_year_establish'], $ifEnable),
      'req_show_year_establish'       => $this->check_status1($projectDB[0]['show_year_establish'], $ifrequired),

      'v_hotel_voucher'       => $this->check_status1($projectDB[0]['show_hotel'], $ifEnable),       // use for voucher code
      'v_hotel_voucher_req'   => $this->check_status1($projectDB[0]['show_hotel'], $ifrequired),     // use for voucher code

      'show_representation'           => $this->check_status1($projectDB[0]['show_representation'], 0),
      'req_show_representation'       => $this->check_status1($projectDB[0]['show_representation'], 2),

      'req_show_upload_files'       => $this->check_status1($projectDB[0]['show_upload_files'], 2),

      'show_remarks'                => $this->check_status1($projectDB[0]['show_remarks'], $ifEnable),
      'req_show_remarks'            => $this->check_status1($projectDB[0]['show_remarks'], $ifrequired),
      'page_show_remarks'           => $this->check_status1($projectDB[0]['show_remarks'], $step),

      'v_jobf'                      => $this->check_status1($projectDB[0]['show_jobfunction'], $ifEnable),
      'v_jobf_req'                  => $this->check_status1($projectDB[0]['show_jobfunction'], $ifrequired),
      'v_jobf_step'                 => $this->check_status1($projectDB[0]['show_jobfunction'], $step),

      'v_repre'                     => $this->check_status1($projectDB[0]['show_representation'], $ifEnable),
      'v_repre_req'                 => $this->check_status1($projectDB[0]['show_representation'], $ifrequired),
      'v_repre_step'                => $this->check_status1($projectDB[0]['show_representation'], $step),

      'v_show_organization'         => $this->check_status1($projectDB[0]['show_organization'], $ifEnable),
      'v_show_organization_req'     => $this->check_status1($projectDB[0]['show_organization'], $ifrequired),
      'v_show_organization_step'    => $this->check_status1($projectDB[0]['show_organization'], $step),

      'v_show_dietary_information'         => $this->check_status1($projectDB[0]['show_dietary_information'], $ifEnable),
      'v_show_dietary_information_req'     => $this->check_status1($projectDB[0]['show_dietary_information'], $ifrequired),
      'v_show_dietary_information_step'    => $this->check_status1($projectDB[0]['show_dietary_information'], $step),

      'v_show_user_agreement'         => $this->check_status1($projectDB[0]['show_user_agreement'], $ifEnable),
      'v_show_user_agreement_req'     => $this->check_status1($projectDB[0]['show_user_agreement'], $ifrequired),
      'v_show_user_agreement_step'    => $this->check_status1($projectDB[0]['show_user_agreement'], $step),

      'v_show_learnedabout'         => $this->check_status1($projectDB[0]['show_learnedabout'], $ifEnable),
      'v_show_learnedabout_req'     => $this->check_status1($projectDB[0]['show_learnedabout'], $ifrequired),
      'v_show_learnedabout_step'    => $this->check_status1($projectDB[0]['show_learnedabout'], $step),

      'v_showProducts'              => $this->check_status1($projectDB[0]['show_products'], $ifEnable),
      'v_showProducts_req'          => $this->check_status1($projectDB[0]['show_products'], $ifrequired),
      'v_showProducts_step'         => $this->check_status1($projectDB[0]['show_products'], $step),

      'v_1stTime'              => $this->check_status1($projectDB[0]['show_firsttime'], $ifEnable),
      'v_1stTime_req'          => $this->check_status1($projectDB[0]['show_firsttime'], $ifrequired),
      'v_1stTime_step'         => $this->check_status1($projectDB[0]['show_firsttime'], $step),

      'v_showAnnualSales'              => $this->check_status1($projectDB[0]['show_annual_sales'], $ifEnable),
      'v_showAnnualSales_req'          => $this->check_status1($projectDB[0]['show_annual_sales'], $ifrequired),
      'v_showAnnualSales_step'         => $this->check_status1($projectDB[0]['show_annual_sales'], $step),

      'v_showReason'              => $this->check_status1($projectDB[0]['show_reason'], $ifEnable),
      'v_showReason_req'          => $this->check_status1($projectDB[0]['show_reason'], $ifrequired),
      'v_showReason_step'         => $this->check_status1($projectDB[0]['show_reason'], $step),

      'v_show_existing_arrangements_with_philippine_suppliers'          => $this->check_status1($projectDB[0]['show_existing_arrangements_with_philippine_suppliers'], $ifEnable),
      'v_show_existing_arrangements_with_philippine_suppliers_req'      => $this->check_status1($projectDB[0]['show_existing_arrangements_with_philippine_suppliers'], $ifrequired),
      'v_show_existing_arrangements_with_philippine_suppliers_step'     => $this->check_status1($projectDB[0]['show_existing_arrangements_with_philippine_suppliers'], $step),

      'v_showMarketSegments'              => $this->check_status1($projectDB[0]['show_market_segments'], $ifEnable),
      'v_showMarketSegments_req'          => $this->check_status1($projectDB[0]['show_market_segments'], $ifrequired),
      'v_showMarketSegments_step'         => $this->check_status1($projectDB[0]['show_market_segments'], $step),

      'v_show_list_of_clients'              => $this->check_status1($projectDB[0]['show_list_of_clients'], $ifEnable),
      'v_show_list_of_clients_req'          => $this->check_status1($projectDB[0]['show_list_of_clients'], $ifrequired),
      'v_show_list_of_clients_step'         => $this->check_status1($projectDB[0]['show_list_of_clients'], $step),

      'v_show_activity_would_like_to_join'         => $this->check_status1($projectDB[0]['show_activity_would_like_to_join'], $ifEnable),
      'v_show_activity_would_like_to_join_req'     => $this->check_status1($projectDB[0]['show_activity_would_like_to_join'], $ifrequired),
      'v_show_activity_would_like_to_join_step'    => $this->check_status1($projectDB[0]['show_activity_would_like_to_join'], $step),

      'v_show_require_an_interpreter'         => $this->check_status1($projectDB[0]['show_require_an_interpreter'], $ifEnable),
      'v_show_require_an_interpreter_req'     => $this->check_status1($projectDB[0]['show_require_an_interpreter'], $ifrequired),
      'v_show_require_an_interpreter_step'    => $this->check_status1($projectDB[0]['show_require_an_interpreter'], $step),

      'v_show_interested_in_arrange_meetings'         => $this->check_status1($projectDB[0]['show_interested_in_arrange_meetings'], $ifEnable),
      'v_show_interested_in_arrange_meetings_req'     => $this->check_status1($projectDB[0]['show_interested_in_arrange_meetings'], $ifrequired),
      'v_show_interested_in_arrange_meetings_step'    => $this->check_status1($projectDB[0]['show_interested_in_arrange_meetings'], $step)


    );
    return $vChk;    //array($show_company_name,$req_company_name,$show_address,$req_address);
  }



  function loadShowValue($projectDB)
  {

    $vChk = array(

      'auto_tag_reg_status' => $this->check_status1($projectDB[0]['auto_tag_reg_status'], 0),

      'disable_prereg'     => $this->check_status1($projectDB[0]['disable_prereg'], 0),
      'send_confirmation_email'     => $this->check_status1($projectDB[0]['send_confirmation_email'], 0),
      'send_email_verification'     => $this->check_status1($projectDB[0]['send_email_verification'], 0),
      'full_screen_view'     => $this->check_status1($projectDB[0]['full_screen_view'], 0),
      'display_header_footer'     => $this->check_status1($projectDB[0]['display_header_footer'], 0),
      //'show_company_name'    => $this->check_status1($projectDB[0]['show_company_name'],0),
      //'req_company_name'     => $this->check_status1($projectDB[0]['show_company_name'],1),
      'show_address'         => $this->check_status1($projectDB[0]['show_address'], 0),
      'req_address'          => $this->check_status1($projectDB[0]['show_address'], 1),
      'show_country'         => $this->check_status1($projectDB[0]['show_country'], 0),
      'req_country'          => $this->check_status1($projectDB[0]['show_country'], 1),
      //'show_work_phone'      => $this->check_status1($projectDB[0]['show_work_phone'],0),
      //'req_work_phone'       => $this->check_status1($projectDB[0]['show_work_phone'],1),
      //'show_website'         => $this->check_status1($projectDB[0]['show_website'],0),
      //'req_website'          => $this->check_status1($projectDB[0]['show_website'],1),
      //'show_social_media_account'       => $this->check_status1($projectDB[0]['show_social_media_accounts'],0),
      //'req_social_media_account'        => $this->check_status1($projectDB[0]['show_social_media_accounts'],1),
      'show_title'           => $this->check_status1($projectDB[0]['show_title'], 0),
      'req_title'            => $this->check_status1($projectDB[0]['show_title'], 1),

      'show_remarks'           => $this->check_status1($projectDB[0]['show_remarks'], 0),
      'req_show_remarks'            => $this->check_status1($projectDB[0]['show_remarks'], 2),
      'page_show_remarks'           => $this->check_status1($projectDB[0]['show_remarks'], 1),

      'show_authority_title'           => $this->check_status1($projectDB[0]['show_authority_title'], 0),
      'req_authority_title'       => $this->check_status1($projectDB[0]['show_authority_title'], 1),

      'show_designation'     => $this->check_status1($projectDB[0]['show_designation'], 0),
      'req_designation'      => $this->check_status1($projectDB[0]['show_designation'], 1),
      'show_nationality'     => $this->check_status1($projectDB[0]['show_nationality'], 0),
      'req_nationality'      => $this->check_status1($projectDB[0]['show_nationality'], 1),
      'show_age_group'       => $this->check_status1($projectDB[0]['show_age_group'], 0),
      'req_age_group'        => $this->check_status1($projectDB[0]['show_age_group'], 1),
      'show_birthdate'       => $this->check_status1($projectDB[0]['show_birthdate'], 0),
      'req_birthdate'        => $this->check_status1($projectDB[0]['show_birthdate'], 1),
      'show_gender'          => $this->check_status1($projectDB[0]['show_gender'], 0),
      'req_gender'           => $this->check_status1($projectDB[0]['show_gender'], 1),
      'show_mobile_number'   => $this->check_status1($projectDB[0]['show_mobile_number'], 0),
      'req_mobile_number'    => $this->check_status1($projectDB[0]['show_mobile_number'], 1),

      //'show_email'           => $this->check_status1($projectDB[0]['show_email'],0),
      //'req_email'            => $this->check_status1($projectDB[0]['show_email'],1),

      'show_fname'           => $this->check_status1($projectDB[0]['show_first_name'], 0),
      'req_fname'            => $this->check_status1($projectDB[0]['show_first_name'], 1),

      'show_lname'           => $this->check_status1($projectDB[0]['show_last_name'], 0),
      'req_lname'            => $this->check_status1($projectDB[0]['show_last_name'], 1),


      //======================= include this as validation =============================================
      //'show_region'           => $this->check_status1($projectDB[0]['show_region'],0),
      //'req_region'            => $this->check_status1($projectDB[0]['show_region'],1),
      //================================================================================================


      'show_company_email'   => $this->check_status1($projectDB[0]['show_company_email'], 0),
      'req_show_company_email'        => $this->check_status1($projectDB[0]['show_company_email'], 1),
      'show_year_establish'           => $this->check_status1($projectDB[0]['show_year_establish'], 0),
      'req_show_year_establish'       => $this->check_status1($projectDB[0]['show_year_establish'], 1),
      'show_representation'           => $this->check_status1($projectDB[0]['show_representation'], 0),
      'req_show_representation'       => $this->check_status1($projectDB[0]['show_representation'], 2),

      'req_show_upload_files'       => $this->check_status1($projectDB[0]['show_upload_files'], 2)


    );
    return $vChk;    //array($show_company_name,$req_company_name,$show_address,$req_address);
  }

  function validation_step1($vType, $sector, $isVIB, $projectDB, $isEdit, $chkRequired)
  {
    //if($vType=="GENERAL PUBLIC" || $vType=="GP") {$vrequired = '';} else {$vrequired = 'required|';}

    $chk_coname =  ($this->check_status1($projectDB[0]['show_company_name'], $chkRequired) == "1" ? 'required|' : '');  // die("aaa= ". $allowView);

    $req_coname = (isset($isEdit) && $isEdit == "1" ? '' : $chk_coname);

    $req_ctry   =  ($this->check_status1($projectDB[0]['show_country'], $chkRequired) == "1" ? 'required|' : '');
    $req_title   =  ($this->check_status1($projectDB[0]['show_title'], $chkRequired) == "1" ? 'required|' : '');

    $req_authority_title   =  ($this->check_status1($projectDB[0]['show_authority_title'], $chkRequired) == "1" ? 'required|' : '');

    $req_designation   =  ($this->check_status1($projectDB[0]['show_designation'], $chkRequired) == "1" ? 'required|' : '');

    $req_email   =  ($this->check_status1($projectDB[0]['show_email'], $chkRequired) == "1" ? 'required|' : '');
    $req_fname   =  ($this->check_status1($projectDB[0]['show_first_name'], $chkRequired) == "1" ? 'required|' : '');
    $req_mi      =  ($this->check_status1($projectDB[0]['show_middle_initial'], $chkRequired) == "1" ? 'required|' : '');
    $req_lname   =  ($this->check_status1($projectDB[0]['show_last_name'], $chkRequired) == "1" ? 'required|' : '');

    $req_show_company_email   =  ($this->check_status1($projectDB[0]['show_company_email'], $chkRequired) == "1" ? 'required|' : '');
    $req_show_year_establish   =  ($this->check_status1($projectDB[0]['show_year_establish'], $chkRequired) == "1" ? 'required|' : '');

    $req_remarks2   =  ($this->check_status1($projectDB[0]['show_remarks'], 2) == "1" ? 'required|' : ''); //die($req_remarks2 );


    //if($vType=="TRADE BUYER" || $vType=="TB" || $vType=="MEDIA") {$vrequired = 'required|';} else {$vrequired = '';}

    //$vibRequired = ( ($isVIB=='REFER') ? 'required|' : ( ($isVIB=='25') ? 'required|' : "" ) );
    $req_designation = (($isVIB == 'REFER') ? 'required|' : $req_designation);
    //$data['step'] = "step1";   
    //$this->form_validation->set_message('is_unique', '%s already registered. Please use another Email address');
    //$this->form_validation->set_rules('email', 'Email', 'required|valid_email|is_unique[v_contact_profile.email]');  // add to chk if exist, is_unique[v_contact_profile.email]    
    $this->form_validation->set_rules('salutation', 'Title', $req_title . 'trim'); //xss_clean removes malicious data

    $this->form_validation->set_rules('authority_title', 'Authority Title', $req_authority_title . 'trim'); //xss_clean removes malicious data


    $this->form_validation->set_rules('fname', 'First Name', $req_fname . 'trim'); //xss_clean removes malicious data
    $this->form_validation->set_rules('lname', 'Last Name', $req_lname . 'trim'); //xss_clean removes malicious data
    $this->form_validation->set_rules('mi', 'Middle Initial', $req_mi . 'trim'); //xss_clean removes malicious data
    $this->form_validation->set_rules('title1', 'Designation', $req_designation . 'trim'); //xss_clean removes malicious data   
    $this->form_validation->set_rules('coname', 'Company', $req_coname . 'trim'); //xss_clean removes malicious data 
    $this->form_validation->set_rules('country', 'Country', $req_ctry . 'trim'); //
    $this->form_validation->set_rules('y_estab', 'Year Establish', $req_show_year_establish . 'trim'); //   	      

    $this->form_validation->set_rules('remarks2', 'Remarks', $req_remarks2 . 'trim');

    // if ==== bootstrapValidator is used disable this ================
    //$this->form_validation->set_rules('email', 'Email', 'required|valid_email');

    //======= POINTWEST =======================
    $this->form_validation->set_rules('email2', 'Company Email', $req_show_company_email . 'trim|valid_email');
  }

  function validation_step2($vType, $withBusCard, $isVIB, $projectDB, $isPhilippines, $chkRequired)
  {
    //$data['step'] = "step2";  

    $req_address =  ($this->check_status1($projectDB[0]['show_address'], $chkRequired) == "1" ? 'required|' : '');
    $req_telno   =  ($this->check_status1($projectDB[0]['show_work_phone'], $chkRequired) == "1" ? 'required|' : '');
    $req_website     =  ($this->check_status1($projectDB[0]['show_website'], $chkRequired) == "1" ? 'required|' : '');
    $req_mobile  =  ($this->check_status1($projectDB[0]['show_mobile_number'], $chkRequired) == "1" ? 'required|' : '');
    $show_social_media_account  =  ($this->check_status1($projectDB[0]['show_social_media_accounts'], $chkRequired) == "1" ? 'required|' : '');
    $req_nationality     =  ($this->check_status1($projectDB[0]['show_nationality'], $chkRequired) == "1" ? 'required|' : '');
    $req_age_group     =  ($this->check_status1($projectDB[0]['show_age_group'], $chkRequired) == "1" ? 'required|' : '');
    $req_birthdate     =  ($this->check_status1($projectDB[0]['show_birthdate'], $chkRequired) == "1" ? 'required|' : '');
    $req_gender     =  ($this->check_status1($projectDB[0]['show_gender'], $chkRequired) == "1" ? 'required|' : '');


    $req_website         = (($isVIB == 'REFER') ? 'required|' : $req_website);
    $req_nationality = (($isVIB == 'REFER') ? 'required|' : $req_nationality);
    //$isRequired  = ( ($isVIB=='25') ? 'required|' : "" );

    //die("xxx=".$req_web);    req_smedia

    //$this->form_validation->set_rules('street', 'Address', $vrequired.'trim'); //xss_clean removes malicious data
    $this->form_validation->set_rules('street', 'Address', $req_address . 'trim'); //xss_clean removes malicious data    $isRequired
    $this->form_validation->set_rules('city', 'City/State', $req_address . 'trim'); //xss_clean removes malicious data   $isRequired
    $this->form_validation->set_rules('zipcode', 'Zip Code', $req_address . 'trim'); //xss_clean removes malicious data  $isRequired

    //======================= include this as validation =============================================
    //======================= include this as validation =============================================

    // $req_region   =  ($this->check_status1($projectDB[0]['show_region'],1)=="1" ? 'required|' : '' );

    // if($isPhilippines == "Philippines") {
    //   $this->form_validation->set_rules('region', 'Region', $req_region.'trim');
    // } else {
    //   $this->form_validation->set_rules('region', 'Region','trim');
    // }
    //======================= include this as validation =============================================
    //======================= include this as validation =============================================

    $this->form_validation->set_rules('telno', 'Work Phone', $req_telno . 'trim'); //xss_clean removes malicious data
    $this->form_validation->set_rules('cell', 'Mobile', $req_mobile . 'trim'); //xss_clean removes malicious data         $vrequired
    $this->form_validation->set_rules('gender', "Gender", $req_gender . 'trim');

    $this->form_validation->set_rules('vmonth', "Birthdate", $req_birthdate . 'trim');

    $this->form_validation->set_rules('age_group', "Age Group", $req_age_group . 'trim');
    $this->form_validation->set_rules('nationality', "Nationality", $req_nationality . 'trim');
    //$this->form_validation->set_rules('country_of_origin', "Country of Origin", 'trim|required');
    //$this->form_validation->set_rules('fax', 'Fax', 'trim'); //xss_clean removes malicious data
    $this->form_validation->set_rules('webpage', 'Webpage', $req_website . 'trim'); //xss_clean removes malicious data 
    $this->form_validation->set_rules('facebook', 'Facebook', $show_social_media_account . 'trim'); //xss_clean removes malicious data 
    $this->form_validation->set_rules('linkedin', 'linkedin', 'trim'); //xss_clean removes malicious data 
    $this->form_validation->set_rules('blogsite', 'blogsite', 'trim'); //xss_clean removes malicious data 
    $this->form_validation->set_rules('twitter', 'twitter', 'trim'); //xss_clean removes malicious data 
    $this->form_validation->set_rules('instagram', 'instagram', 'trim'); //xss_clean removes malicious data 
    $this->form_validation->set_rules('pinterest', 'pinterest', 'trim'); //xss_clean removes malicious data 
    $this->form_validation->set_rules('weibo', 'weibo', 'trim'); //xss_clean removes malicious data                           

  }

  //  function validation_step3() {      
  //   $this->form_validation->set_rules('repre[]', 'Nature of Business', 'trim|required'); //xss_clean removes malicious data 
  //
  //  }  

  //$this->updateSurvey('v_job_function',$repcode,$barcode,strtoupper($data['fcode']),$data['vsector'],'jobf','other_jobf');

  function updateOrderPlaced($vtable, $rcode, $vbarcode, $vfcode, $vsec, $vfield1, $vfield2, $vfield3, $vremarks)
  {

    // ===== delete old record ===============

    $delete1 = $this->site_model->deleteRecTable($vtable, $rcode, $vfcode, $vsec);

    // =======================================
    $arrlength = count($this->input->post($vfield1)); //die("aaa=".$arrlength);
    for ($x = 0; $x < $arrlength; $x++) {

      if ($vfield1[$x] <> "" || $vfield2[$x] <> "" || $vfield3[$x] <> "") {
        $insert = $this->site_model->insertOrderPlacedTable($vtable, $rcode, $vbarcode, $vfield1[$x], $vfield2[$x], $vfield3[$x], $vremarks, $vfcode, $vsec);
      }
    }
  }

  function updatePayment($vtable, $rcode, $vbarcode, $vfcode, $vsec, $vfield, $vfield2, $referenceCode, $CreditCard, $ProjDescription, $email, $IPG)
  {

    //$delete1 = $this->site_model->deleteRecTable($vtable,$rcode,$vfcode,$vsec); 
    //$this->updatePayment('v_payment_online',$repcode,$barcode,strtoupper($data['fcode']),$data['vsector'],'epayment','showWorkshop',$referenceCode,$CreditCard,$data['vdesc'],$_POST['email'],'DBP');

    // =======================================
    $arrlength = count($this->input->post($vfield)); //die("aaa=".$arrlength);
    $rarray = $this->input->post($vfield);
    $rarray2 = $this->input->post($vfield2);  //die("bbb=".$vremarks);
    for ($x = 0; $x < $arrlength; $x++) {

      $tempvalue  = element($x, $rarray);    // helper(array('array')) required      
      $itemcode   = explode('#', $tempvalue); //print_r($itemcode); die("aaa=".$tempvalue);

      $tempvalue2 = element($x, $rarray2);    // helper(array('array')) required      
      //$tempvalue2 = $rarray2;                 //print_r($tempvalue2); die();
      $itemcode2  = explode('#', $tempvalue2); //die("aaa=".$tempvalue2);

      $insert = $this->site_model->insertRecTable($vtable, $rcode, $vbarcode, $itemcode[0], $itemcode[1], $vfcode, $vsec);

      $tmp = str_replace('<strong>', '', $itemcode2[1]);
      $serviceType = $ProjDescription . " - " . str_replace('</strong>', '', $tmp);


      $this->site_model->updateField($vtable, "amount", $itemcode2[0], "refno=" . $insert);
      $this->site_model->updateField($vtable, "currency", $itemcode2[2], "refno=" . $insert);
      $this->site_model->updateField($vtable, "serviceType", $serviceType, "refno=" . $insert);
      $this->site_model->updateField($vtable, "referenceCode", $referenceCode, "refno=" . $insert);
      $this->site_model->updateField($vtable, "date_apply", date('Y-m-d H:i:s'), "refno=" . $insert);

      switch ($IPG) {
        case "DBP":

          //if($CreditCard=="yes") {
          //$this->site_model->updateField($vtable,"terminalID",$terminalID,"refno=".$insert);        
          $requestToken = sha1(TERMINAL_ID . $referenceCode . "{" . TRANSACTON_KEY . "}");

          $this->site_model->updateField($vtable, "securityToken_request", $requestToken, "refno=" . $insert);
          //}
          break;
        case "DragonPay":

          $this->site_model->updateField($vtable, "email", $email, "refno=" . $insert);

          break;
      }



      // Terminal ID:        00000083
      // Transaction Key:    04f08c7c8b27fdb52ffdf99a0445dc34a2d7c066    
    }

    return $insert;
  }

  function updateSurvey($vtable, $rcode, $vbarcode, $vfcode, $vsec, $vfield, $vremarks)
  {

    //die("aaa=".$this->input->post('other_NotAttendingEvent2'));
    //die("aaa=".$this->input->post($vfield));
    if ($vremarks <> 'other_Agreement') {
      $delete1 = $this->site_model->deleteRecTable($vtable, $rcode, $vfcode, $vsec);
    }

    if (is_countable($this->input->post($vfield))) {
      // ===== delete old record ===============
      // if($vremarks<>'other_Agreement')                                                  // orig location
      // {                                                                                 // here not
      //    $delete1 = $this->site_model->deleteRecTable($vtable,$rcode,$vfcode,$vsec);	  // above
      // }                                                                                 //
      // =======================================
      // == in PHP 7.2 NULL in count() return Warning ==========
      // Be careful.. If $variable is already an array it will be [[$variable]] and count will be 1
      //$arrlength=count(array($this->input->post($vfield))); //die("aaa=".$arrlength); //count(array($variable));


      $arrlength = count($this->input->post($vfield)); // ==== ORIG
      $rarray = $this->input->post($vfield);        // ==== ORIG
      $rarray1 = $this->input->post($vremarks);  //die("bbb=".$vremarks);
      for ($x = 0; $x < $arrlength; $x++) {
        //$remarkValue = element($x,$rarray1); die("ccc=".$remarkValue);
        $tempvalue = element($x, $rarray);     // helper(array('array')) required		   
        $itemcode  = explode('#', $tempvalue); //die("aaa=".$tempvalue);
        //=== IMPT use "!==" instead  "!=" =====================
        //if (strpos($itemcode[1],"Other") !== false) {$remarks=$this->input->post($vremarks.$x);} //chk Others option contain value
        //if ($itemcode[2] === "textbox") {$remarks=$this->input->post($vremarks);} //chk Others option contain value		 
        if ($itemcode[2] === "textbox") {
          $remarks = $this->input->post($vremarks . $itemcode[3]);
        } //chk Others option contain value
        elseif ($itemcode[2] === "textarea") {
          $remarks = $this->input->post($vremarks . $itemcode[3]);
        } //chk Others option contain value					 			 
        else {
          $remarks = $itemcode[1];
        }
        // ===== insert new record ===============
        //$remarks = str_replace('/', '', $remarks);
        $remarks = addslashes($remarks);
        $insert = $this->site_model->insertRecTable($vtable, $rcode, $vbarcode, $itemcode[0], $remarks, $vfcode, $vsec);
      }
    }
  }

  function updateSurveyProd($vtable, $rcode, $vbarcode, $vfcode, $vsec, $vfield, $vremarks)
  {

    if (is_countable($this->input->post($vfield))) {
      // ===== delete old record ===============
      $delete1 = $this->site_model->deleteRecTable($vtable, $rcode, $vfcode, $vsec);
      // =======================================
      $arrlength = count($this->input->post($vfield));
      $rarray = $this->input->post($vfield);
      //$remarksValue = $this->input->post($vremarks);
      for ($x = 0; $x < $arrlength; $x++) {
        $tempvalue = element($x, $rarray);     // helper(array('array')) required		   
        $itemcode  = explode('#', $tempvalue);

        //$insert = $this->site_model->insertProdTable($vtable,$rcode,$vbarcode,$itemcode[0],$itemcode[2],$itemcode[1],"",$vfcode,$vsec);
        if ($vsec == "00") // 
        {
          $vremarks_minor = "";
        } else {
          $vremarks_minor = $itemcode[3];
        }

        //if (is_numeric($vremarks_minor)) { }

        $vremarks_minor = (is_numeric($vremarks_minor) ? "" : $vremarks_minor);

        $prodcode = ($itemcode[2] == 1 ? "" : $itemcode[2]);

        $insert = $this->site_model->insertProdTable($vtable, $rcode, $vbarcode, $itemcode[0], $prodcode, $itemcode[1], $vremarks_minor, $vfcode, $vsec);
      }
    }
  }

  // function getContinent($country) {

  //  $continent ="";
  //  if($country<>"") {    
  //    $temp1['conti'] = $this->site_read_model->getRec("citem_masterfile.v_reference","where switch='R1' and c_profile = ?",$country,"");
  //    foreach($temp1['conti'] as $r1) { $continent = $r1['c_code'];}
  //  }   
  //  return $continent;
  //}  

  public function check_status1($vFieldN, $varray)
  {
    $vstr = explode("_", $vFieldN);
    $vtmp = count($vstr) - 1;
    $value = (($vtmp >= $varray) ? $vstr[$varray] : "");
    //$value = $vstr[$varray];
    return $value;
  }

  public function email_check($str)
  {
    $data['chkEmail1'] = $this->site_read_model->getRec("v_contact_profile", "where email<>'' and email = ?", $str, '');
    //=======================================================
    // IF EMAIL DOES NOT EXIST DO NOT ACCEPT ================
    if ($data['chkEmail1'] == "") {
      $this->form_validation->set_message('email_check', 'The %s is not yet registered. Please register below');
      return FALSE;
    } else {
      return TRUE;
    }
  }

  public function check_upload($str, $vvalue)
  {
    if ($vvalue <> "") {
      $this->form_validation->set_message('check_upload', $vvalue);
      return FALSE;
    } else
      return TRUE;
  }

  public function check_values($str, $vvalue)
  {
    if ($vvalue <> 5) {
      $this->form_validation->set_message('check_values', ' only 5 reqistrants is required');
      return FALSE;
    } else
      return TRUE;
  }

  public function voucher_check($str, $repcode)
  {
    //$rs1= $this->site_model->loadRec("v_contact_profile","where voucher ='".$str."' limit 1");
    //die("aaa= ".$repcode);
    $strX = explode("_", $repcode);

    // if($strX[2]=="3") 
    // {
    //   $this->form_validation->set_message('voucher_check', 'Voucher Code');
    //   return FALSE; 
    // }

    $qryVoucher = $this->site_read_model->loadRec("v_voucher_code", "where item_code ='" . $str . "' and sector ='" . $strX[1] . "' limit 1");
    //$qryVoucher= $this->site_read_model->loadRec("v_voucher_code","where item_code ='".$str."' limit 1");

    if ($qryVoucher == "") {
      $this->form_validation->set_message('voucher_check', 'Invalid promo code. Please contact Secretariat - ' . $strX[4]);
      return FALSE;
    } else {
      foreach ($qryVoucher as $rs) {
        if ($rs['rep_code'] == 0)   //  || $rs['rep_code']==1
        {
          $qrycount = $this->site_read_model->countRec("v_voucher_code", "rep_code =" . $strX[0]);  //loadRec("v_voucher_code","where rep_code =".$repcode);
          if ($qrycount > 0) {
            $this->form_validation->set_message('voucher_check', 'You already have an existing Promo code.'); // check if meron NULL sa rep_code sa table
            return FALSE;
          } else {
            return TRUE;
          }
        } else if ($rs['rep_code'] == 1) {
          $this->form_validation->set_message('voucher_check', 'Promo code Void. Please contact Secretariat - ' . $strX[4]);
          return FALSE;
        } else {
          $this->form_validation->set_message('voucher_check', 'Promo code already in use');
          return FALSE;
        }
      }
    }
  }

  public function generateCode($characters)
  {
    /* list all possible characters, similar looking characters and vowels have been removed */
    $possible = '23456789bcdfghjkmnpqrstvwxyz';
    $code = '';
    $i = 0;
    while ($i < $characters) {
      $code .= substr($possible, mt_rand(0, strlen($possible) - 1), 1);
      $i++;
    }
    return $code;
  }

  private function _generateCaptcha()
  {
    $vals = array(
      'img_path' => './captcha_images/',
      'img_url' => base_url('captcha_images/'),
      //'font_path'     =>  dirname(__FILE__).'/assets/fonts/monofont.ttf',         
      'font_path'     =>  FCPATH . 'assets/fonts/monofont.ttf',       // remove to if uploaded ==> './assets/fonts/monofont.ttf'
      //'font_path'     =>  FCPATH.'./monofont.ttf',
      'img_width' => '150',
      'img_height' => '45',
      'word_length'   => '4',
      'font_size'     => '29',
      'expiration' => '7200',
      'pool'          => '0123456789abcdefghijklmnopqrstuvwxyz',
      'colors'        => array(
        'background' => array(255, 255, 255),
        'border' => array(255, 255, 255),
        'text' => array(0, 0, 0),
        'grid' => array(255, 40, 40)
      )

    );
    /* Generate the captcha */
    return create_captcha($vals);
  }

  public function checkCaptcha1($post)
  {
    if ($post['captcha'] == $post['ccode']) {
      return 'Success';
    } else {
      return 'Mismatch, try again';
    }
  }
  public function checkCaptcha2($post)
  {
    if ($post['captcha2'] == $post['ccode2']) {
      return 'Success';
    } else {
      return 'Mismatch, try again';
    }
  }

  public function generatePID()
  {
    $code = sha1(mt_rand(10000, 99999) . time() . $this->input->post('fcode'));
    return $code;
  }


  public function mess_Regular($paymentOrder, $ctry)
  {

    if ($ctry == 'Philippines') {
      if ($paymentOrder[0]['number_student'] == 0 && $paymentOrder[0]['number_professional'] == 0) {
        if (trim($paymentOrder[0]['remarks']) == 'Student Rate') {
          $txt = '<br>(Note: Kindly present your school ID at the registration counter for validation.)';
        } else {
          $txt = "";
        }

        $vclassification = $paymentOrder[0]['remarks'] . $txt;     //"Regular rate: For local participants";                

      } else {
        $vclassification = "For local participants:<p>Professional Rate - Php 7,200 x " . $paymentOrder[0]['number_professional'] . "<br>Student Rate - Php 6,500 x " . $paymentOrder[0]['number_student'] . " </p>";
      }

      $rates = "Php " . number_format($paymentOrder[0]['amount'], 2) . "<br>(Inclusive of VAT)";   //"Php 7,200.00";

      $vcontents = "<P><strong>For local participants:</strong></P>
                                <p>
                                  <img src='" . base_url("images/pnb_paymentslip.jpg") . "' alt='PNB Payment Slip' class='center' style='width:273px;height:445px;'> 
                                </p>";
    } else {
      $vclassification = "Regular rate: For delegates overseas <p><b>Note</b>: The <b>USD 145</b> registration fee shall be <b>remitted in full</b> to the organizer. Additional <b>bank charges</b> such as <u>foreign telegraphic transfer</u> shall be on the account of the registrant.</p>";
      $rates = "USD 145.00";
      $vcontents = "<P><strong>For overseas participants:</strong></P>                      
                                <p>Kindly pay in <u>US Dollars</u> through the bank account below:</p>
                                <table border='1' style='font-size:12px; width: 100%;'>
                                <tr style='background-color:#db037d; padding:5px;'><th style='padding:5px;'>US DOLLAR ACCOUNT</th><th>BANK DETAILS</th></tr>
                                <tr><td style='height:40px; padding:5px; text-align:left;' >Bank</td><td style='height:40px; padding:5px; text-align:left;'>Philippine National Bank</td></tr> 
                                <tr><td style='height:40px; padding:5px; text-align:left;' >Account Name</td><td style='height:40px; padding:5px; text-align:left;'>CITEM</td></tr>
                                <tr><td style='height:40px; padding:5px; text-align:left;' >Account number</td><td style='height:40px; padding:5px; text-align:left;'>1514-9000-5557</td></tr>
                                <tr><td style='height:40px; padding:5px; text-align:left;' >Bank address</td><td style='height:40px; padding:5px; text-align:left;'>Suite 101, CTC Building, 2232 Roxas Boulevard, Pasay City, Philippines</td></tr>
                                <tr><td style='height:40px; padding:5px; text-align:left;' >Swift code</td><td style='height:40px; padding:5px; text-align:left;'>PNBMPHMM</td></tr>
                                </table>";
    }


    $urlMess = "<p><strong>Payment number: " . $paymentOrder[0]['item_code'] . "</strong></p>
                          Thank you for signing up in the CREATE Philippines Masterclasses. To secure a slot in the event, kindly follow these (3) simple steps: 
                          <ul>
                            <li>Fill out Payment Slip and indicate <strong>Payment number</strong> included in this e-mail</li>
                            <li>Pay through our depository bank (PNB)</li>
                            <li>E-mail payment <NAME_EMAIL></li>
                          </ul>
                          <p>Please see applicable rate and event access:</p>
                          <table border='1' style='font-size:12px; width: 100%;'>
                          <tr style='background-color:#db037d; padding:5px;'><th style='padding:5px;'>CLASSIFICATION</th><th>RATE</th><th>ACCESS</th></tr>

                          <tr><td style='height:40px; padding:5px; text-align:center;' >" . $vclassification . "</td><td style='height:40px; padding:5px; text-align:center;'>" . $rates . "</td><td style='height:40px; padding:5px; text-align:center;'>Masterclass + Exhibit</td></tr> 

                          </table>

                          " . $vcontents . "                                            

                          <p>
                          <strong>Note:</strong> No account number will be required upon payment. Please settle payment within <strong><u>seventy-two (72)</u></strong> hours or <strong><u>three (3) business days</u></strong>  to ensure reservation of slots for the CREATE Philippines Masterclasses, otherwise reservation will expire. Seats are limited and will be on a <u>first-come, first-served</u> basis.
                          </p>
                          <p>For more information about the show, visit our website <a href='http://www.createphilippines.com'>CREATE Philippines</a>. And for any question or concern, you may get in touch with us through our e-mail at <a href='mailto:<EMAIL>'><EMAIL></a> or call us at +63-2.832-5044.
                          </p>
                          <p>Looking forward to having you onboard CREATE Philippines!</p>
                          <br>
                          <p>Cheers,</p>
                          <br>
                          <p>
                          Your CREATE Philippines Team
                          </p>";
    return $urlMess;
  }

  public function mess_earlyBird($pnumber, $ctry)
  {

    if ($ctry == 'Philippines') {
      $vclassification = "Early Bird rate: local participants (First 50 registrants)";
      $rates = "Php 5,500.00";
      $vcontents = "<P><strong>For local participants:</strong></P>
                        <p>
                          <img src='" . base_url("images/pnb_paymentslip.jpg") . "' alt='PNB Payment Slip' class='center' style='width:273px;height:445px;'> 
                        </p>";
    } else {
      $vclassification = "Early Bird rate: delegates overseas (first 50 registrants) <p><b>Note</b>: The <b>USD 112</b> registration fee shall be <b>remitted in full</b> to the organizer. All bank charges including <u>foreign telegraphic transfer</u> shall be on the account of the registrant.</p>";
      $rates = "USD 112.00";
      $vcontents = "<P><strong>For overseas participants:</strong></P>                      
                        <p>Kindly pay in <u>US Dollars</u> through the bank account below:</p>
                        <table border='1' style='font-size:12px; width: 100%;'>
                        <tr style='background-color:#db037d; padding:5px;'><th style='padding:5px;'>US DOLLAR ACCOUNT</th><th>BANK DETAILS</th></tr>
                        <tr><td style='height:40px; padding:5px; text-align:left;' >Bank</td><td style='height:40px; padding:5px; text-align:left;'>Philippine National Bank</td></tr> 
                        <tr><td style='height:40px; padding:5px; text-align:left;' >Account Name</td><td style='height:40px; padding:5px; text-align:left;'>CITEM</td></tr>
                        <tr><td style='height:40px; padding:5px; text-align:left;' >Account number</td><td style='height:40px; padding:5px; text-align:left;'>1514-9000-5557</td></tr>
                        <tr><td style='height:40px; padding:5px; text-align:left;' >Bank address</td><td style='height:40px; padding:5px; text-align:left;'>Suite 101, CTC Building, 2232 Roxas Boulevard, Pasay City, Philippines</td></tr>
                        <tr><td style='height:40px; padding:5px; text-align:left;' >Swift code</td><td style='height:40px; padding:5px; text-align:left;'>PNBMPHMM</td></tr>
                        </table>";
    }


    $urlMess = "<p><strong>Payment number: " . $pnumber . "</strong></p>
                  Thank you for signing up for the Early Bird rates of the CREATE Philippines Masterclasses. To avail your Early Bird discount, kindly follow these (3) simple steps: 
                  <ul>
                    <li>Fill out Payment Slip and indicate <strong>Payment number</strong> included in this e-mail</li>
                    <li>Pay through our depository bank (PNB)</li>
                    <li>E-mail payment <NAME_EMAIL></li>
                  </ul>
                  <p>Please see applicable rate and event access:</p>
                  <table border='1' style='font-size:12px; width: 100%;'>
                  <tr style='background-color:#db037d; padding:5px;'><th style='padding:5px;'>CLASSIFICATION</th><th>RATE</th><th>ACCESS</th></tr>

                  <tr><td style='height:40px; padding:5px; text-align:left;' >" . $vclassification . "</td><td style='height:40px; padding:5px; text-align:center;'>" . $rates . "</td><td style='height:40px; padding:5px; text-align:center;'>Masterclass + Exhibit</td></tr> 

                  </table>

                  " . $vcontents . "                                            

                  <p>
                  <strong>Note:</strong> Please settle payment within <strong>seventy-two (72) hours or three (3) business days</strong>, otherwise early bird slots will expire, and regular rates shall apply.
                  </p>
                  <p>Early Bird rates will be until <strong>10 August 2018</strong> only or subject to availability of slots. Limited slots available and will be on a <u>first-come, first-served basis</u>.
                  </p>
                  <p>For more information about the show, visit our website <a href='http://www.createphilippines.com'>CREATE Philippines</a>. And for any question or concern, you may get in touch with us through our e-mail at <a href='mailto:<EMAIL>'><EMAIL></a> or call us at +63-2.832-5044.
                  </p>
                  <p>Looking forward to having you onboard CREATE Philippines!</p>
                  <br>
                  <p>Cheers,</p>
                  <br>
                  <p>
                  Your CREATE Philippines Team
                  </p>";

    return $urlMess;
  }

  public function messageReferer($fairdesc, $link, $vfrom, $bannerpic, $othinfo, $xsector, $refCode)
  {

    switch ($xsector) {
      case "01":
        $fbook = "ifexphilippines";
        $tweet = "IFEXPhilippines";
        $insta = "ifexphilippines";
        break;
      case "02":
        $fbook = "ManilaFAMEofficial";
        $tweet = "TheManilaFAME";
        $insta = "manilafame";
        break;
      case "20":
        $fbook = "createphilippines";
        $tweet = "createphils";
        $insta = "createphilippines";
        break;
      default:
        $fbook = "";
        $tweet = "";
        $insta = "";
    }

    $info = explode("~", $othinfo);

    $mess = "<html><head><title>" . $fairdesc . "</title>
           <style>
            .page-wrap {
              width: 800px;
              margin: 0 auto;
            } 
            td {
             height: 101px;
             vertical-align: middle;
            }
            .round1 {
              border: 1px solid gray;
              border-radius: 10px; 
              width: 720px;
              padding: 10px;
            }
            div {
              margin: 0px 50px 0px 50px;
            }
            a:link
            {
              color:#000000
            }

            .center {
                display: block;
                margin-left: auto;
                margin-right: auto;
                width: 50%;
            }

           </style>

           </head><body class='page-wrap'>
           
           <div class='round1'>
           <table cellpadding='0' cellspacing='0' border='0' width='720'>
           <tr>
              <td align='right' valign='top' ><img src='" . $bannerpic . "' width='720px' alt='" . $fairdesc . "'></td>
           </tr>
           <tr>
            <td valign='top'><br><font size='2' face='Verdana, Arial, Helvetica, sans-serif'>Dear " . $info[0] . ",<br><br>" . $link . "<br><br><br>Ref # " . $refCode . "<br><br></td>
       	   </tr>           

       	   <tr>
            <td bgcolor='#ebecee' style='background-color:#ebecee; height:20px;'>  
              <table border='0' cellpadding='0' cellspacing='0' align='left' style='width:50%; height:20px;'>
                <tr>
                  <td align='left' bgcolor='#ebecee' style='background-color:#ebecee; height:20px;'>
                    <a href='http://www.dti.gov.ph/' target='_blank'><img src='http://www.citem.com.ph/citemprojects/pix/foremail/dti.jpg' width='78' height='96' alt='DTI' border='0'></a>
                    <a href='http://www.citem.gov.ph/' target='_blank'><img src='http://www.citem.com.ph/citemprojects/pix/foremail/citem.jpg' width='70' height='96' alt='CITEM' border='0'></a>
                  </td>
                </tr>
              </table>                        
              <table border='0' cellpadding='0' cellspacing='3' align='right' style='width:50%;'>
                <tr>
                  <td align='right' bgcolor='#ebecee' style='background-color:#ebecee;'>
                    <a href='https://www.facebook.com/" . $fbook . "' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/facebook_new.png' border='0' alt='Facebook' /></a>
                    <a href='https://twitter.com/" . $tweet . "' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/twitter_new.png' border='0' alt='Twitter' /></a>
                    <a href='https://www.instagram.com/" . $insta . "' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/instagram_new.png' border='0' alt='Instagram' /></a>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
             <td style='height:20px; font-size:9px; font-family:Verdana, Arial, Helvetica, sans-serif;'>This email was sent by " . $fairdesc . " thru CITEM's Visitor Registration System.</td>
           </tr>
          </table>
          
          </div>
          </body></html>";

    return $mess;
  }

  public function messagePrereg($fairdesc, $link, $vfrom, $bannerpic, $emailExist, $xsector)
  {

    $vtxt2 = ($xsector == "20" ? "" : "");      // Masterclasses

    switch ($xsector) {
      case "02":

        $fbook = "ManilaFAMEofficial";
        $tweet = "TheManilaFAME";
        $insta = "manilafame";
        break;
      case "20":
        $fbook = "createphilippines";
        $tweet = "createphils";
        $insta = "createphilippines";
        break;
      case "25":
        $fbook = "SSXPhilippines";
        $tweet = "SSXPhilippines";
        $insta = "ssx.philippines";
        break;
      default:
        $fbook = "";
        $tweet = "";
        $insta = "";
    }

    if ($emailExist == '2') {
      $vtxt = explode("_", $vfrom);
      $mess = "<html><head><title>" . $fairdesc . "</title>
            <style>
            .page-wrap {
            width: 800px;
            margin: 0 auto;
            } 
            td {
             height: 101px;
             vertical-align: middle;
            }
            .round1 {
            border: 1px solid gray;
            border-radius: 10px; 
            width: 720px;
            padding: 10px;
            }
            div {
            margin: 0px 50px 0px 50px;
            }
            </style>
            </head>
            <body class='page-wrap' oncontextmenu='return false;'>    <!-- DISABLE RIGHT click ==== oncontextmenu='return false;' -->
            <!-- <body class='page-wrap'> -->
            <div class='round1'>
            <table cellpadding='0' cellspacing='0' border='0' width='600'>
            <tr>
              <td align='right' valign='top' ><img src='" . $bannerpic . "' width='720px'></td>
            </tr>
            <tr><td valign='top'><br><font size='2' face='Verdana, Arial, Helvetica, sans-serif'>
            Hi " . $vtxt[2] . "! 
            <br><br>         
		        This is an automated email to inform you that Mr./Ms. <strong>" . $vtxt[0] . "</strong> of <strong>" . $vtxt[1] . "</strong> has completed the <a href='" . $link . "' target='_blank'>Hotel Booking Form</a> 
            for " . $fairdesc . ". Please give acknowledgement and confirmation within 24 hours.<br><br>To complete the Hotel Booking Form do not forget to ask their credit card details.
            <br><br>
            Thank you.
            <br><br><br><br>
            </td></tr></table></div></body></html>";
    } elseif ($emailExist == '0') {

      $vtxt = explode("^", $vfrom);
      $salu = (trim($vtxt[1]) == "" ? "Hi" : "Dear Mr./Ms. " . $vtxt[1]);
      $mess = "<html><head><title>" . $fairdesc . "</title>
            <style>
            .page-wrap {
            width: 800px;
            margin: 0 auto;
            } 
            td {
             height: 101px;
             vertical-align: middle;
            }

            div {
              width: 600px; 
              border: 0px solid #000000;
              /*margin: 0px 50px 0px 50px;*/
            }

            .round1 {
            border: 1px solid gray;
            border-radius: 10px; 
            width: 720px;
            padding: 10px;
            }

            </style>
            </head>
            <body class='page-wrap' oncontextmenu='return false;'>    <!-- DISABLE RIGHT click ==== oncontextmenu='return false;' -->
            <!-- <body class='page-wrap'> -->
            <div class='round1'>
            <table cellpadding='0' cellspacing='0' border='0' width='600'>
            <tr>
              <td align='right' valign='top' ><img src='" . $bannerpic . "' width='720px'></td>
            </tr>
            <tr><td valign='top'><br><font size='2' face='Verdana, Arial, Helvetica, sans-serif'>
            " . $salu . ",<br><br>
            <div class='wraptxt'>
            Your email address was successfully validated. Please click the 
            <a href='" . $link . "' target='_blank'>LINK</a> or you may copy and paste the url below to your browser
            to proceed with your pre-registration for the " . trim($fairdesc . " " . $vtxt2) . ".<br><br>
            <a href='" . $link . "' target='_blank'>
            <div style='word-wrap: break-word;'>"
        . $link .
        "</div></a>
            <br><br>
            To avoid any inconvenience, please add " . $vtxt[0] . " to your contact email-address 
            or safe sender list.<br><br>Thank you. <br><br>
            </div>
            </td>
            </tr>

            <tr>
            <td bgcolor='#ebecee' style='background-color:#ebecee; height:20px;'>  
              <table border='0' cellpadding='0' cellspacing='0' align='left' style='width:50%; height:20px;'>
                <tr>
                  <td align='left' bgcolor='#ebecee' style='background-color:#ebecee; height:20px;'>
                    <a href='http://www.dti.gov.ph/' target='_blank'><img src='http://www.citem.com.ph/citemprojects/pix/foremail/dti.jpg' width='78' height='96' alt='DTI' border='0'></a>
                    <a href='http://www.citem.gov.ph/' target='_blank'><img src='http://www.citem.com.ph/citemprojects/pix/foremail/citem.jpg' width='70' height='96' alt='CITEM' border='0'></a>
                  </td>
                </tr>
              </table>                        
              <table border='0' cellpadding='0' cellspacing='3' align='right' style='width:50%;'>
                <tr>
                  <td align='right' bgcolor='#ebecee' style='background-color:#ebecee;'>
                    <a href='https://www.facebook.com/" . $fbook . "' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/facebook_new.png' border='0' alt='Facebook' /></a>
                    <a href='https://twitter.com/" . $tweet . "' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/twitter_new.png' border='0' alt='Twitter' /></a>
                    <a href='https://www.instagram.com/" . $insta . "' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/instagram_new.png' border='0' alt='Instagram' /></a>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
             <td style='height:20px; font-size:8px; font-family:Verdana, Arial, Helvetica, sans-serif;'>This email was sent by " . $fairdesc . " thru CITEM's Visitor Registration System.</td>
           </tr>

            </table></div></body></html>";
    } else {
      $vtxt = explode("^", $vfrom);
      $salu = (trim($vtxt[1]) == "" ? "Hi" : "Dear Mr./Ms. " . $vtxt[1]);
      $mess = "<html><head><title>" . $fairdesc . "</title>
            <style>
            .page-wrap {
            width: 800px;
            margin: 0 auto;
            } 
            td {
             height: 101px;
             vertical-align: middle;
            }

            div {
              width: 600px; 
              border: 0px solid #000000;
              /*margin: 0px 50px 0px 50px;*/
            }

            .round1 {
            border: 1px solid gray;
            border-radius: 10px; 
            width: 720px;
            padding: 10px;
            word-wrap: break-word;
            }
            </style>
            </head>
            <body class='page-wrap' oncontextmenu='return false;'>    <!-- DISABLE RIGHT click ==== oncontextmenu='return false;' -->
            <!-- <body class='page-wrap'> -->
            <div class='round1'>
            <table cellpadding='0' cellspacing='0' border='0' width='600' >
            <tr>
              <td align='right' valign='top' ><img src='" . $bannerpic . "' width='720px'></td>
            </tr>
            <tr><td valign='top'><br><font size='2' face='Verdana, Arial, Helvetica, sans-serif'>
            " . $salu . ",<br><br>
            
            Your email address was previously registered. Please click the
            <a href='" . $link . "' target='_blank'>LINK</a> or you may copy and paste the url below to your browser
            to review and update your information for the " . trim($fairdesc . " " . $vtxt2) . ".<br><br>
            <a href='" . $link . "' target='_blank'>
            <div style='word-wrap: break-word;'>"
        . $link .
        "</div></a> <br><br>
            To avoid any inconvenience, please add " . $vtxt[0] . " to your contact email-address 
            or safe sender list.<br><br>Thank you. <br><br>

            </td>
            </tr>

            <tr>
            <td bgcolor='#ebecee' style='background-color:#ebecee; height:20px;'>  
              <table border='0' cellpadding='0' cellspacing='0' align='left' style='width:50%; height:20px;'>
                <tr>
                  <td align='left' bgcolor='#ebecee' style='background-color:#ebecee; height:20px;'>
                    <a href='http://www.dti.gov.ph/' target='_blank'><img src='http://www.citem.com.ph/citemprojects/pix/foremail/dti.jpg' width='78' height='96' alt='DTI' border='0'></a>
                    <a href='http://www.citem.gov.ph/' target='_blank'><img src='http://www.citem.com.ph/citemprojects/pix/foremail/citem.jpg' width='70' height='96' alt='CITEM' border='0'></a>
                  </td>
                </tr>
              </table>                        
              <table border='0' cellpadding='0' cellspacing='3' align='right' style='width:50%;'>
                <tr>
                  <td align='right' bgcolor='#ebecee' style='background-color:#ebecee;'>
                    <a href='https://www.facebook.com/" . $fbook . "' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/facebook_new.png' border='0' alt='Facebook' /></a>
                    <a href='https://twitter.com/" . $tweet . "' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/twitter_new.png' border='0' alt='Twitter' /></a>
                    <a href='https://www.instagram.com/" . $insta . "' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/instagram_new.png' border='0' alt='Instagram' /></a>
                  </td>
                </tr>
              </table>
            </td>
            </tr>
            <tr>
             <td style='height:20px; font-size:8px; font-family:Verdana, Arial, Helvetica, sans-serif;'>This email was sent by " . $fairdesc . " thru CITEM's Visitor Registration System.</td>
            </tr>

            </table></div></body></html>";
    }

    return $mess;
  }


  public function generateEmail($txtemailfrom, $vpid, $regThru, $faircode, $sectorCode, $project, $vib)
  {

    foreach ($project as $proj1) {
      $pregClose  = $proj1['disable_prereg'];
      $v_jobf        = $this->check_status1($proj1['show_jobfunction'], 0); // chek value of explode (0 - array[0] , 1 - array[1])	  
      $v_represent   = $this->check_status1($proj1['show_representation'], 0);
      $v_showMarketSegments = $this->check_status1($proj1['show_market_segments'], 0);
      $v_showAnnualSales =  $this->check_status1($proj1['show_annual_sales'], 0);
      $v_showReason = $this->check_status1($proj1['show_reason'], 0);
      $v_showLearnedabout = $this->check_status1($proj1['show_learnedabout'], 0);
      $v_showImpt = $this->check_status1($proj1['show_trade_shows_important'], 0);
      $v_1stTime  = $this->check_status1($proj1['show_firsttime'], 0);
      $v_showFrequentTravelToAsia = $this->check_status1($proj1['show_frequent_travel_to_asia'], 0);
      $v_showProducts = $this->check_status1($proj1['show_products'], 0);
      $v_showspecificProducts = $this->check_status1($proj1['show_specific_product'], 0);
      $v_showProductClassification = $this->check_status1($proj1['show_product_classification'], 0);
      $v_showImportantFactor = $this->check_status1($proj1['show_important_factor'], 0);
      $v_show_activity_would_like_to_join         = $this->check_status1($proj1['show_activity_would_like_to_join'], 0);
      $v_show_interested_in_arrange_meetings      = $this->check_status1($proj1['show_interested_in_arrange_meetings'], 0);
      $v_show_existing_arrangements_with_philippine_suppliers      = $this->check_status1($proj1['show_existing_arrangements_with_philippine_suppliers'], 0);
      $v_show_list_of_clients = $this->check_status1($proj1['show_list_of_clients'], 0);
      $v_showWorkshop     = $this->check_status1($proj1['show_workshop_or_seminar'], 0);
      $v_hotel_voucher    = $this->check_status1($proj1['show_hotel'], 0);
      $v_show_organization = $this->check_status1($proj1['show_organization'], 0);
      $v_show_require_an_interpreter = $this->check_status1($proj1['show_require_an_interpreter'], 0);

      $v_show_year_establish = $this->check_status1($proj1['show_year_establish'], 0);
    }

    $dataProfile = $this->site_read_model->getRec("v_contact_profile", "where pid = ?", $vpid, '');
    foreach ($dataProfile as $rcode0) {
      $rcode = $rcode0['rep_code'];
      //$voucher = $rcode0['voucher']; 
    }

    //======== visitor type (TRADE BUYER, VISITOR, MEDIA)

    $VisitorType = "";
    $VisitorStatus = "";
    $VisitorRemarks = "";
    $buyerClass = "";

    if (strpos($faircode, '-VISITOR') !== false) {
      $faircode = trim(str_replace("-VISITOR", "", $faircode));
    }
    if (strpos($faircode, '-GUEST') !== false) {
      $faircode = trim(str_replace("-GUEST", "", $faircode));
    }
    if (strpos($faircode, '-MEDIA') !== false) {
      $faircode = trim(str_replace("-MEDIA", "", $faircode));
    }
    if (strpos($faircode, '-GP') !== false) {
      $faircode = trim(str_replace("-GP", "", $faircode));
    }
    if (strpos($faircode, '-VIB') !== false) {
      $faircode = trim(str_replace("-VIB", "", $faircode));
    }

    $dataVisitorType = $this->site_read_model->getRec("v_attendance", "where fair_code='" . strtoupper($faircode) . "' and sector='" . $sectorCode . "' and rep_code = ?", $rcode, '');
    if ($dataVisitorType <> "") {
      foreach ($dataVisitorType as $rvtype1) {
        $VisitorType    = $rvtype1['visitor_type'];
        $VisitorStatus  = $rvtype1['visitor_status'];
        $VisitorRemarks = $rvtype1['visitor_status_remarks'];
        $buyerClass     = $rvtype1['buyerclass'];
      }
    }

    $v_hotelvoucher = '';
    if ($v_hotel_voucher == '1') {
      $dataVoucher = $this->site_read_model->getRec("v_voucher_code", "where rep_code = ?", $rcode, '');
      if (isset($dataVoucher)) {
        foreach ($dataVoucher as $rsVoucher) {
          $v_hotelvoucher = $rsVoucher['item_code'];
        }
      }
    }
    //===================================================  

    $v_show_activity = '';
    if ($v_show_activity_would_like_to_join == '1') {
      $dataactivity = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_join_activity", "item_code", $rcode, $faircode, $sectorCode, 'ACT1');
      if (isset($dataactivity)) {
        foreach ($dataactivity as $Vactivity) {
          $v_show_activity = $v_show_activity . $Vactivity['c_profile'] . ", ";
        }
      }
    }
    //===================================================
    $vshow_meeting = '';
    if ($v_show_interested_in_arrange_meetings == '1') {
      $interest_meetings = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_arranged_meeting", "item_code", $rcode, $faircode, $sectorCode, 'MET1');
      if (isset($interest_meetings)) {
        foreach ($interest_meetings as $Vmeeting) {
          $vshow_meeting = $vshow_meeting . $Vmeeting['c_profile'] . ", ";
        }
      }
    }

    //======== check if active in BUSSMATCH_DATE ========
    $jobfunction = '';
    if ($v_jobf == '1') {
      $datajobf = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_job_function", "item_code", $rcode, $faircode, $sectorCode, 'job_f');
      if (isset($datajobf)) {
        foreach ($datajobf as $jobf) {
          $jobfunction = $jobfunction . $jobf['c_profile'] . ", ";
        }
      }
    }
    //===================================================
    //======== check if active in BUSSMATCH_DATE ========
    $v_repre = '';
    if ($v_represent == '1') {
      if ($VisitorType == "VISITOR" || $VisitorType == "GENERAL PUBLIC" || $VisitorType == "GUEST") {
        $qRy = "SELECT a.c_profile,a.c_code,a.switch,a.sector,b.* FROM " . MASTER_DB . ".v_reference AS a 
              Inner Join v_representation AS b ON a.c_code = b.item_code 
              WHERE a.switch ='B2' AND b.rep_code = " . $rcode . " AND b.fair_code = '" . $faircode . "' 
              AND b.sector = '" . $sectorCode . "' AND a.sector LIKE '%30%'";
        $dataRepre = $this->site_read_model->loadRec2($qRy);
      } else {
        $dataRepre = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_representation", "item_code", $rcode, $faircode, $sectorCode, 'B2');
      }

      if (isset($dataRepre)) {
        foreach ($dataRepre as $Repre) {
          $ifOthers = ($Repre['c_profile'] == "Others (please specify)" ? "Others - " : "");
          $v_repre = $v_repre . $ifOthers . $Repre['remarks'] . ", ";
        }
      }
    }

    //===================================================
    //======== check if active in BUSSMATCH_DATE ========
    $v_org_assoc = '';
    if ($v_show_organization == '1') {
      $v_organization = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_organization", "item_code", $rcode, $faircode, $sectorCode, 'OR');
      if (isset($v_organization)) {
        foreach ($v_organization as $org_assoc) {
          $v_org_assoc = $v_org_assoc . $org_assoc['c_profile'] . ", ";
        }
      }
    }
    //===================================================
    //======== check if active in BUSSMATCH_DATE ========
    $v_marketsegment = '';
    if ($v_showMarketSegments == '1') {
      $datamarketsegment = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_mn_marketsegment", "item_code", $rcode, $faircode, $sectorCode, 'MN3');
      if (isset($datamarketsegment)) {
        foreach ($datamarketsegment as $marketsegment) {
          $v_marketsegment = $v_marketsegment . $marketsegment['c_profile'] . ", ";
        }
      }
    }
    //===================================================      
    //======== check if active in BUSSMATCH_DATE ========
    $v_annualsales = '';
    if ($v_showAnnualSales == '1') {
      $dataannualsales = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_mn_annualsales", "item_code", $rcode, $faircode, $sectorCode, 'MN2');
      if (isset($dataannualsales)) {
        foreach ($dataannualsales as $annualsales) {
          $v_annualsales = $v_annualsales . $annualsales['c_profile'] . ", ";
        }
      }
    }
    //===================================================
    //======== check if active in BUSSMATCH_DATE ========
    $v_Reason = '';
    if ($v_showReason == '1') {
      $dataReason = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_showreason", "item_code", $rcode, $faircode, $sectorCode, 'MN1');
      if (isset($dataReason)) {
        foreach ($dataReason as $Reason) {
          $ifOthers = ($Reason['c_profile'] == "Others (please specify)" ? "Others - " : "");
          $v_Reason = $v_Reason . $ifOthers . $Reason['remarks'] . ", ";
        }
      }
    }
    //===================================================  
    //======== check if active in BUSSMATCH_DATE ========
    $v_informthru = '';
    if ($v_showLearnedabout == '1') {
      $datainformthru = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_informthru", "item_code", $rcode, $faircode, $sectorCode, 'B1');
      if (isset($datainformthru)) {
        foreach ($datainformthru as $informthru) {
          $ifOthers = ($informthru['c_profile'] == "Others (please specify)" ? "Others - " : "");
          $v_informthru = $v_informthru . $ifOthers . $informthru['remarks'] . ", ";
        }
      }
    }
    //===================================================    
    //======== check if active in BUSSMATCH_DATE ========
    $v_showsimportant = '';
    if ($v_showImpt == '1') {
      $datashowsimportant = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_tradeshowsimportant", "item_code", $rcode, $faircode, $sectorCode, 'B11');
      if (isset($datashowsimportant)) {
        foreach ($datashowsimportant as $showsimportant) {
          $v_showsimportant = $v_showsimportant . $showsimportant['c_profile'] . ", ";
        }
      }
    }
    //===================================================  
    $v_products = '';
    if ($v_showProducts == '1') {
      $dataproducts = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_genproducts", "prod_cat", $rcode, $faircode, $sectorCode, 'GP');
      if (isset($dataproducts)) {
        foreach ($dataproducts as $products) {
          $v_products = $v_products . $products['remarks_major'] . "->" . $products['remarks_minor'] . ", ";
          //if($products['remarks_minor']=="" ) {$v_products =$v_products.$products['remarks_major'].", "; }
          //else {$v_products =$v_products.$products['remarks_minor'].", "; }
        }
      }
    }
    //=================================================== 

    $v_specproducts = '';
    if ($v_showspecificProducts == '1') {
      $dataSpecProducts = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_product_requirement", "item_code", $rcode, $faircode, $sectorCode, 'GP_O');
      if (isset($dataSpecProducts)) {
        foreach ($dataSpecProducts as $Specproducts) {
          $v_specproducts = $v_specproducts . $Specproducts['remarks'];
        }
      }
    }
    //=================================================== 

    //======== check if active in BUSSMATCH_DATE ========
    $v_prodClass = '';
    if ($v_showProductClassification == '1') {
      $dataprodClass = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_genproducts_classification", "item_code", $rcode, $faircode, $sectorCode, 'PCLAS');
      if (isset($dataprodClass)) {
        foreach ($dataprodClass as $prodClass) {
          $v_prodClass = $v_prodClass . $prodClass['c_profile'] . ", ";
        }
      }
    }
    //===================================================    

    $v_show_existing_arrangements = '';
    if ($v_show_existing_arrangements_with_philippine_suppliers == '1') {
      $dataExisting_arrangements = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_existing_arrangement", "item_code", $rcode, $faircode, $sectorCode, 'SUP1');
      if (isset($dataExisting_arrangements)) {
        foreach ($dataExisting_arrangements as $dataExisting_arrange) {
          $v_show_existing_arrangements = $v_show_existing_arrangements . $dataExisting_arrange['c_profile'] . " - " . $dataExisting_arrange['remarks'];
        }
      }
    }
    //===================================================      
    //======== check if active in BUSSMATCH_DATE ========
    $v_Factor = '';
    if ($v_showImportantFactor == '1') {
      $dataFactor = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_importantfactor", "item_code", $rcode, $faircode, $sectorCode, 'B10');
      if (isset($dataFactor)) {
        foreach ($dataFactor as $Factor) {
          $v_Factor = $v_Factor . $Factor['c_profile'] . ", ";
        }
      }
    }
    //=================================================== 
    $v_workshop = '';
    if ($v_showWorkshop == '1') {
      $dataFactor = $this->site_read_model->getJoinRec(MASTER_DB . ".v_reference", "v_fair_tracks", "item_code", $rcode, $faircode, $sectorCode, 'WS1');
      if (isset($dataFactor)) {
        foreach ($dataFactor as $workshop) {
          $v_workshop = $v_workshop . $workshop['c_profile'] . ", ";
        }
      }
    }



    $vcss = "style='font-size: 11px; background-color: #CCC; color: #000;padding: 6px;text-align: left;border: 1px #fff solid; font-family: Verdana, Geneva, Arial, Helvetica, sans-serif ;'";

    foreach ($dataProfile as $rcode1) {
      $repcode = $rcode1['rep_code'];

      $emaildateandtime = date('l, F j, Y \a\t G:i:s');
      $txtmailbody = '';

      $txtmailbody = $txtmailbody . "<div style='font-family: Verdana, Geneva, Arial, Helvetica, sans-serif ;font-size: 11px;'>
	 Below is the result of your Pre-Registration form.  It was submitted by " .
        $txtemailfrom . "<br> on $emaildateandtime <br><br>
     Registered thru - " . $regThru . "<br> ---------------------------------------------------------------------------<br>	 
	 ";

      $txtmailbody = $txtmailbody . "</div><table border=0 >";



      if ($v_hotelvoucher != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">HOTEL VOUCHER : </td><td " . $vcss . ">" . $v_hotelvoucher . "</td></tr>";
      }

      if ($VisitorType != "") {
        if ($vib == "1") {
          $bclass = " -> " . $buyerClass;
        } else {
          $bclass = "";
        }
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">I am a : </td><td " . $vcss . ">" . $VisitorType . " <strong>" . $bclass . "</strong></td></tr>";
      }
      if ($rcode1['cont_per_fn'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">First_Name : </td><td " . $vcss . ">" . $rcode1['cont_per_fn'] . "</td></tr>";
      }
      if ($rcode1['cont_per_ln'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Last_Name : </td><td " . $vcss . ">" . $rcode1['cont_per_ln'] . "</td></tr>";
      }
      if ($rcode1['gender'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Gender : </td><td " . $vcss . ">" . $rcode1['gender'] . "</td></tr>";
      }
      if ($rcode1['nationality'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Nationality : </td><td " . $vcss . ">" . $rcode1['nationality'] . "</td></tr>";
      }
      if ($rcode1['birthdate'] != "0000-00-00") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Birthdate : </td><td " . $vcss . ">" . $rcode1['birthdate'] . "</td></tr>";
      }
      if ($rcode1['age_group'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Age_Group : </td><td " . $vcss . ">" . $rcode1['age_group'] . "</td></tr>";
      }
      if ($rcode1['position'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Designation : </td><td " . $vcss . ">" . $rcode1['position'] . "</td></tr>";
      }
      if ($rcode1['co_name'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Company : </td><td " . $vcss . ">" . $rcode1['co_name'] . "</td></tr>";
      }
      if ($rcode1['description'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Description of Company : </td><td " . $vcss . ">" . $rcode1['description'] . "</td></tr>";
      }
      if ($rcode1['add_st'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Address : </td><td " . $vcss . ">" . $rcode1['add_st'] . "</td></tr>";
      }
      if ($rcode1['add_city'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">City_state : </td><td " . $vcss . ">" . $rcode1['add_city'] . "</td></tr>";
      }
      if ($rcode1['zipcode'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Zip_Code : </td><td " . $vcss . ">" . $rcode1['zipcode'] . "</td></tr>";
      }
      if ($rcode1['country'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Country : </td><td " . $vcss . ">" . $rcode1['country'] . "</td></tr>";
      }
      if ($rcode1['tel_off'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Phone : </td><td " . $vcss . ">" . $rcode1['tel_off'] . "</td></tr>";
      }
      if ($rcode1['mobile'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Mobile : </td><td " . $vcss . ">" . $rcode1['mobile'] . "</td></tr>";
      }
      // if ($rcode1['fax'] != "") {
      //   $txtmailbody = $txtmailbody. "<tr><td ".$vcss.">Fax : </td><td ".$vcss.">".$rcode1['fax']."</td></tr>" ;
      //    }
      if ($rcode1['email'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Email : </td><td " . $vcss . ">" . $rcode1['email'] . "</td></tr>";
      }
      if ($rcode1['email2'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Company Email : </td><td " . $vcss . ">" . $rcode1['email2'] . "</td></tr>";
      }
      if ($rcode1['webpage'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Website : </td><td " . $vcss . ">" . $rcode1['webpage'] . "</td></tr>";
      }

      if ($rcode1['y_estab'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Year Established : </td><td " . $vcss . ">" . $rcode1['y_estab'] . "</td></tr>";
      }
      if ($rcode1['facebook'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Facebook : </td><td " . $vcss . ">https://www.facebook.com/" . $rcode1['facebook'] . "</td></tr>";
      }
      if ($rcode1['linkedin'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">LinkedIn : </td><td " . $vcss . ">https://www.linkedin.com/in/" . $rcode1['linkedin'] . "</td></tr>";
      }
      if ($rcode1['blogsite'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Blog Site : </td><td " . $vcss . ">" . $rcode1['blogsite'] . "</td></tr>";
      }
      if ($rcode1['twitter'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Twitter : </td><td " . $vcss . ">https://www.twitter.com/" . $rcode1['twitter'] . "</td></tr>";
      }
      if ($rcode1['instagram'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Instagram : </td><td " . $vcss . ">https://www.instagram.com/" . $rcode1['instagram'] . "</td></tr>";
      }
      if ($rcode1['pinterest'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Pinterest : </td><td " . $vcss . ">http://www.pinterest.com/" . $rcode1['pinterest'] . "</td></tr>";
      }
      if ($rcode1['weibo'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Weibo : </td><td " . $vcss . ">http://www.weibo.com/" . $rcode1['weibo'] . "</td></tr>";
      }



      $txtmailbody = $txtmailbody . "</table><br>";
      $txtmailbody = $txtmailbody . "<table border=0>";


      // === Job function ===================
      if ($jobfunction != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Role in company's purchasing activities:</td><td " . $vcss . ">" . $jobfunction . "</td></tr>";
      }

      if ($v_org_assoc != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . "> Type of Organization: </td><td " . $vcss . ">" . $v_org_assoc . "</td></tr>";
      }

      if ($v_repre != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Nature of Business: </td><td " . $vcss . ">" . $v_repre . "</td></tr>";
      }

      if ($v_marketsegment != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">I cover the following market segments: </td><td " . $vcss . ">" . $v_marketsegment . "</td></tr>";
      }
      //$txtmailbody = $txtmailbody."</table><br>";
      //======================================	
      if ($v_showFrequentTravelToAsia == '1' && $rcode1['first_time_asia'] != "") {
        $ftTravel = "Yes";
        if ($rcode1['first_time_asia'] == "REGULAR") {
          $ftTravel = "No";
        }
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Is this your first time to travel to Asia to conduct business :</td><td " . $vcss . ">" . $ftTravel . "</td></tr>";
      }
      if ($v_1stTime == '1' && $VisitorStatus != "") {
        $ftVisit = "Yes";
        $lastAttend = "";
        if ($VisitorStatus == "REGULAR") {
          $ftVisit = "No";
          $lastAttend = ($VisitorRemarks <> "") ? ", last time attended, " . $VisitorRemarks : "";
        }
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Is this your first time to visit the show:</td><td " . $vcss . ">" . $ftVisit . $lastAttend . "</td></tr>";
      }
      // $txtmailbody = $txtmailbody."<table border=1>";
      // === Reason/s for visiting ===================
      if ($v_Reason != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Participation Goals:</td><td " . $vcss . ">" . $v_Reason . "</td></tr>";
        //Reason/s for visiting the Trade Event
      }
      if ($v_informthru != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">You learned about the event through:</td><td " . $vcss . ">" . $v_informthru . "</td></tr>";
      }
      if ($v_showsimportant != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">What are other trade shows you consider important to your business:</td><td " . $vcss . ">" . $v_showsimportant . "</td></tr>";
      }
      if ($v_products != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Sector/Product of Interest:</td><td " . $vcss . ">" . $v_products . "</td></tr>";
      }
      if ($v_specproducts != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Other Product Requirements:</td><td " . $vcss . ">" . $v_specproducts . "</td></tr>";
      }

      if ($v_annualsales != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Company's annual sales volume: </td><td " . $vcss . ">" . $v_annualsales . "</td></tr>";
      }
      if ($v_show_existing_arrangements != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Existing Arrangements with Philippine Suppliers: </td><td " . $vcss . ">" . $v_show_existing_arrangements . "</td></tr>";
      }

      if ($v_show_list_of_clients != "" && $rcode1['list_of_clients'] != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">List of Clients (*DISTRIBUTION NETWORK): </td><td " . $vcss . ">" . $rcode1['list_of_clients'] . "</td></tr>";
      }

      if ($v_prodClass != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Product Classification:</td><td " . $vcss . ">" . $v_prodClass . "</td></tr>";
      }
      if ($v_Factor != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Aside from Price, Quality and Design, what is the next most important factor you consider before purchasing:</td><td " . $vcss . ">" . $v_Factor . "</td></tr>";
      }
      if ($v_show_activity != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Which Activity would you Like to Join  :</td><td " . $vcss . ">" . $v_show_activity . "</td></tr>";
      }
      if ($vshow_meeting != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Interested in Pre-Arranged Business Meetings:</td><td " . $vcss . ">" . $vshow_meeting . "</td></tr>";
      }

      if ($rcode1['specific_request'] != "") {          // $v_show_require_an_interpreter
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Do you Require an Interpreter: </td><td " . $vcss . ">" . $rcode1['specific_request'] . "</td></tr>";
      }

      if ($v_workshop != "") {
        $txtmailbody = $txtmailbody . "<tr><td " . $vcss . ">Workshop or Seminar to Attend:</td><td " . $vcss . ">" . $v_workshop . "</td></tr>";
      }



      $txtmailbody = $txtmailbody . "</table><br>";
    } // end foreach   


    return $txtmailbody;
  }
}

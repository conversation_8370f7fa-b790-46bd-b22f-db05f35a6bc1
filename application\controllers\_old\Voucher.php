<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
use Ngekoding\CodeIgniterDataTables\DataTables;
require_once FCPATH.'vendor/autoload.php';
class Voucher extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
        $this->load->model('voucher_model');
	}

//========================================================================

	public function setup(){
		$set_data                  = $this->session->all_userdata();
		$data                      = sessionRights($set_data);
		$fair_code                 = $set_data['sessionData']['fcode'];
		$data['unused']            = $this->voucher_model->get_unused();
		$data['unassigned']        = $this->voucher_model->get_unassigned();
		$data['fair_voucher_used'] = $this->voucher_model->get_used_by_faircode($fair_code);
		$data['available']         = $this->voucher_model->get_available_by_faircode($fair_code);
		// diagnostics($data);
		// exit();
       	if($_SERVER['REQUEST_METHOD'] === 'POST'){
			$this->form_validation->set_error_delimiters('<div class="text-red">', '</div>');
			$this->form_validation->set_rules('no_of_code', 'Number of Code', 'required');

			if($this->form_validation->run() != FALSE){
				//UPDATE FILE ASSIGN AVAILABLE RECORD WITH FAIRCODE
				$affected_rows = $this->voucher_model->assign_codes_by_faircode($this->input->post('no_of_code'));
				if($affected_rows){
					$this->session->set_flashdata(array('msg'=>'Successfully added '.$affected_rows.' code/s', 'status_code'=>'success','assign_code'=>$fair_code));
					redirect('voucher/setup');
				}
				redirect('voucher/setup');
			}else{
				$this->load->view('vrs_voucher_setup',$data);
			}
       	}else{
       		$this->load->view('vrs_voucher_setup',$data);
       	}
	}
//========================================================================

	public function ajax_datatable(){
		$query = $this->voucher_model->dtbl_get_all();
		$datatables = new Datatables($query,'3');
		$datatables->asObject()->generate();
	}
//========================================================================

	public function reset(){
		if($this->input->post('fair_code')!=null AND $this->input->post('fair_code')!=''){
			$affected_rows = $this->voucher_model->reset_unused_by_faircode(trim($this->input->post('fair_code')));
			if($affected_rows){
				echo json_encode(['success'=>'200','msg'=>$affected_rows.' codes was reset.']);
			}else{
				echo json_encode(['success'=>'200','msg'=>'No unused '.$this->input->post('fair_code').' voucher codes.']);
			}
		}else{
			echo json_encode(['error'=>'Faircode field is required.']);
		}
	}


}

/* End of file Voucher.php */
/* Location: ./application/controllers/Voucher.php */
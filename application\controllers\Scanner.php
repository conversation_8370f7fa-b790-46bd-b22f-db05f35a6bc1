<?php

class Scanner extends CI_controller{

    public function __construct(){
        parent::__construct();          

        ini_set('memory_limit','256M');

        $this->load->library(array('form_validation','session','encryption'));          
        $this->load->helper(array('form', 'url', 'array', 'html', 'captcha')); 
        $this->load->model('Model_arrive');
        $this->load->model('master_model');
        $this->load->model('vrs_read_model');
        // $this->load->library('CustomLogger');
        // $this->customlogger->log_request_headers();    
   }    
  
  //   // public function index(): string{
  //   //    return view('v_scan');
  //   // }
    public function getVtype($strVtype){
      switch (strtoupper($strVtype)){
        case "TB":
          return "TRADE BUYER";
          break;
        case "GP":
          return "GENERAL PUBLIC";
          break;
        case "MEDIA":
          return "MEDIA";
          break;
        case "GUEST":
          return "GUEST";
          break;
        default:
          return $strVtype;
      }
    }
    //get profile qrcode used in compli pass di kasi nilalagay sa qr code ang details kapag complipass
    public function getProfile($scanValue){
      // print_r($scanValue);
      for($i=0;$i<=count($scanValue)-1;$i++){
        $zValue = explode(":",$scanValue[$i]);
        if ($zValue[0]=='JRCODE'){
          // echo $zValue[1];die();
          $profile = $this->vrs_read_model->joinTable("v_contact_profile as A","v_attendance as B","","A.rep_code = B.rep_code","A.rep_code='".$zValue[1]."' AND A.deleted= '0' and B.fair_code LIKE '".FAIR_CODE."%'","");
          if ($profile){
            $data['fn'] = trim($profile[0]['cont_per_fn']);
            $data['ln'] = trim($profile[0]['cont_per_ln']);
            $data['email'] = trim($profile[0]['email']);
            $data['org'] = trim($profile[0]['co_name']); 
            $data['ctry'] = trim($profile[0]['country']);
            $data['vrs'] = 'compli';
            $data['vstatus'] = trim($profile[0]['visitor_status']);
            $data['vtype'] = trim($profile[0]['visitor_type']);
            $data['rcode'] = trim($zValue[1]); 
            $data['country'] = trim($profile[0]['country']);
            $data['mobile'] = trim($profile[0]['mobile']);
            $data['title'] = trim($profile[0]['position']);
            $data['salutation'] = trim($profile[0]['salutation']);
            $data['buss'] = '';
          }
          return $data;
        }
      }
    }
    

    public function explodeQR($strQR){
      $scanValue = explode("|",$strQR);
      //remove na kasi nilagay na ni sir jun ang details sa qrcode
      // if(array_search('JVRS:compli',$scanValue)){
      //   $data = $this->getProfile($scanValue);
      // }else{

        if(strtoupper($scanValue[0])=="BEGIN:VCARD"){
          for($i=0;$i<=count($scanValue)-1;$i++){
            $zValue = explode(":",$scanValue[$i]);
            switch (strtoupper($zValue[0])){
              case "JFN":
                $data['fn'] = trim($zValue[1]); 
                break;
              case "JN":
                $data['ln'] = trim($zValue[1]);
                break;
              case 'JEMAIL':
                  $data['email'] = trim($zValue[1]);
                break;
              case 'JORG':
                  $data['org'] = trim($zValue[1]);
                break;
              case 'JADR':
                  $data['ctry'] = trim($zValue[1]);
                break;
              case 'JVRS':
                  $data['vrs'] = trim($zValue[1]); 
                break;
              case 'JSTATUS':
                  $data['vstatus'] = trim($zValue[1]);    
                break;
              case 'JVTYPE':
                  $data['vtype'] = $this->getVtype(trim($zValue[1])); 
                break;  
              case 'JRCODE':
                  $data['rcode'] = trim($zValue[1]); 
                break;  
              case 'JCOMPANY':
                break;
              case 'JCOUNTRY':
                $data['country'] = trim($zValue[1]);
                break;
              case 'JTEL':
                  $data['mobile'] = trim($zValue[1]);
                break;
              case 'JTITLE':
                  $data['title'] = trim($zValue[1]); 
                break;  
              case 'JPREFIX':
                  $data['salutation'] = trim($zValue[1]);
                break;      
              case 'JBUSINESS':
                  $data['buss'] = trim($zValue[1]);
                break;    
            }
          }
        }else{
          $data['result'] = $scanValue[0];
        }
      // }
      return $data;
    }
    public function c_scanme1(){
      echo "test1";
    }

    

    // function to update v_attendance and insert to reg_tbl_arrived to monitor all scanned qr
        /*SAMPLE QR
        BEGIN:VCARD|JFN:|JN:|JEMAIL:|JORG:|JADR:|JVRS:compli|JSTATUS:|JVTYPE:GUEST|JRCODE:145183|JEND:VCARD||
       [fn] => Bong
        [ln] => Madrid
        [email] => <EMAIL>
        [ctry] => Philippines
        [comp] => CITEM
        [qrFrmVRS] => FPLUS
        [vstatus] => NEW
        [vtype] => GUEST
        [rcode] => 6335
        */
    public function c_scanMe(){
      $vbarcode = $this->input->post('vbarcode');
      // $vbarcode = "BEGIN:VCARD|JFN:Bong|JN:Madrid|JEMAIL:<EMAIL>|JORG:CITEM|JADR:Philippines|JVRS:onsite|JSTATUS:NEW|JVTYPE:TB|JRCODE:158773|JEND:VCARD";
      $data = $this->explodeQR($vbarcode);
      $qryResult = $this->Model_arrive->call_mysql_update_attendance($data);
    }

    //function to determine papersize
    public function c_get_paper_size($vtype){
      $bwidth = "";
      $set_data = $this->session->all_userdata();
      // echo "<pre>";
      // print_r($set_data);
      // echo $v_type;die();
      // die();

      for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {
        switch (strtoupper($vtype)){
          case 'VISITOR':
            if ($set_data['sessionData'][$x."r"] == "visitor_3x1") 
              { $bwidth = "3.5"; $bheight ="1.4"; $pagesized = array(88.9,35.56); $format = "visitor";}
            if ($set_data['sessionData'][$x."r"] == "visitor_3x2") 
              { $bwidth = "3.5"; $bheight ="2.5"; $pagesized = array(88.9,63.5);  $format = "visitor";}
            if ($set_data['sessionData'][$x."r"] == "visitor_3x5") 
              { $bwidth = "3.5"; $bheight ="5.0"; $pagesized = array(88.9,127); $format = "visitor";}
          break;
          case 'GUEST':
            if ($set_data['sessionData'][$x."r"] == "visitor_3x1") 
              { $bwidth = "3.5"; $bheight ="1.4"; $pagesized = array(88.9,35.56); $format = "visitor";}
            if ($set_data['sessionData'][$x."r"] == "visitor_3x2") 
              { $bwidth = "3.5"; $bheight ="2.5"; $pagesized = array(88.9,63.5);  $format = "visitor";}
            if ($set_data['sessionData'][$x."r"] == "visitor_3x5") 
              { $bwidth = "3.5"; $bheight ="5.0"; $pagesized = array(88.9,127); $format = "visitor";}
          break;
          case 'TRADE BUYER':
            if ($set_data['sessionData'][$x."r"] == "tb_3x1") 
              { $bwidth = "3.5"; $bheight ="1.4"; $pagesized = array(88.9,35.56); $format = "tb";}
            if ($set_data['sessionData'][$x."r"] == "tb_3x2") 
              { $bwidth = "3.5"; $bheight ="2.5"; $pagesized = array(88.9,63.5);  $format = "tb";}
            if ($set_data['sessionData'][$x."r"] == "tb_3x5") 
              { $bwidth = "3.5"; $bheight ="5.0"; $pagesized = array(88.9,127); $format = "tb";}
          break;
          case 'MEDIA':
            if ($set_data['sessionData'][$x."r"] == "media_3x1") 
              { $bwidth = "3.5"; $bheight ="1.4"; $pagesized = array(88.9,35.56); $format = "media";}
            if ($set_data['sessionData'][$x."r"] == "media_3x2") 
              { $bwidth = "3.5"; $bheight ="2.5"; $pagesized = array(88.9,63.5);  $format = "media";}
            if ($set_data['sessionData'][$x."r"] == "media_3x5") 
              { $bwidth = "3.5"; $bheight ="5.0"; $pagesized = array(88.9,127); $format = "media";}
          break;
            
        }
        if($bwidth!=""){ //check if my value to terminate loop
          $papersize = array ('bwidth'=>$bwidth,'bheight'=>$bheight,'pagesized'=>$pagesized,'format'=>$format);
          return $papersize;
        }
      }
      if ($bwidth==""){ //if width still is empty then use default
        // $pagesized = array(88.9,63.5);
        $papersize = array ('bwidth'=>'3.5','bheight'=>'2.5','pagesized'=>[88.9,63.5],'format'=>'visitor');
        return $papersize;
      }
    }
    //function to determine badge size (WALA MUNA TO)
    public function c_badge_size($vtype){
      switch (strtoupper($vtype)){
        case "TB" || "TRADE BUYER":
          $data['vwidth'] = "3.5"; 
          $data['vheight'] ="2.5";
          break;
        CASE "GUEST":

          break;
        default:
          break;
      }
    }
    function reload_session(){
      $set_data = $this->session->all_userdata();

       // print_r($set_data['sessionData']); die();
             
      if (isset($set_data['sessionData']) && isset($set_data['sessionData']['realname'])  && $set_data['sessionData']['realname'] != NULL) {
        $data['read_set_value'] = $set_data['sessionData']['realname'];
        $data['sessionRights'] = $set_data['sessionData']['urights'];
        $data['loginuser'] = $set_data['sessionData']['loginuser'];     
        
        $data['sysName'] = $set_data['sessionData']['sysName'];     
        $data['systitle'] = $set_data['sessionData']['systitle'];
        $data['eventDB'] = $set_data['sessionData']['eventDB'];

        $data['fcode'] = $set_data['sessionData']['fcode'];
        $data['sectorName'] = $set_data['sessionData']['sectorName'];
        $data['sector'] = $set_data['sessionData']['sector'];
        $data['fairdesc'] = $set_data['sessionData']['fdesc'];      
        $data['diyTerminal'] = $set_data['sessionData']['terminalNo'];

        $data['myVenue'] = $set_data['sessionData']['venue'];
        $data['myVenueNum'] = $set_data['sessionData']['venueNum'];

        $data['controller_CI'] = $set_data['sessionData']['controller_CI']; 
        $data['getDay'] = $set_data['sessionData']['getDay'];

        $data['eventProj'] = $set_data['sessionData']['eventProj'];
        $data['genericFcode'] = $set_data['sessionData']['genericFcode'];
        $data['sectorProjects'] = $set_data['sessionData']['sectorProjects'];

        $data['default_form_type'] = $set_data['sessionData']['default_form_type'];   
        

        $data['userAccessRef']= $this->master_model->loadRec("v_reference","where switch ='VRS1' and exclude=0 order by sortfield");
        $data['userAccessMod']= $this->master_model->loadRec("v_reference","where switch ='VMOD' and exclude=0 order by sortfield");
        // $data['userAccessRef'] =$set_data['sessionData']['userAccessRef'];
        // $data['userAccessMod'] =$set_data['sessionData']['userAccessMod'];
        // $data['project']= $this->master_model->getRec("busmatch_date","where fair_code = ?",strtoupper($data['fcode']),'');
        // $data['userAccessMod'] =$set_data['sessionData']['userAccessMod'];
      //   if ($data['project']=='') {die("invalid faircode.....");} 


      // //===========================================
      //       //======== get RIGHTS procedure =============
      //       //===========================================
            foreach ($data['userAccessRef'] as $r1)
            {
              
              $founds=0;                
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {
               
               $xxx = $set_data['sessionData'][$x."r"];               
                                                        // Using ${} is a way to create dynamic variables, ex.====> ${'vrs_'.$r1['c_code']} = "";
               if ($r1['c_code']==$xxx) { $data['vrs_'.$r1['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r1['c_code']] = ""; }
              //echo $data['vrs_'.$r1['c_code']]."<br>";

            }
            foreach ($data['userAccessMod'] as $r2)
            {
              
              $founds=0;                
              for ($x = 1; $x <= $set_data['sessionData']['numrights']; $x++) {
               
               $xxx = $set_data['sessionData'][$x."r"];               
               // Using ${} is a way to create dynamic variables,
               if ($r2['c_code']==$xxx) { $data['vrs_'.$r2['c_code']] = $xxx; $founds=1; }
               // ================================================================
              }
              if($founds==0) { $data['vrs_'.$r2['c_code']] = ""; }
              //echo $data['vrs_'.$r2['c_code']]."<br>";
            }

             if ($data['vrs_enable_scan'] =="") { redirect('vrs/index'); }    // === if no rights redirect to index

            if(isset($_GET['mess']) && $_GET['mess']<>"") {$data['messageX'] = $_GET['mess'];}

            if($data['vrs_diy_visitor_print']=="") {$data['disableVisitPrint']="";} else {$data['disableVisitPrint']="&autoprint=no";}
        return $data;
      }
    }
    
    // function to print scanned qr code
    public function c_print_sbadge(){
      if(isset($_POST['vbarcode']) && trim($_POST['vbarcode'])=="") {
        $data['messageX'] = "No QRcode Detected...";
        if(isset($_POST['buttonPressed']) && $_POST['buttonPressed']=="buttonPressed") {
          redirect("vrs/scanQR"); 
        } else {
          redirect("vrs/scanQR?mess=".$data['messageX']); 
        }
      } else {
        $vbarcode = $_POST['vbarcode'];
        // $vbarcode = 'BEGIN:VCARD|JFN:Bong|JN:Madrid|JEMAIL:<EMAIL>|JORG:CITEM|JADR:Philippines|JVRS:onsite|JSTATUS:NEW|JVTYPE:TB|JRCODE:158773|JEND:VCARD|';
        $data = $this->explodeQR($vbarcode);
        // echo "<pre>";
        // print_r($data);die();
        $pageSetup = $this->c_get_paper_size($data['vtype']);
        $disable_qrCode = $this->searchSessionValue('disable_qr');
        $disable_barCode = $this->searchSessionValue('disable_bc');
        $disable_gpPay = $this->searchSessionValue('disable_gp');
        $pageSetup += array('disable_qrCode'=>$disable_qrCode,'disable_barCode'=>$disable_barCode,'disable_gpPay'=>$disable_gpPay);
        
        if(!isset($data['rcode']) || $data['rcode']=="" || $data['rcode']==null) {
          $data['messageX'] = "No Ref Code, Please Scan again... :( ";
          redirect("vrs/scanQR?mess=".$data['messageX']); 
        }else{
          $qrcodes = $this->createQR($data,'YES'); //function createQR($data,$buscard='NO')
          $filename = $this->printID($data,$pageSetup); //function printID($data)
          $print = $pageSetup+array('labelName'=>$filename,'printMe'=>$filename);
          $data = $this->reload_session();
          $data['mmenu'] = 'print';
          $data += $print;
          $data += array('vbarcode'=>$_POST['vbarcode']);
          // echo "<pre>";
          // print_r($data);
          // echo "</pre>";die();
          $this->load->view("vrs_scanqrcode",$data);
        }
      }

    }
    // Function to search for a value in session
    function searchSessionValue($value) {
      // echo "<pre>";
      // print_r($_SESSION);die();
      foreach ($_SESSION['sessionData'] as $key => $sessionData) {
        // print_r($sessionData);die();
          if (is_array($sessionData)) {
              if (array_search($value, $sessionData) !== false) {
               return "YES";
              }
          } else {
              if ($sessionData === $value) {
                  return "YES";
              }
          }
      }
      return "NO";
    }
    
    //functino to generate QRCode
    function createQR($data,$buscard='NO',$vrsPrereg='onsite',$fcode=FAIR_CODE){
      $this->load->library('infiQr');

      $tempDir = FCPATH."idbadge/";
      // print_r($data);die();
      $codeContents  = 'BEGIN:VCARD'."\n";            // "|J"
      $codeContents .= 'FN:'.($data['fn']??"")."\n";              //  .$ln."\n";
      $codeContents .= 'N:'.($data['ln']??"")."\n";
      $codeContents .= 'EMAIL:'.($data['email']??"")."\n";

      if($buscard=="YES") {
        $codeContents .= 'TEL:'.($data['mobile']??"")."\n";
        $codeContents .= 'TITLE:'.($data['title']??"")."\n";
      //$codeContents .= 'ADR:'.$ctry."\n";           //COUNTRY
        //$codeContents .= 'SALUTATION:'.$salutation."\n";    
      }  

        $codeContents .= 'ORG:'.($data['org']??"")."\n";             // ORG  
        $codeContents .= 'ADR:'.($data['ctry']??"")."\n";         
        $codeContents .= 'VRS:'.$vrsPrereg."\n";          // ===== indicates VRS created the QRcode ====
        $codeContents .= 'STATUS:'.($data['vstatus']??"")."\n";

        $codeContents .= 'VTYPE:'.($data['vtype']??"")."\n";  

        $codeContents .= 'RCODE:'.($data['rcode']??"")."\n";

        //$codeContents .= 'END:EESY_PROFILE';
        $codeContents .= 'END:VCARD';
        
        // generating
        //QRcode::png($codeContents, $tempDir.'025.png', QR_ECLEVEL_L, 3);
        QRcode::png($codeContents, $tempDir.$fcode."-".$data['rcode'].'.png', QR_ECLEVEL_L, 2);
       
        $qvalue = $tempDir.$fcode."-".$data['rcode'].'.png';

      return $qvalue; 
     }
    function getDay1(){
      $today = date("Y-m-d");
      if($today<=strtotime('2024-10-17')){
        return "DAY 1";
      }else if ($today<=strtotime('2024-10-17')){
        return "DAY 2";
      }else{
        return "DAY 3";
      }
    }

    public function getDay(){
      $vsector = $this->session->userdata('sessionData')['sector'];
      $chkDay = $this->master_model->getRec("v_reference","where switch='dayofuse' and exclude='0' and sector LIKE '%".$vsector."%' and c_code= ? limit 1",date('Y-m-d'),""); 
      if (isset($chkDay[0]) && is_array($chkDay[0]) && count($chkDay[0])) {
        return $chkDay[0]['c_profile'];
      }else{
        return 'DAY 0';
      }
    }

    function getAutoStretchFontSize($text, $maxWidth, $maxHeight, $fontFamily, $mpdf) {
          $fontSize = 100; // Start with a large font size

          // Use a loop to find the maximum font size that fits
          do {
              $mpdf->SetFont($fontFamily, '', $fontSize);
              $textWidth = $mpdf->GetStringWidth($text);
              $textHeight = $mpdf->FontSize; // mPDF's font size is set directly
              $fontSize--;
          } while ($textWidth > $maxWidth || $textHeight > $maxHeight);

          return $fontSize + 1; // Return the largest fitting font size
    }

    //function to generate ID PDF
    function printID($data,$pageSetup,$fcode=FAIR_CODE){
       require_once APPPATH .'/third_party/mpdf/vendor/autoload.php';

      $pdf = new \Mpdf\Mpdf();
      $text = strtoupper($data['fn']." ".$data['ln']."<br><strong>".$data['org']."</strong><br>".$data['ctry']); ;
      $maxWidth = 336+100; // Maximum width in mm
      $maxHeight = 264+100; // Maximum height in mm
      $fontFamily = 'Arial'; // Specify your font

      // Calculate the fitting font size
      $data['fontSize'] = $this->getAutoStretchFontSize($text, $maxWidth, $maxHeight, $fontFamily, $pdf);
      // echo "bong";
      $data += $pageSetup;
      $data['fcode'] = $fcode;
      $data['getDay'] = $this->getDay(); //temp lang ito kasi wala value yung session na getDay
      $dspHTML = $this->load->view('vrs_createid_new2', $data, true);
      // echo $dspHTML;die('test');
      $filename = $fcode."-".$data['rcode'];
      $pagesized = $pageSetup['pagesized'];
      $pdfFilePath = FCPATH."idbadge/$filename.pdf";
      
      $data['filename'] = $filename;
      $data["labelName"] = $filename;
      
      // echo $data['getDay'];die();
      // echo $dspHTML;die();




     


     
     

      // $topmargin = ($pageSetup['bheight']==5) ? 40 : 0; //orig
      $topmargin = ($pageSetup['bheight']==5) ? 30 : 0; 
      // echo "<pre>";
      // print_r($data);die();
      $pdf->addPage("P","","","","","0","0",$topmargin,"0","","","","","","","","","","","",$pagesized);
      $pdf->WriteHTML($dspHTML,2);
      $pdf->Output($pdfFilePath, "F");
      return $filename;
    }

  
}

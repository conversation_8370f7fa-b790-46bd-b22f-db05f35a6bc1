<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
use Ngekoding\CodeIgniterDataTables\DataTables;
require_once FCPATH.'vendor/autoload.php';
class Vdetails extends CI_Controller {


	public function __construct()
	{
		parent::__construct();
			ini_set('memory_limit','256M');
			//$this->load->library('session');		  
			//$this->load->helper('form');
			//$this->load->helper('url');
			//$this->load->helper('html');
			$this->load->helper('array');  // used by element() in function updateSurvey
			//$this->load->database();
			//$this->load->library('form_validation');
			//$this->load->library('pagination');
			//$this->load->library('encryption');
			//load the login model
			// $this->load->model('vrs_model');
			// $this->load->model('vrs_read_model');
			// $this->load->model('master_model'); 
			$this->load->model('vdetails_model'); 

	}

	public function index()
	{

		$set_data = $this->session->all_userdata(); //E5
     	$data = sessionRights($set_data);
     	$decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $this->input->get('d'));
       	$get = $this->encryption->decrypt($decryptX);
       	$data['tmp'] = explode('#',$get); 
       	$data['ViewHeader'] = $data['tmp'][3]; 
       	$data['asofDate'] = date('F d, Y h:i A');
       	// diagnostics($data['tmp']);
		$this->load->view('vrs_viewdetails',$data);
		
	}

//====================================================

	public function ajax_datatable(){
		$decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $this->input->get('d'));
       	$get = $this->encryption->decrypt($decryptX);
       	$data['fcode'] = $_SESSION["sessionData"]['fcode'];
       	$qryAdd = $this->_generate_qryAdd($get);
		$query = $this->vdetails_model->joinTable("v_contact_profile as A","v_attendance as B",
			"A.rep_code,A.barcode,A.co_name,A.email,A.email2,A.work_email,A.remarks,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,A.continent,A.region,A.pid,A.add_st,A.add_city,A.zipcode,A.email2,A.webpage,B.pre_reg,B.buyerclass,B.date_apply,B.visitor_type,B.validated,B.emailed,B.date_validated,B.sector as name,A.remarks,B.assigned_table",
			"A.rep_code = B.rep_code",$qryAdd." A.deleted= '0' and B.fair_code = '".$data['fcode']."'","");

		$datatables = new DataTables($query, '3');
		$datatables->addColumnAlias('A.rep_code', 'rep_code');
		$datatables->addColumnAlias('B.date_apply', 'date_apply');
		$datatables->addColumnAlias('B.pre_reg', 'pre_reg');

		$datatables->addSequenceNumber('row_number')
			->asObject()
			->generate();

	}

//====================================================

	private function _generate_qryAdd($get){
		$tmp = explode('#',$get);
		if(strtolower($tmp['3'])=='cumulative'){
			if(strtolower($tmp['0'])=='trade buyer'){
				return "B.visitor_type LIKE '%TRADE BUYER%' and B.reg_status='T' and ";
			}
			if(strtolower($tmp['0'])=='guest'){
				return "B.visitor_type LIKE '%GUEST%' and B.reg_status='T' and ";
			}
			if(strtolower($tmp['0'])=='general public'){
				return "B.visitor_type LIKE '%GENERAL PUBLIC%' and B.reg_status='T' and ";
			}
			if(strtolower($tmp['0'])=='media'){
				return "B.visitor_type LIKE '%MEDIA%' and B.reg_status='T' and ";
			}

		}
		if(strtolower($tmp['3'])=='pre-registered'){
			if(strtolower($tmp['0'])=='trade buyer'){
				return "B.visitor_type LIKE '%TRADE BUYER%' and B.pre_reg='P' and ";
			}
			if(strtolower($tmp['0'])=='guest'){
				return "B.visitor_type LIKE '%GUEST%' and B.pre_reg='P' and ";
			}
			if(strtolower($tmp['0'])=='general public'){
				return "B.visitor_type LIKE '%GENERAL PUBLIC%' and B.pre_reg='P' and ";
			}
			if(strtolower($tmp['0'])=='media'){
				return "B.visitor_type LIKE '%MEDIA%' and B.pre_reg='P' and ";
			}

		}

		//cummulative
		//media = B.visitor_type LIKE '%MEDIA%' and B.reg_status='T' and 
		//paying visitor = B.visitor_type LIKE '%GENERAL PUBLIC%' and B.reg_status='T' and 
		//non-trade = B.visitor_type LIKE '%GUEST%' and B.reg_status='T' and 
		// TB = B.visitor_type LIKE '%TRADE BUYER%' and B.reg_status='T' and 
		// 
		// pre reg
		//media = B.visitor_type LIKE '%MEDIA%' and B.pre_reg='P' and 
		//paying visitor = B.visitor_type LIKE '%GENERAL PUBLIC%' and B.pre_reg='P' and 
		//non-trade = B.visitor_type LIKE '%GUEST%' and B.pre_reg='P' and  
		// TB = B.visitor_type LIKE '%TRADE BUYER%' and B.pre_reg='P' and 
		// 

	}




}

/* End of file  */
/* Location: ./application/controllers/ */
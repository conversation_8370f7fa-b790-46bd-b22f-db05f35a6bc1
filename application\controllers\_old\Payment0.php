<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');



include_once (dirname(__FILE__) . "/Registration.php");



class Payment extends Registration{

	 public function __construct()

     {

          parent::__construct();



          ini_set('memory_limit','256M');

          $this->load->library(array('form_validation','session'));

          $this->load->helper(array('form', 'url', 'array', 'html', 'captcha'));

          $this->load->library('encryption');



          //load the login model



          $this->load->model('vrs_model');

          $this->load->model('master_model');

          $this->load->model('site_model');

          $this->load->model('site_read_model');



     }



    function paynow() {

    	//http://*************/event/payment/paynow?fcode=MFIO2019-VISITOR&barcode=37012998&amt=400&currency=PHP&pmode=CreditCard



    	//http://*************/event/payment/paynow?epay=5502b45b922e3832e62f9f7c3b13eb0a4d8ef6bf3d59ba38084c02b8b27f0483a6e54a91fa07177bac7c1ab2a056acf81ac71fccd5a83e2a2f2bfaed4107cc30F5GLxUPMBoJoVu54xQiLPb59zOp6XQYlxzmPzu1EBEJfGr2mHVczPDHAUhTHWMuZgnN0gCWPwdNWkUpumVxHIA^^



      // creditcard - e5c9332e71798dd95327f403d957930663e6522b410d72b894a7683f68aa4e65a48e3d8179224965f401fde4b4a0107969527e6410ddf6c54a792a74e8509d05v1q-xPuSclU5ti93ErPslnfsidGOXxqIZRFZIBnC6EZBE4_Dn8rws4mQvhmsYbblowZ93eyoWhYFb8g908VCCg^^



    	// dragonpay - 8450834a0f530ca0c3aaa2d85ddb72a062cf7304fdab0e5ad31ceda7f7312025459a913f3adae20061866ba44f244953c4e5d2363bfb3d78353627d9b8ed16670edBhrXqdFFPdwB-Fc_sHEolXYS1iFbSW9DI0B-wHkIRD3mstFtO-2pP2Y5tuOBKlGJfeVl8r3BRyEb02Mx_NQ^^



    	//$vlinkURL 	  = "MFIO2020#2029150#1#PHP#DragonPay";

    	//$TMP1 	  	  = $this->encryption->encrypt($vlinkURL);

     	//$_GET['epay'] = str_replace(array('+', '/', '='), array('-', '_', '^'), $TMP1);		die("encrypted - ".$_GET['epay']);





        if(isset($_GET['epay']) && trim($_GET['epay'])<>"" ) {}

        else { die("nada....No value0"); }



    	$decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $_GET['epay']);

        $valueX = $this->encryption->decrypt($decryptX);										//die("<br><br> decrypted - ".$valueX);



        $tempvar = str_replace('-', '', $valueX);												//die("<br><br> decrypted - ".$tempvar);

        if(!ctype_alnum(str_replace('#', '', $tempvar)))

          {

            die("illegal value(3x)..., please <NAME_EMAIL>");

          }



        //die("<br><br> decrypted - ".$tempvar);



        $getValue = explode("#",$valueX); //print_r($getValue);die();



    	//http_build_query()

		$repcode = $getValue[1];

		$faircode = $getValue[0];

	 	$amount = $getValue[2];

	 	$currency = $getValue[3];

	 	$paymentMode = $getValue[4];



	 	$desc = "Ticket";

	 	$valueWshop = $amount."#".$desc."#".$currency;

	 	$_POST['workshop'] = array($valueWshop);



	 	//print_r($_POST['workshop']); die();



	 	$_POST['prof'] = 0;

        $_POST['stud'] = 0;



	 	if(isset($repcode) && $repcode<>"" && isset($faircode) && $faircode<>"")

	 	{



	    	$data['profiles']= $this->site_read_model->getRec("v_contact_profile","where barcode = ? limit 1",$repcode,'');

	    	$data['project']= $this->master_model->getRec("busmatch_date","where fair_code = ?",$faircode,'');



	    	$v_disable_epayment = $this->check_status1($data['project'][0]["disable_epayment"],0);  //die($v_disable_epayment);



	    	if($data['project']=="") {die("Wrong val / No Proj");}

	    	if($data['profiles']=="") {die("Wrong val / Not Exist Profile");}

	    	if($v_disable_epayment == "1") {die("Wrong val / Module Disabled");}



	    	$faircode = str_replace('-VISITOR', '', $faircode);



        //die("wazup");



	    	//createPaymentOrder($vprofile,$vproject,$vamount,$amt_prof,$amt_stud,$servicetype,$paymentMode,$baseURL,$refcode);



	    	// ============ createPaymentOrder(), updatePayment() from Registration controller ============================================================================================

	    	//===========================================================================================================================================================

	    	$referenceCode = $this->createPaymentOrder($data['profiles'],$data['project'],$amount,"0","0",$data['project'][0]['description'],$paymentMode,"",$faircode);

	    	// ==========================================================================================================================================================



	    	//updatePayment($vtable,$rcode,$vbarcode,$vfcode,$vsec,$epayment,$showWorkshop,$referenceCode,$CreditCard,$ProjDescription,$email,$IPG)

	    	switch($paymentMode) {



              	  //======== DBP =================================

	              case "CreditCard":

	                $CreditCard="yes";



	                $item = "DBP";

	                $valueEpay = $item."#".$paymentMode;

	                $_POST['epayment'] = array($valueEpay);



	                $idnum = $this->updatePayment('v_payment_online',$repcode,$data['profiles'][0]['barcode'],$faircode,$data['project'][0]['sector'],'epayment','workshop',$referenceCode,'',$data['project'][0]['description'],$data['profiles'][0]['email'],$item);



	                break;

	              //======== dragonpay ==========================

	              case "DragonPay":

	                $CreditCard="no";



	                $item = "DragonPay";

	                $valueEpay = $item."#".$paymentMode;

	                $_POST['epayment'] = array($valueEpay);



	                $idnum = $this->updatePayment('v_payment_online',$repcode,$data['profiles'][0]['barcode'],$faircode,$data['project'][0]['sector'],'epayment','workshop',$referenceCode,'',$data['project'][0]['description'],$data['profiles'][0]['email'],$item);

	                break;



	              //======= PNB over the counter

	              default:

	                $CreditCard="NA";

	                $idnum = "";

	        }



      		$epayment = $CreditCard."#".$idnum."#".$referenceCode; //die($epayment);



  			//============ check value for epayment =====================================

            $CreditCard ="NA";



            //$v_disable_epayment = $this->check_status1($data['project'][0]["disable_epayment"],0);  //die($v_disable_epayment);

            if(isset($epayment) && trim($epayment)<>"" && $v_disable_epayment == "0") {





                //die("waaaz yes =".$epayment);



                $tempvar = str_replace('_', '', $epayment);		//die("aaa= ".str_replace('#', '', $tempvar));

                // if(!ctype_alnum(str_replace('#', '', $tempvar)))

                //   {

                //     die("illegal value(3x)..., please <NAME_EMAIL>");

                //   }

                $epay = explode("#",$epayment); //print_r($epay);die();

                $rspay = $this->site_read_model->getRec("v_payment_online","where refno = ?",$epay[1],'');

                $pnumber = $rspay[0]['referenceCode'];



                if($epay[0]=="yes") {$CreditCard = "yes";}

                if($epay[0]=="no") {$CreditCard = "no";}



            }

            else { die("Wrong val / Module Disabled_2");}



              //die("wazzz no=".$CreditCard);

              //============ Dragon Pay ===================================================



            if($CreditCard=="no")

              {

                //$environment = ENV_TEST;

                //$environment = ENV_LIVE;



                $errors = array();

                $is_link = false;



                $parameters = array(

                    'merchantid' => MERCHANT_ID,

                    'txnid' => $rspay[0]['referenceCode'],

                    'amount' => $rspay[0]['amount'],

                    'ccy' => $rspay[0]['currency'],

                    'description' => $rspay[0]['serviceType'],

                    'email' => $rspay[0]['email'],

                );



                //print_r($parameters); die("<br>aaa");



                $fields = array(

                    'txnid' => array(

                        'label' => 'Transaction ID',

                        'type' => 'text',

                        'attributes' => array(),

                        'filter' => FILTER_SANITIZE_STRING,

                        'filter_flags' => array(FILTER_FLAG_STRIP_LOW),

                    ),

                    'amount' => array(

                        'label' => 'Amount',

                        'type' => 'number',

                        'attributes' => array('step="0.01"'),

                        'filter' => FILTER_SANITIZE_NUMBER_FLOAT,

                        'filter_flags' => array(FILTER_FLAG_ALLOW_THOUSAND, FILTER_FLAG_ALLOW_FRACTION),

                    ),

                    'description' => array(

                        'label' => 'Description',

                        'type' => 'text',

                        'attributes' => array(),

                        'filter' => FILTER_SANITIZE_STRING,

                        'filter_flags' => array(FILTER_FLAG_STRIP_LOW),

                    ),

                    'email' => array(

                        'label' => 'Email',

                        'type' => 'email',

                        'attributes' => array(),

                        'filter' => FILTER_SANITIZE_EMAIL,

                        'filter_flags' => array(),

                    ),

                );





                  $_POST['submit'] = "Pay";



                  if (isset($_POST['submit'])) {





                    // Check for set values.

                    foreach ($fields as $key => $value) {

                      // Sanitize user input. However:

                      // NOTE: this is a sample, user's SHOULD NOT be inputting these values.

                      if (isset($_POST[$key])) {

                          $parameters[$key] = filter_input(INPUT_POST, $key, $value['filter'],

                            array_reduce($value['filter_flags'], function ($a, $b) { return $a | $b; }, 0));

                      }

                    }



                    // Validate values.

                    // Example, amount validation.

                    // Do not rely on browser validation as the client can manually send

                    // invalid values, or be using old browsers.

                    if (!is_numeric($parameters['amount'])) {

                      $errors[] = 'Amount should be a number.';

                    }

                    else if ($parameters['amount'] <= 0) {

                      $errors[] = 'Amount should be greater than 0.';

                    }



                    if (empty($errors)) {

                      // Transform amount to correct format. (2 decimal places,

                      // decimal separated by period, no thousands separator)

                      $parameters['amount'] = number_format($parameters['amount'], 2, '.', '');

                      // Unset later from parameter after digest.

                      $parameters['key'] = MERCHANT_PASSWORD;

                      $digest_string = implode(':', $parameters);

                      unset($parameters['key']);

                      // NOTE: To check for invalid digest errors,

                      // uncomment this to see the digest string generated for computation.

                      // var_dump($digest_string); $is_link = true;



                      $parameters['digest'] = sha1($digest_string);

                      $url = 'https://gw.dragonpay.ph/Pay.aspx?';

                      if (ENV_TEST == 1 ) {

                        $url = 'http://test.dragonpay.ph/Pay.aspx?';

                      }



                      $url .= http_build_query($parameters, '', '&');  //die($url);





                      if ($is_link) {

                        echo '<br><a href="' . $url . '">' . $url . '</a>';

                      }

                      else {

                        header("Location: $url");

                      }

                    }

                  }

                  else { die("No POST encountered(001)... please <NAME_EMAIL>"); }



              }

              //============ DBP Payment Gateway ==========================================

              //===========================================================================

            elseif($CreditCard=="yes")   //credit card was selected

              {

                // send email reply re payment

                //==============================



                //==============================

                // procedd to DBP online payment

                redirect(DBP_URL."terminalID=".TERMINAL_ID."&referenceCode=".$rspay[0]['referenceCode']."&amount=".$rspay[0]['amount']."&serviceType=".$rspay[0]['serviceType']."&securityToken=".$rspay[0]['securityToken_request']);

              }

            else

              {

              	die("nada....No value1");

              }

              //===========================================================================





	    	//die("yupp");



    	}

    	else 	//isset($repcode) && $repcode<>"" && isset($faircode) && $faircode<>""

    	{

    		// display message

    		die("nada....No value2");

    	}



    }





  	public function postback(){





  		//print_r($_POST); die();



  		if (isset($_POST['txnid'])) {



  			    //======  check digest_response========

  			    $secretkey = MERCHANT_PASSWORD;



  			    $parameters = array('txnid'=>$_POST['txnid'],

                'refno'=>$_POST['refno'],

                'status'=>$_POST['status'],

                'message'=>$_POST['message'],

                'secretkey'=>$secretkey );

            $digest_string = implode(":", $parameters);



            $digest = sha1($digest_string);

  			    //=====================================



            if($digest == $_POST['digest'])

            {

        		  	$this->vrs_model->updateAllRecord("v_payment_online","refno_dragon",$_POST['refno'],"referenceCode='".$_POST['txnid']."'");

        				$this->vrs_model->updateAllRecord("v_payment_online","message",$_POST['message'],"referenceCode='".$_POST['txnid']."'");

        				$this->vrs_model->updateAllRecord("v_payment_online","status",$_POST['status'],"referenceCode='".$_POST['txnid']."'");

        				$this->vrs_model->updateAllRecord("v_payment_online","digest_response",$_POST['digest'],"referenceCode='".$_POST['txnid']."'");

        				$this->vrs_model->updateAllRecord("v_payment_online","date_payment",date('Y-m-d H:i:s'),"referenceCode='".$_POST['txnid']."'");



        				//==== update v_payment_order =======

        				$this->vrs_model->updateAllRecord("v_payment_order","date_payment",date('Y-m-d H:i:s'),"item_code='".$_POST['txnid']."'");

        				//$this->vrs_model->updateAllRecord("v_payment_order","remarks","DragonPay","item_code='".$_POST['txnid']."'");



  				      switch($_POST['status']) {

          					case "S":

          	    				$this->vrs_model->updateAllRecord("v_payment_order","status","Paid","item_code='".$_POST['txnid']."'");

          			    		break;

          			    case "F":

          			    		$this->vrs_model->updateAllRecord("v_payment_order","status","Failed","item_code='".$_POST['txnid']."'");

          			    		break;

          			    case "P":

          			    		$this->vrs_model->updateAllRecord("v_payment_order","status","Pending","item_code='".$_POST['txnid']."'");

          			    		break;

          					case "U":

          			    		$this->vrs_model->updateAllRecord("v_payment_order","status","Unknown","item_code='".$_POST['txnid']."'");

          			    		break;

          			    case "R":

          			    		$this->vrs_model->updateAllRecord("v_payment_order","status","Refund","item_code='".$_POST['txnid']."'");

          			    		break;

          					case "K":

          			    		$this->vrs_model->updateAllRecord("v_payment_order","status","Chargeback","item_code='".$_POST['txnid']."'");

          			    		break;

          			    case "V":

          			    		$this->vrs_model->updateAllRecord("v_payment_order","status","Void","item_code='".$_POST['txnid']."'");

          			    		break;

          					case "A":

          			    		$this->vrs_model->updateAllRecord("v_payment_order","status","Authorized","item_code='".$_POST['txnid']."'");

          			    		break;

          			    default:

          			    		$this->vrs_model->updateAllRecord("v_payment_order","status","Invalid","item_code='".$_POST['txnid']."'");

      			    }



                // =========== send mail to cashier ==============================================

                // ===============================================================================

                if($_POST['status']=="S") {



                    $rsPayorder= $this->vrs_model->getRec("v_payment_order","where item_code = ?",$_POST['txnid'],'');

                    $data['project']= $this->master_model->getRec("busmatch_date","where fair_code = ?",$rsPayorder[0]['fair_code'],'');



                    if($data['project'][0]['sector']=="02")

                        {

                          $cashierEmail = $data['project'][0]['ty_email'];



                          $this->load->library('email');

                          $this->email->set_newline("\r\n");

                          $this->email->from($data['project'][0]['emailto_registration'],$data['project'][0]['description']); // change it to yours

                          $this->email->reply_to($data['project'][0]['emailto_registration']);

                          $this->email->to($cashierEmail);  // change it to yours ==== sendto

                          if(trim($data['project'][0]['emailto']) <> "")

                          {

                            $this->email->cc(trim($data['project'][0]['emailto']));

                          }

                          $this->email->subject("Entrance Payment - ".$data['project'][0]['description']);

                          //$this->email->set_custom_header("X-MC-Subaccount", $subAccount); // uses application/libraries/MY_Email.php

                          $urlMess= "<p><b>Payment was made.</b> Please proceed to  <a href='http://www.citem.com.ph/event/vrs' target='_blank'>http://www.citem.com.ph/event/vrs</a> and login to input O.R. at the payment module.</p>Thank you.";

                          $vcontact = "Cashier";

                          //messageReferer($fairdesc,$link,$vfrom,$bannerpic,$othinfo,$xsector,$refCode)

                          $message= $this->messageReferer($data['project'][0]['description'],$urlMess,"",$data['project'][0]['bannerpic'],$vcontact,$data['project'][0]['sector'],$_POST['txnid']);



                          $this->email->message($message);

                           if($this->email->send())

                             { $data['messageX'] = "Update Successful"; }

                           else

                             {

                               //show_error($this->email->print_debugger());

                               $data['emailErr'] = "Error in Sending Email... (s6)";

                             }

                        }

                }

                // ===============================================================================

			      }



		  }



  		$this->load->view('postback');



  	}



  	public function returnurl(){				//=============== dragon pay ===========================



  		if (isset($_GET['txnid'])) {



  			//======  check digest_response========

  			$secretkey = MERCHANT_PASSWORD;



  			$parameters = array('txnid'=>$_GET['txnid'],

                'refno'=>$_GET['refno'],

                'status'=>$_GET['status'],

                'message'=>$_GET['message'],

                'secretkey'=>$secretkey );

            $digest_string = implode(":", $parameters);



            //print_r($parameters); die();



            $digest = sha1($digest_string);

  			//=====================================



  			//die("aa= ".$digest."<br>bb= ".$_GET['digest']);



		  	//$this->vrs_model->updateAllRecord("v_payment_online","txnid",$_GET['txnid'],"referenceCode='".$_GET['txnid']."'");   	//======= uses referenceCode field ======





            if($digest == $_GET['digest'])

            {

	  			// $this->vrs_model->updateAllRecord("v_payment_online","refno_dragon",$_GET['refno'],"referenceCode='".$_GET['txnid']."'");

				// $this->vrs_model->updateAllRecord("v_payment_online","message",$_GET['message'],"referenceCode='".$_GET['txnid']."'");

				// $this->vrs_model->updateAllRecord("v_payment_online","status",$_GET['status'],"referenceCode='".$_GET['txnid']."'");

				// $this->vrs_model->updateAllRecord("v_payment_online","digest_response",$_GET['digest'],"referenceCode='".$_GET['txnid']."'");

				// $this->vrs_model->updateAllRecord("v_payment_online","date_payment",date('Y-m-d H:i:s'),"referenceCode='".$_GET['txnid']."'");



				//==== update v_payment_order =======

				//$this->vrs_model->updateAllRecord("v_payment_order","date_payment",date('Y-m-d H:i:s'),"item_code='".$_GET['txnid']."'");

				//$this->vrs_model->updateAllRecord("v_payment_order","remarks","DragonPay","item_code='".$_GET['txnid']."'");



          		$rsPay= $this->vrs_model->getRec("v_payment_online","where referenceCode = ?",$_GET['txnid'],"");



	  			switch($_GET['status']) {



					case "S":

			  			$data['vtitle'] = "Payment successful";



              				if($rsPay[0]['sector']=="20") {

						    	$data['mess1'] = "<p>Your delegate code will be sent to you in an e-mail. Our team will issue an official receipt to confirmed participants during the event proper.

	    						<p>Should you need an advance copy, kindly notify <NAME_EMAIL>.

	    						<p>Thank you and see you this September 22-23!";

                			}

              				elseif($rsPay[0]['sector']=="02")   {

                				$data['mess1'] = "<p>Your visitor code will be sent to your email. Please present the email sent upon claiming your entry badge. An official receipt may be issued to you during the event proper.

                  				<p>Should you need an advance copy, you may request <NAME_EMAIL>.

                  				<p>Thank you and see you this Manila FAME!";

                			}





			    		break;

			    	case "F":

			  			$data['vtitle'] = "Failure";

			    		$data['mess1'] = "";

			    		break;

			    	case "P":

			  			$data['vtitle'] = "Pending";

			    		$data['mess1'] = " For over-the-counter payments, please check your email for instructions.";

			    		break;

					case "U":

			  			$data['vtitle'] = "Unknown";

			    		$data['mess1'] = "";

			    		break;

			    	case "R":

			  			$data['vtitle'] = "Refund";

			    		$data['mess1'] = "";

			    		break;

					case "K":

			  			$data['vtitle'] = "Chargeback";

			    		$data['mess1'] = "";

			    		break;

			    	case "V":

			  			$data['vtitle'] = "Void";

			    		$data['mess1'] = "";

			    		break;

					case "A":

			  			$data['vtitle'] = "Authorized";

			    		$data['mess1'] = "";

			    		break;

			    	default:

			    		$data['vtitle'] = "Problem Encountered(1)";

	    				$data['mess1'] = "";

			    }

			}

			else

    		{

    			$data['vtitle'] = "Problem Encountered (Token1)";

    			$data['mess1'] = "No valid value..";

    		}

  		}

  		else

    	{

    		$data['vtitle'] = "Problem Encountered(2)";

    		$data['mess1'] = "No valid value..";

    	}



  		$this->load->view('returnurl',$data);



  	}



	public function success(){					// ===== DBP ================================



		if (isset($_GET['message'])) {



			$requestToken= $this->vrs_model->getRec("v_payment_online","where referenceCode = ?",$_GET['referenceCode'],'');

			$responseToken = sha1( $requestToken[0]["securityToken_request"] . "{" . TRANSACTON_KEY . "}" );



			//die("aa= ".$responseToken."<br>bb= ".$_GET['securityToken']);



			if($responseToken == $_GET['securityToken']) {



				$data['message'] = $_GET['message'];

				$this->vrs_model->updateAllRecord("v_payment_online","retrievalReferenceCode",$_GET['retrievalReferenceCode'],"referenceCode='".$_GET['referenceCode']."'");

				$this->vrs_model->updateAllRecord("v_payment_online","message",$_GET['message'],"referenceCode='".$_GET['referenceCode']."'");

				$this->vrs_model->updateAllRecord("v_payment_online","securityToken_response",$_GET['securityToken'],"referenceCode='".$_GET['referenceCode']."'");

				$this->vrs_model->updateAllRecord("v_payment_online","amount",$_GET['amount'],"referenceCode='".$_GET['referenceCode']."'");

				$this->vrs_model->updateAllRecord("v_payment_online","date_payment",date('Y-m-d H:i:s'),"referenceCode='".$_GET['referenceCode']."'");



				//==== update v_payment_order =======



				$this->vrs_model->updateAllRecord("v_payment_order","status","Paid","item_code='".$_GET['referenceCode']."'");

				$this->vrs_model->updateAllRecord("v_payment_order","date_payment",date('Y-m-d H:i:s'),"item_code='".$_GET['referenceCode']."'");

				$this->vrs_model->updateAllRecord("v_payment_order","remarks","CreditCard","item_code='".$_GET['referenceCode']."'");



		        $rsPay= $this->vrs_model->getRec("v_payment_online","where referenceCode = ?",$_GET['referenceCode'],"");



		        if($rsPay[0]['sector']=="20") {

		                $data['mess1'] = "<p>Your delegate code will be sent to you in an e-mail. Our team will issue an official receipt to confirmed participants during the event proper.

		                  <p>Should you need an advance copy, kindly notify <NAME_EMAIL>.

		                  <p>Thank you and see you this September 22-23!";

		                }

		        elseif($rsPay[0]['sector']=="02")   {

		                $data['mess1'] = "<p>Your visitor code will be sent to your email. Please present the email sent upon claiming your entry badge. An official receipt may be issued to you during the event proper.

		                  <p>Should you need an advance copy, you may request <NAME_EMAIL>.

		                  <p>Thank you and see you this Manila FAME!";

		        }



		        $data['vtitle'] = "Payment successful";



  			//==== send confirmation email ============================



				$rsPayorder= $this->vrs_model->getRec("v_payment_order","where item_code = ?",$_GET['referenceCode'],'');



				$data['project']= $this->master_model->getRec("busmatch_date","where fair_code = ?",$rsPayorder[0]['fair_code'],'');

 					//if ($data['project']=='') {die("Under Construction.....");}



				$data['profiles']= $this->vrs_model->getRec("v_contact_profile","where rep_code = ?",$rsPayorder[0]['rep_code'],'');

				//if ($data['profiles']=="") {die("repcode not found");}



        if($data['profiles']<>"" && $data['profiles']<>"" )

					{

  						foreach($data['profiles'] as $rcode)

  						 {

  						   	$barcode = $rcode['barcode'];

  						   	$visitorType = $rcode['visitor_type'];

  						  	$email = $rcode['email'];

  							  $ctry = $rcode['country'];

  						 }



  						      $data['vtype'] = $visitorType;



                    //====== MFAME Email to cashier ===============================================================

                    //=============================================================================================

                    if($data['project'][0]['sector']=="02")

                    {

                      $cashierEmail = $data['project'][0]['ty_email'];



                      $this->load->library('email');

                      $this->email->set_newline("\r\n");

                      $this->email->from($data['project'][0]['emailto_registration'],$data['project'][0]['description']); // change it to yours

                      $this->email->reply_to($data['project'][0]['emailto_registration']);

                      $this->email->to($cashierEmail);  // change it to yours ==== sendto

                      if(trim($data['project'][0]['emailto']) <> "")

                      {

                        $this->email->cc(trim($data['project'][0]['emailto']));

                      }

                      $this->email->subject("Entrance Payment - ".$data['project'][0]['description']);

                      //$this->email->set_custom_header("X-MC-Subaccount", $subAccount); // uses application/libraries/MY_Email.php

                      $urlMess= "<p><b>Payment was made.</b> Please proceed to  <a href='http://www.citem.com.ph/event/vrs' target='_blank'>http://www.citem.com.ph/event/vrs</a> and login to input O.R. at the payment module.</p>Thank you.";

                      $vcontact = "Cashier";

                      //messageReferer($fairdesc,$link,$vfrom,$bannerpic,$othinfo,$xsector,$refCode)

                      $message= $this->messageReferer($data['project'][0]['description'],$urlMess,"",$data['project'][0]['bannerpic'],$vcontact,$data['project'][0]['sector'],$_GET['referenceCode']);



                      $this->email->message($message);

                       if($this->email->send())

                         { $data['messageX'] = "Update Successful"; }

                       else

                         {

                           //show_error($this->email->print_debugger());

                           $data['emailErr'] = "Error in Sending Email... (s6)";

                         }

                    }



					          //====== check if for CREATE PHILS ============================================================

					          //=============================================================================================

					          if($data['project'][0]['sector']=="20")

					          {

					            $this->barcode($barcode,$rsPayorder[0]['fair_code']);

					            $vcontact = $data['profiles'][0]['cont_per_fn']." ".$data['profiles'][0]['cont_per_ln'];

					            $barcodeIMG = base_url('assets/images/barcode/barcode'.$rsPayorder[0]['fair_code'].'_'.$barcode.'.gif');

					            $urlMess = messageTXT($data['project'][0]['sector'],$barcodeIMG,$barcode);

				            	$message= $this->messageReferer($data['project'][0]['description'],$urlMess,"",$data['project'][0]['bannerpic'],$vcontact,$data['project'][0]['sector'],$_GET['referenceCode']);



				            	//die($message);



				                $this->load->library('email');

				                $this->email->set_newline("\r\n");

				                $this->email->from($data['project'][0]['emailto_registration'],$data['project'][0]['description']); // change it to yours

				                $this->email->reply_to($data['project'][0]['emailto_registration']);

				                $this->email->to($email);// change it to yours ==== sendto

								        if(trim($data['project'][0]['emailto']) <> "")

				                {

				               	 	$this->email->cc(trim($data['project'][0]['emailto']));

				            	  }

				                //$this->email->subject($data['project'][0]['description']." Masterclasses");

				                $this->email->subject($data['project'][0]['description']." Masterclasses");

				                //$this->email->set_custom_header("X-MC-Subaccount", $subAccount); // uses application/libraries/MY_Email.php

				                $this->email->message($message);

				                 if($this->email->send())

				                   { $data['messageX'] = "Update Successful"; }

				                 else

				                   {

				                     //show_error($this->email->print_debugger());

				                     $data['emailErr'] = "Error in Sending Email... (s6)";

				                   }



					          //====== for CREATE PHILS =====================================================================

					          //=============================================================================================

					          }



					}



    			//=========================================================



    		}

    		else

    		{

    			$data['vtitle'] = "Problem Encountered (Token2)";

    			$data['mess1'] = "No valid value..";

    		}

    	}

    	else

    	{

    		$data['vtitle'] = "Problem Encountered";

    		$data['mess1'] = "No valid value..";

    	}





		$this->load->view('success',$data);





	}

	public function failed(){



		if (isset($_GET['message'])) {

			$data['message'] = $_GET['message'];

			$this->vrs_model->updateAllRecord("v_payment_online","retrievalReferenceCode","","referenceCode='".$_GET['referenceCode']."'");

			$this->vrs_model->updateAllRecord("v_payment_online","message",$_GET['message'],"referenceCode='".$_GET['referenceCode']."'");

			$this->vrs_model->updateAllRecord("v_payment_online","securityToken_response",$_GET['securityToken'],"referenceCode='".$_GET['referenceCode']."'");

			$this->vrs_model->updateAllRecord("v_payment_online","date_payment",date('Y-m-d H:i:s'),"referenceCode='".$_GET['referenceCode']."'");



			$data['vtitle'] = "Payment Failed";

			$data['mess1'] = $data['message'];

		}

		else

		{

			$data['vtitle'] = "Problem Encountered";

    		$data['mess1'] = "No valid value..";

		}



		//==== update v_payment_order =======



		$this->load->view('failed',$data);

	}





	 ###### FOR BARCODE GENERATION  #####

	//USED LIBRARIES /libraries/ZEND FOLDER  & /libraries/ZEND.PHP

	function barcode($bcode,$fcode)

	{

	    //$visitor_id=$this->uri->segment(3);

	    //$faircode=$this->uri->segment(4);

	    $this->load->library('zend');

	    $this->zend->load('Zend/Barcode');



	      //Zend_Barcode::render('code39', 'image', array('text' => $visitor_id), array());

	      $barcode=Zend_Barcode::draw('code39', 'image', array('text' => $bcode,'barHeight'=>30, 'factor'=>2, 'fontSize'=>8,'drawText'=>FALSE), array());



	      /*$config = new Zend_Config(array(

	      'barcode'        => 'code39',

	        'barcodeParams'  => array('text' => $visitor_id, 'barHeight'=>30, 'factor'=>2),

	        'renderer'       => 'image',

	        'rendererParams' => array('imageType' => 'gif'),

	    	));

	    	$barcode = Zend_Barcode::factory($config);

	      */

	    imagegif($barcode, 'assets/images/barcode/barcode'.$fcode.'_'.$bcode.'.gif');

	    //echo $barcode."bong".$faircode.'_'.$visitor_id;

	    //file_put_contents('assets/images', $barcode);

	} //End of Barcode generation function





	public function messageReferer($fairdesc,$link,$vfrom,$bannerpic,$othinfo,$xsector,$refCode) {



      switch($xsector)

      {

        case "02":



          $fbook = "ManilaFAMEofficial";

          $tweet = "TheManilaFAME";

          $insta = "manilafame";

          break;

        case "20":

          $fbook = "createphilippines";

          $tweet = "createphils";

          $insta = "createphilippines";

          break;

        default:

          $fbook = "";

          $tweet = "";

          $insta = "";

      }



      $info = explode("~",$othinfo);



      $mess = "<html><head><title>".$fairdesc."</title>

           <style>

            .page-wrap {

              width: 800px;

              margin: 0 auto;

            }

            td {

             height: 101px;

             vertical-align: middle;

            }

            .round1 {

              border: 1px solid gray;

              border-radius: 10px;

              width: 720px;

              padding: 10px;

            }

            div {

              margin: 0px 50px 0px 50px;

            }

            a:link

            {

              color:#000000

            }



            .center {

                display: block;

                margin-left: auto;

                margin-right: auto;

                width: 50%;

            }



           </style>



           </head><body class='page-wrap'>



           <div class='round1'>

           <table cellpadding='0' cellspacing='0' border='0' width='600'>

           <tr>

              <td align='right' valign='top' ><img src='".$bannerpic."' height='187' alt='".$fairdesc."'></td>

           </tr>

           <tr>

            <td valign='top'><br><font size='2' face='Verdana, Arial, Helvetica, sans-serif'>Hi ".$info[0].",<br><br>".$link."<br><br><br><b>Ref # ".$refCode."</b><br><br></td>

       	   </tr>



       	   <tr>

            <td bgcolor='#ebecee' style='background-color:#ebecee; height:20px;'>

              <table border='0' cellpadding='0' cellspacing='0' align='left' style='width:50%; height:20px;'>

                <tr>

                  <td align='left' bgcolor='#ebecee' style='background-color:#ebecee; height:20px;'>

                    <a href='http://www.dti.gov.ph/' target='_blank'><img src='http://www.citem.com.ph/citemprojects/pix/foremail/dti.jpg' width='78' height='96' alt='DTI' border='0'></a>

                    <a href='http://www.citem.gov.ph/' target='_blank'><img src='http://www.citem.com.ph/citemprojects/pix/foremail/citem.jpg' width='70' height='96' alt='CITEM' border='0'></a>

                  </td>

                </tr>

              </table>

              <table border='0' cellpadding='0' cellspacing='3' align='right' style='width:50%;'>

                <tr>

                  <td align='right' bgcolor='#ebecee' style='background-color:#ebecee;'>

                    <a href='https://www.facebook.com/".$fbook."' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/facebook_new.png' border='0' alt='Facebook' /></a>

                    <a href='https://twitter.com/".$tweet."' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/twitter_new.png' border='0' alt='Twitter' /></a>

                    <a href='https://www.instagram.com/".$insta."' target='_blank'><img src='http://www.citem.com.ph/ecampaign/others/other/email_signature/instagram_new.png' border='0' alt='Instagram' /></a>

                  </td>

                </tr>

              </table>

            </td>

          </tr>

          <tr>

             <td style='height:20px; font-size:9px; font-family:Verdana, Arial, Helvetica, sans-serif;'>This email was send by ".$fairdesc." thru CITEM's Visitor Registration System.</td>

           </tr>

          </table>



          </div>

          </body></html>";



      return $mess;

 	}



 	public function messageTXT($sector,$url,$barcode) {



 	  switch($xsector)

      {

        case "02":



          $fbook = "ManilaFAMEofficial";

          $tweet = "TheManilaFAME";

          $insta = "manilafame";



          $urlMess = "";

          break;

        case "20":

          $fbook = "createphilippines";

          $tweet = "createphils";

          $insta = "createphilippines";



          $urlMess = "Welcome aboard, fellow creative mind! You just secured yourself a slot in the <strong>CREATE Philippines</strong> Masterclasses.

	              <p>

	              To learn fresh creative ideas and meet our top-caliber speakers, kindly present this confirmation e-mail at the Onsite Registration Counter together with your delegate code below:

	              </p>



	              <p style='text-align:center;'>

	              <img src='".$url."' height='60' alt='".$barcode."'><br>

	              ".$barcode."

	              </p>



	              <p>

	              Want to maximize your participation in <strong>CREATE Philippines</strong>? Join the different pocket events below for <strong>FREE</strong>:

	              </p>

	              <p>

	              <ul>

	              	<li><strong>Portfolio exhibition</strong> – a showcase of creative works of different artists, designers and creative studios including illustration, graphic design, motion graphics and many more.

	              	</li><br>

	              	<li><strong>Workshop</strong> – enjoy immersive and fun workshop with fellow creative heads courtesy of Crown Supply.</li><br>

	              	<li><strong>Seminar</strong> – Beyond the Box is conducting seminars during the event. Stay tuned for the topics!</li><br>

	              	<li><strong>Business-matching</strong> – meet potential creative entrepreneurs, artists and designers who can provide customized creative packages for your company. Sign up <a href='http://www.citem.com.ph/createphilippines/businessmatching.html' target='_blank'>here!</a>

	              	</li>

	              </ul>

	              </p>

	              <p>

	              Note: For <strong>students</strong>, kindly present your <strong>school ID</strong> at the registration counter for validation.

	              </p>

	              <p>

	              To stay updated on the latest about the event including speakers and masterclass schedules, follow or like our

	               <a href='https://www.facebook.com/createphilippines' target='_blank'>Facebook</a>,

				   <a href='https://twitter.com/CreatePHILS' target='_blank'>Twitter</a> and

				   <a href='https://www.instagram.com/createphilippines' target='_blank'>Instagram</a> accounts.

	              </p>

	              <p>

	              For any question or concern, you may reach us through our e-mail at <a href='mailto:<EMAIL>'><EMAIL></a> or call us at +63-2.832-5044.

	              </p>

	              <p>We are thrilled to share creative ideas with you!</p>

	              <br>

	              <p>Cheers,</p>

	              <br>

	              <p>

	              Your CREATE Philippines Team

	              </p>

	        ";



          break;

        default:

          $fbook = "";

          $tweet = "";

          $insta = "";



          $urlMess = "";

      }







      return $urlMess;

 	}

  function mayapaynow(){
    $sector = $this->input->get('sector') ?? "";
    $url = "https://paymaya.me/CITEM";
    switch ($sector){
      case "01":
        $close=true;
        if ($close!=true){
          $url = "https://paymaya.me/CITEM?amt=200";
        }else{
          // $getCPage = $this->master_model->loadRec("v_reference","where switch ='".$vconfirmationPage."' and exclude=0 AND sector like '%".$vproject[0]['sector']."%' order by sortfield LIMIT 1");
          $getCPage = $this->master_model->loadRec("v_reference","where switch ='template' and exclude=0 AND sector like '%01%' order by sortfield LIMIT 1");
          if($getCPage=="") {die("contact SMDD... template not defined");}

          $PageMess = $getCPage[0]['content_message'];

          $messTxt = "<b>ONLINE PAYMENT IS NOW CLOSED</b>.<br>All payments shall be settled <b>ONSITE</b> at <b>World Trade Center</b> payment counters on the day of visit within period of <b>26-28 May 2023.</b>";

          $mess0 = str_replace("{message}",$messTxt,$PageMess);

          echo $mess0;
        }
        break;
      case "02":
        $url = "https://paymaya.me/CITEM";
        break;

      default:
        redirect($url);
    }

  }



}

?>
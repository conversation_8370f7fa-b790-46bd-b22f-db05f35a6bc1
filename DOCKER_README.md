# CodeIgniter 3 Docker Staging Environment

This project includes a Docker setup that **exactly matches your production hosting environment** for staging and development.

## Production Environment Match

- **OS**: AlmaLinux v8.10.0 STANDARD
- **Apache Version**: 2.4.63
- **Database Version**: MySQL 8.0.42
- **Architecture**: x86_64
- **Operating System**: linux

## Prerequisites

- Docker
- Docker Compose

## Services

- **Web Server**: Apache 2.4.63 with PHP 7.4 on AlmaLinux 8.10 (accessible at http://localhost:8080)
- **Database**: MySQL 8.0.42 (accessible at localhost:3307)

## Getting Started

1. **Start the containers:**
   ```bash
   docker-compose up -d
   ```

2. **Stop the containers:**
   ```bash
   docker-compose down
   ```

3. **Rebuild containers (if you make changes to Dockerfile):**
   ```bash
   docker-compose up --build -d
   ```

## Database Configuration

The database is automatically configured with the following settings:
- **Host**: `db` (container name)
- **Database**: `ci3db`
- **Username**: `ciuser`
- **Password**: `cipass`
- **Root Password**: `root`

## Accessing the Application

- **Web Application**: http://localhost:8080
- **PHP Info**: http://localhost:8080/phpinfo.php (verify environment matches production)
- **Database** (from host): localhost:3307

## File Structure

```
docker/
├── apache/
│   ├── Dockerfile          # Web server configuration
│   └── 000-default.conf    # Apache virtual host configuration
└── docker-compose.yml      # Docker services configuration
```

## Troubleshooting

1. **Port conflicts**: If port 8080 or 3307 are already in use, modify the ports in `docker-compose.yml`
2. **Permission issues**: The containers run with proper permissions for www-data user
3. **Database connection**: Make sure the database container is running before the web container tries to connect

## Development

- Your CodeIgniter files are mounted as a volume, so changes are reflected immediately
- PHP extensions included: mysqli, pdo, pdo_mysql, mbstring, json, curl, xml
- Apache mod_rewrite is enabled for clean URLs
- Environment exactly matches production hosting for accurate staging

## Environment Verification

Visit http://localhost:8080/phpinfo.php to verify:
- OS: AlmaLinux 8.10
- Apache version: 2.4.63
- PHP version: 7.4.x
- MySQL connection capability
- All required PHP extensions
